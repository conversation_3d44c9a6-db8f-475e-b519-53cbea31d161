{"version": 3, "sources": ["../../three/examples/jsm/loaders/TGALoader.js", "../../three/examples/jsm/loaders/ColladaLoader.js"], "sourcesContent": ["import {\n\tDataTextureLoader,\n\tLinearMipmapLinearFilter\n} from 'three';\n\nclass TGALoader extends DataTextureLoader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t}\n\n\tparse( buffer ) {\n\n\t\t// reference from vthibault, https://github.com/vthibault/roBrowser/blob/master/src/Loaders/Targa.js\n\n\t\tfunction tgaCheckHeader( header ) {\n\n\t\t\tswitch ( header.image_type ) {\n\n\t\t\t\t// check indexed type\n\n\t\t\t\tcase TGA_TYPE_INDEXED:\n\t\t\t\tcase TGA_TYPE_RLE_INDEXED:\n\t\t\t\t\tif ( header.colormap_length > 256 || header.colormap_size !== 24 || header.colormap_type !== 1 ) {\n\n\t\t\t\t\t\tthrow new Error( 'THREE.TGALoader: Invalid type colormap data for indexed type.' );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\t\t// check colormap type\n\n\t\t\t\tcase TGA_TYPE_RGB:\n\t\t\t\tcase TGA_TYPE_GREY:\n\t\t\t\tcase TGA_TYPE_RLE_RGB:\n\t\t\t\tcase TGA_TYPE_RLE_GREY:\n\t\t\t\t\tif ( header.colormap_type ) {\n\n\t\t\t\t\t\tthrow new Error( 'THREE.TGALoader: Invalid type colormap data for colormap type.' );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\t\t// What the need of a file without data ?\n\n\t\t\t\tcase TGA_TYPE_NO_DATA:\n\t\t\t\t\tthrow new Error( 'THREE.TGALoader: No data.' );\n\n\t\t\t\t\t// Invalid type ?\n\n\t\t\t\tdefault:\n\t\t\t\t\tthrow new Error( 'THREE.TGALoader: Invalid type ' + header.image_type );\n\n\t\t\t}\n\n\t\t\t// check image width and height\n\n\t\t\tif ( header.width <= 0 || header.height <= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.TGALoader: Invalid image size.' );\n\n\t\t\t}\n\n\t\t\t// check image pixel size\n\n\t\t\tif ( header.pixel_size !== 8 && header.pixel_size !== 16 &&\n\t\t\t\theader.pixel_size !== 24 && header.pixel_size !== 32 ) {\n\n\t\t\t\tthrow new Error( 'THREE.TGALoader: Invalid pixel size ' + header.pixel_size );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// parse tga image buffer\n\n\t\tfunction tgaParse( use_rle, use_pal, header, offset, data ) {\n\n\t\t\tlet pixel_data,\n\t\t\t\tpalettes;\n\n\t\t\tconst pixel_size = header.pixel_size >> 3;\n\t\t\tconst pixel_total = header.width * header.height * pixel_size;\n\n\t\t\t // read palettes\n\n\t\t\t if ( use_pal ) {\n\n\t\t\t\t palettes = data.subarray( offset, offset += header.colormap_length * ( header.colormap_size >> 3 ) );\n\n\t\t\t }\n\n\t\t\t // read RLE\n\n\t\t\t if ( use_rle ) {\n\n\t\t\t\t pixel_data = new Uint8Array( pixel_total );\n\n\t\t\t\tlet c, count, i;\n\t\t\t\tlet shift = 0;\n\t\t\t\tconst pixels = new Uint8Array( pixel_size );\n\n\t\t\t\twhile ( shift < pixel_total ) {\n\n\t\t\t\t\tc = data[ offset ++ ];\n\t\t\t\t\tcount = ( c & 0x7f ) + 1;\n\n\t\t\t\t\t// RLE pixels\n\n\t\t\t\t\tif ( c & 0x80 ) {\n\n\t\t\t\t\t\t// bind pixel tmp array\n\n\t\t\t\t\t\tfor ( i = 0; i < pixel_size; ++ i ) {\n\n\t\t\t\t\t\t\tpixels[ i ] = data[ offset ++ ];\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// copy pixel array\n\n\t\t\t\t\t\tfor ( i = 0; i < count; ++ i ) {\n\n\t\t\t\t\t\t\tpixel_data.set( pixels, shift + i * pixel_size );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tshift += pixel_size * count;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// raw pixels\n\n\t\t\t\t\t\tcount *= pixel_size;\n\n\t\t\t\t\t\tfor ( i = 0; i < count; ++ i ) {\n\n\t\t\t\t\t\t\tpixel_data[ shift + i ] = data[ offset ++ ];\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tshift += count;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t } else {\n\n\t\t\t\t// raw pixels\n\n\t\t\t\tpixel_data = data.subarray(\n\t\t\t\t\t offset, offset += ( use_pal ? header.width * header.height : pixel_total )\n\t\t\t\t);\n\n\t\t\t }\n\n\t\t\t return {\n\t\t\t\tpixel_data: pixel_data,\n\t\t\t\tpalettes: palettes\n\t\t\t };\n\n\t\t}\n\n\t\tfunction tgaGetImageData8bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image, palettes ) {\n\n\t\t\tconst colormap = palettes;\n\t\t\tlet color, i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i ++ ) {\n\n\t\t\t\t\tcolor = image[ i ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = 255;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = colormap[ ( color * 3 ) + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = colormap[ ( color * 3 ) + 1 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = colormap[ ( color * 3 ) + 2 ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction tgaGetImageData16bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image ) {\n\n\t\t\tlet color, i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i += 2 ) {\n\n\t\t\t\t\tcolor = image[ i + 0 ] + ( image[ i + 1 ] << 8 );\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = ( color & 0x7C00 ) >> 7;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = ( color & 0x03E0 ) >> 2;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = ( color & 0x001F ) << 3;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = ( color & 0x8000 ) ? 0 : 255;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction tgaGetImageData24bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image ) {\n\n\t\t\tlet i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i += 3 ) {\n\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = 255;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = image[ i + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = image[ i + 1 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = image[ i + 2 ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction tgaGetImageData32bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image ) {\n\n\t\t\tlet i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i += 4 ) {\n\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = image[ i + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = image[ i + 1 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = image[ i + 2 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = image[ i + 3 ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction tgaGetImageDataGrey8bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image ) {\n\n\t\t\tlet color, i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i ++ ) {\n\n\t\t\t\t\tcolor = image[ i ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = color;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = color;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = color;\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = 255;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction tgaGetImageDataGrey16bits( imageData, y_start, y_step, y_end, x_start, x_step, x_end, image ) {\n\n\t\t\tlet i = 0, x, y;\n\t\t\tconst width = header.width;\n\n\t\t\tfor ( y = y_start; y !== y_end; y += y_step ) {\n\n\t\t\t\tfor ( x = x_start; x !== x_end; x += x_step, i += 2 ) {\n\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 0 ] = image[ i + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 1 ] = image[ i + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 2 ] = image[ i + 0 ];\n\t\t\t\t\timageData[ ( x + width * y ) * 4 + 3 ] = image[ i + 1 ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn imageData;\n\n\t\t}\n\n\t\tfunction getTgaRGBA( data, width, height, image, palette ) {\n\n\t\t\tlet x_start,\n\t\t\t\ty_start,\n\t\t\t\tx_step,\n\t\t\t\ty_step,\n\t\t\t\tx_end,\n\t\t\t\ty_end;\n\n\t\t\tswitch ( ( header.flags & TGA_ORIGIN_MASK ) >> TGA_ORIGIN_SHIFT ) {\n\n\t\t\t\tdefault:\n\t\t\t\tcase TGA_ORIGIN_UL:\n\t\t\t\t\tx_start = 0;\n\t\t\t\t\tx_step = 1;\n\t\t\t\t\tx_end = width;\n\t\t\t\t\ty_start = 0;\n\t\t\t\t\ty_step = 1;\n\t\t\t\t\ty_end = height;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase TGA_ORIGIN_BL:\n\t\t\t\t\tx_start = 0;\n\t\t\t\t\tx_step = 1;\n\t\t\t\t\tx_end = width;\n\t\t\t\t\ty_start = height - 1;\n\t\t\t\t\ty_step = - 1;\n\t\t\t\t\ty_end = - 1;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase TGA_ORIGIN_UR:\n\t\t\t\t\tx_start = width - 1;\n\t\t\t\t\tx_step = - 1;\n\t\t\t\t\tx_end = - 1;\n\t\t\t\t\ty_start = 0;\n\t\t\t\t\ty_step = 1;\n\t\t\t\t\ty_end = height;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase TGA_ORIGIN_BR:\n\t\t\t\t\tx_start = width - 1;\n\t\t\t\t\tx_step = - 1;\n\t\t\t\t\tx_end = - 1;\n\t\t\t\t\ty_start = height - 1;\n\t\t\t\t\ty_step = - 1;\n\t\t\t\t\ty_end = - 1;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( use_grey ) {\n\n\t\t\t\tswitch ( header.pixel_size ) {\n\n\t\t\t\t\tcase 8:\n\t\t\t\t\t\ttgaGetImageDataGrey8bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 16:\n\t\t\t\t\t\ttgaGetImageDataGrey16bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tthrow new Error( 'THREE.TGALoader: Format not supported.' );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tswitch ( header.pixel_size ) {\n\n\t\t\t\t\tcase 8:\n\t\t\t\t\t\ttgaGetImageData8bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image, palette );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 16:\n\t\t\t\t\t\ttgaGetImageData16bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 24:\n\t\t\t\t\t\ttgaGetImageData24bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 32:\n\t\t\t\t\t\ttgaGetImageData32bits( data, y_start, y_step, y_end, x_start, x_step, x_end, image );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tthrow new Error( 'THREE.TGALoader: Format not supported.' );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Load image data according to specific method\n\t\t\t// let func = 'tgaGetImageData' + (use_grey ? 'Grey' : '') + (header.pixel_size) + 'bits';\n\t\t\t// func(data, y_start, y_step, y_end, x_start, x_step, x_end, width, image, palette );\n\t\t\treturn data;\n\n\t\t}\n\n\t\t// TGA constants\n\n\t\tconst TGA_TYPE_NO_DATA = 0,\n\t\t\tTGA_TYPE_INDEXED = 1,\n\t\t\tTGA_TYPE_RGB = 2,\n\t\t\tTGA_TYPE_GREY = 3,\n\t\t\tTGA_TYPE_RLE_INDEXED = 9,\n\t\t\tTGA_TYPE_RLE_RGB = 10,\n\t\t\tTGA_TYPE_RLE_GREY = 11,\n\n\t\t\tTGA_ORIGIN_MASK = 0x30,\n\t\t\tTGA_ORIGIN_SHIFT = 0x04,\n\t\t\tTGA_ORIGIN_BL = 0x00,\n\t\t\tTGA_ORIGIN_BR = 0x01,\n\t\t\tTGA_ORIGIN_UL = 0x02,\n\t\t\tTGA_ORIGIN_UR = 0x03;\n\n\t\tif ( buffer.length < 19 ) throw new Error( 'THREE.TGALoader: Not enough data to contain header.' );\n\n\t\tlet offset = 0;\n\n\t\tconst content = new Uint8Array( buffer ),\n\t\t\theader = {\n\t\t\t\tid_length: content[ offset ++ ],\n\t\t\t\tcolormap_type: content[ offset ++ ],\n\t\t\t\timage_type: content[ offset ++ ],\n\t\t\t\tcolormap_index: content[ offset ++ ] | content[ offset ++ ] << 8,\n\t\t\t\tcolormap_length: content[ offset ++ ] | content[ offset ++ ] << 8,\n\t\t\t\tcolormap_size: content[ offset ++ ],\n\t\t\t\torigin: [\n\t\t\t\t\tcontent[ offset ++ ] | content[ offset ++ ] << 8,\n\t\t\t\t\tcontent[ offset ++ ] | content[ offset ++ ] << 8\n\t\t\t\t],\n\t\t\t\twidth: content[ offset ++ ] | content[ offset ++ ] << 8,\n\t\t\t\theight: content[ offset ++ ] | content[ offset ++ ] << 8,\n\t\t\t\tpixel_size: content[ offset ++ ],\n\t\t\t\tflags: content[ offset ++ ]\n\t\t\t};\n\n\t\t// check tga if it is valid format\n\n\t\ttgaCheckHeader( header );\n\n\t\tif ( header.id_length + offset > buffer.length ) {\n\n\t\t\tthrow new Error( 'THREE.TGALoader: No data.' );\n\n\t\t}\n\n\t\t// skip the needn't data\n\n\t\toffset += header.id_length;\n\n\t\t// get targa information about RLE compression and palette\n\n\t\tlet use_rle = false,\n\t\t\tuse_pal = false,\n\t\t\tuse_grey = false;\n\n\t\tswitch ( header.image_type ) {\n\n\t\t\tcase TGA_TYPE_RLE_INDEXED:\n\t\t\t\tuse_rle = true;\n\t\t\t\tuse_pal = true;\n\t\t\t\tbreak;\n\n\t\t\tcase TGA_TYPE_INDEXED:\n\t\t\t\tuse_pal = true;\n\t\t\t\tbreak;\n\n\t\t\tcase TGA_TYPE_RLE_RGB:\n\t\t\t\tuse_rle = true;\n\t\t\t\tbreak;\n\n\t\t\tcase TGA_TYPE_RGB:\n\t\t\t\tbreak;\n\n\t\t\tcase TGA_TYPE_RLE_GREY:\n\t\t\t\tuse_rle = true;\n\t\t\t\tuse_grey = true;\n\t\t\t\tbreak;\n\n\t\t\tcase TGA_TYPE_GREY:\n\t\t\t\tuse_grey = true;\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t//\n\n\t\tconst imageData = new Uint8Array( header.width * header.height * 4 );\n\t\tconst result = tgaParse( use_rle, use_pal, header, offset, content );\n\t\tgetTgaRGBA( imageData, header.width, header.height, result.pixel_data, result.palettes );\n\n\t\treturn {\n\n\t\t\tdata: imageData,\n\t\t\twidth: header.width,\n\t\t\theight: header.height,\n\t\t\tflipY: true,\n\t\t\tgenerateMipmaps: true,\n\t\t\tminFilter: LinearMipmapLinearFilter,\n\n\t\t};\n\n\t}\n\n}\n\nexport { TGALoader };\n", "import {\n\tAmbientLight,\n\tAnimationClip,\n\tBone,\n\tBufferGeometry,\n\tClampToEdgeWrapping,\n\tColor,\n\tDirectionalLight,\n\tDoubleSide,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tFrontSide,\n\tGroup,\n\tLine,\n\tLineBasicMaterial,\n\tLineSegments,\n\tLoader,\n\tLoaderUtils,\n\tMathUtils,\n\tMatrix4,\n\tMesh,\n\tMeshBasicMaterial,\n\tMeshLambertMaterial,\n\tMeshPhongMaterial,\n\tOrthographicCamera,\n\tPerspectiveCamera,\n\tPointLight,\n\tQuaternion,\n\tQuaternionKeyframeTrack,\n\tRepeatWrapping,\n\tScene,\n\tSkeleton,\n\tSkinnedMesh,\n\tSpotLight,\n\tTextureLoader,\n\tVector2,\n\tVector3,\n\tVectorKeyframeTrack,\n\tSRGBColorSpace\n} from 'three';\nimport { TGALoader } from '../loaders/TGALoader.js';\n\nclass ColladaLoader extends Loader {\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst path = ( scope.path === '' ) ? LoaderUtils.extractUrlBase( url ) : scope.path;\n\n\t\tconst loader = new FileLoader( scope.manager );\n\t\tloader.setPath( scope.path );\n\t\tloader.setRequestHeader( scope.requestHeader );\n\t\tloader.setWithCredentials( scope.withCredentials );\n\t\tloader.load( url, function ( text ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( text, path ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tparse( text, path ) {\n\n\t\tfunction getElementsByTagName( xml, name ) {\n\n\t\t\t// Non recursive xml.getElementsByTagName() ...\n\n\t\t\tconst array = [];\n\t\t\tconst childNodes = xml.childNodes;\n\n\t\t\tfor ( let i = 0, l = childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = childNodes[ i ];\n\n\t\t\t\tif ( child.nodeName === name ) {\n\n\t\t\t\t\tarray.push( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tfunction parseStrings( text ) {\n\n\t\t\tif ( text.length === 0 ) return [];\n\n\t\t\tconst parts = text.trim().split( /\\s+/ );\n\t\t\tconst array = new Array( parts.length );\n\n\t\t\tfor ( let i = 0, l = parts.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ] = parts[ i ];\n\n\t\t\t}\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tfunction parseFloats( text ) {\n\n\t\t\tif ( text.length === 0 ) return [];\n\n\t\t\tconst parts = text.trim().split( /\\s+/ );\n\t\t\tconst array = new Array( parts.length );\n\n\t\t\tfor ( let i = 0, l = parts.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ] = parseFloat( parts[ i ] );\n\n\t\t\t}\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tfunction parseInts( text ) {\n\n\t\t\tif ( text.length === 0 ) return [];\n\n\t\t\tconst parts = text.trim().split( /\\s+/ );\n\t\t\tconst array = new Array( parts.length );\n\n\t\t\tfor ( let i = 0, l = parts.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ] = parseInt( parts[ i ] );\n\n\t\t\t}\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tfunction parseId( text ) {\n\n\t\t\treturn text.substring( 1 );\n\n\t\t}\n\n\t\tfunction generateId() {\n\n\t\t\treturn 'three_default_' + ( count ++ );\n\n\t\t}\n\n\t\tfunction isEmpty( object ) {\n\n\t\t\treturn Object.keys( object ).length === 0;\n\n\t\t}\n\n\t\t// asset\n\n\t\tfunction parseAsset( xml ) {\n\n\t\t\treturn {\n\t\t\t\tunit: parseAssetUnit( getElementsByTagName( xml, 'unit' )[ 0 ] ),\n\t\t\t\tupAxis: parseAssetUpAxis( getElementsByTagName( xml, 'up_axis' )[ 0 ] )\n\t\t\t};\n\n\t\t}\n\n\t\tfunction parseAssetUnit( xml ) {\n\n\t\t\tif ( ( xml !== undefined ) && ( xml.hasAttribute( 'meter' ) === true ) ) {\n\n\t\t\t\treturn parseFloat( xml.getAttribute( 'meter' ) );\n\n\t\t\t} else {\n\n\t\t\t\treturn 1; // default 1 meter\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseAssetUpAxis( xml ) {\n\n\t\t\treturn xml !== undefined ? xml.textContent : 'Y_UP';\n\n\t\t}\n\n\t\t// library\n\n\t\tfunction parseLibrary( xml, libraryName, nodeName, parser ) {\n\n\t\t\tconst library = getElementsByTagName( xml, libraryName )[ 0 ];\n\n\t\t\tif ( library !== undefined ) {\n\n\t\t\t\tconst elements = getElementsByTagName( library, nodeName );\n\n\t\t\t\tfor ( let i = 0; i < elements.length; i ++ ) {\n\n\t\t\t\t\tparser( elements[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction buildLibrary( data, builder ) {\n\n\t\t\tfor ( const name in data ) {\n\n\t\t\t\tconst object = data[ name ];\n\t\t\t\tobject.build = builder( data[ name ] );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get\n\n\t\tfunction getBuild( data, builder ) {\n\n\t\t\tif ( data.build !== undefined ) return data.build;\n\n\t\t\tdata.build = builder( data );\n\n\t\t\treturn data.build;\n\n\t\t}\n\n\t\t// animation\n\n\t\tfunction parseAnimation( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tsources: {},\n\t\t\t\tsamplers: {},\n\t\t\t\tchannels: {}\n\t\t\t};\n\n\t\t\tlet hasChildren = false;\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tlet id;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'source':\n\t\t\t\t\t\tid = child.getAttribute( 'id' );\n\t\t\t\t\t\tdata.sources[ id ] = parseSource( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'sampler':\n\t\t\t\t\t\tid = child.getAttribute( 'id' );\n\t\t\t\t\t\tdata.samplers[ id ] = parseAnimationSampler( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'channel':\n\t\t\t\t\t\tid = child.getAttribute( 'target' );\n\t\t\t\t\t\tdata.channels[ id ] = parseAnimationChannel( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'animation':\n\t\t\t\t\t\t// hierarchy of related animations\n\t\t\t\t\t\tparseAnimation( child );\n\t\t\t\t\t\thasChildren = true;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tconsole.log( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( hasChildren === false ) {\n\n\t\t\t\t// since 'id' attributes can be optional, it's necessary to generate a UUID for unqiue assignment\n\n\t\t\t\tlibrary.animations[ xml.getAttribute( 'id' ) || MathUtils.generateUUID() ] = data;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseAnimationSampler( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tinputs: {},\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'input':\n\t\t\t\t\t\tconst id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tconst semantic = child.getAttribute( 'semantic' );\n\t\t\t\t\t\tdata.inputs[ semantic ] = id;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseAnimationChannel( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tconst target = xml.getAttribute( 'target' );\n\n\t\t\t// parsing SID Addressing Syntax\n\n\t\t\tlet parts = target.split( '/' );\n\n\t\t\tconst id = parts.shift();\n\t\t\tlet sid = parts.shift();\n\n\t\t\t// check selection syntax\n\n\t\t\tconst arraySyntax = ( sid.indexOf( '(' ) !== - 1 );\n\t\t\tconst memberSyntax = ( sid.indexOf( '.' ) !== - 1 );\n\n\t\t\tif ( memberSyntax ) {\n\n\t\t\t\t//  member selection access\n\n\t\t\t\tparts = sid.split( '.' );\n\t\t\t\tsid = parts.shift();\n\t\t\t\tdata.member = parts.shift();\n\n\t\t\t} else if ( arraySyntax ) {\n\n\t\t\t\t// array-access syntax. can be used to express fields in one-dimensional vectors or two-dimensional matrices.\n\n\t\t\t\tconst indices = sid.split( '(' );\n\t\t\t\tsid = indices.shift();\n\n\t\t\t\tfor ( let i = 0; i < indices.length; i ++ ) {\n\n\t\t\t\t\tindices[ i ] = parseInt( indices[ i ].replace( /\\)/, '' ) );\n\n\t\t\t\t}\n\n\t\t\t\tdata.indices = indices;\n\n\t\t\t}\n\n\t\t\tdata.id = id;\n\t\t\tdata.sid = sid;\n\n\t\t\tdata.arraySyntax = arraySyntax;\n\t\t\tdata.memberSyntax = memberSyntax;\n\n\t\t\tdata.sampler = parseId( xml.getAttribute( 'source' ) );\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildAnimation( data ) {\n\n\t\t\tconst tracks = [];\n\n\t\t\tconst channels = data.channels;\n\t\t\tconst samplers = data.samplers;\n\t\t\tconst sources = data.sources;\n\n\t\t\tfor ( const target in channels ) {\n\n\t\t\t\tif ( channels.hasOwnProperty( target ) ) {\n\n\t\t\t\t\tconst channel = channels[ target ];\n\t\t\t\t\tconst sampler = samplers[ channel.sampler ];\n\n\t\t\t\t\tconst inputId = sampler.inputs.INPUT;\n\t\t\t\t\tconst outputId = sampler.inputs.OUTPUT;\n\n\t\t\t\t\tconst inputSource = sources[ inputId ];\n\t\t\t\t\tconst outputSource = sources[ outputId ];\n\n\t\t\t\t\tconst animation = buildAnimationChannel( channel, inputSource, outputSource );\n\n\t\t\t\t\tcreateKeyframeTracks( animation, tracks );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn tracks;\n\n\t\t}\n\n\t\tfunction getAnimation( id ) {\n\n\t\t\treturn getBuild( library.animations[ id ], buildAnimation );\n\n\t\t}\n\n\t\tfunction buildAnimationChannel( channel, inputSource, outputSource ) {\n\n\t\t\tconst node = library.nodes[ channel.id ];\n\t\t\tconst object3D = getNode( node.id );\n\n\t\t\tconst transform = node.transforms[ channel.sid ];\n\t\t\tconst defaultMatrix = node.matrix.clone().transpose();\n\n\t\t\tlet time, stride;\n\t\t\tlet i, il, j, jl;\n\n\t\t\tconst data = {};\n\n\t\t\t// the collada spec allows the animation of data in various ways.\n\t\t\t// depending on the transform type (matrix, translate, rotate, scale), we execute different logic\n\n\t\t\tswitch ( transform ) {\n\n\t\t\t\tcase 'matrix':\n\n\t\t\t\t\tfor ( i = 0, il = inputSource.array.length; i < il; i ++ ) {\n\n\t\t\t\t\t\ttime = inputSource.array[ i ];\n\t\t\t\t\t\tstride = i * outputSource.stride;\n\n\t\t\t\t\t\tif ( data[ time ] === undefined ) data[ time ] = {};\n\n\t\t\t\t\t\tif ( channel.arraySyntax === true ) {\n\n\t\t\t\t\t\t\tconst value = outputSource.array[ stride ];\n\t\t\t\t\t\t\tconst index = channel.indices[ 0 ] + 4 * channel.indices[ 1 ];\n\n\t\t\t\t\t\t\tdata[ time ][ index ] = value;\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tfor ( j = 0, jl = outputSource.stride; j < jl; j ++ ) {\n\n\t\t\t\t\t\t\t\tdata[ time ][ j ] = outputSource.array[ stride + j ];\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'translate':\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'rotate':\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'scale':\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform );\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tconst keyframes = prepareAnimationData( data, defaultMatrix );\n\n\t\t\tconst animation = {\n\t\t\t\tname: object3D.uuid,\n\t\t\t\tkeyframes: keyframes\n\t\t\t};\n\n\t\t\treturn animation;\n\n\t\t}\n\n\t\tfunction prepareAnimationData( data, defaultMatrix ) {\n\n\t\t\tconst keyframes = [];\n\n\t\t\t// transfer data into a sortable array\n\n\t\t\tfor ( const time in data ) {\n\n\t\t\t\tkeyframes.push( { time: parseFloat( time ), value: data[ time ] } );\n\n\t\t\t}\n\n\t\t\t// ensure keyframes are sorted by time\n\n\t\t\tkeyframes.sort( ascending );\n\n\t\t\t// now we clean up all animation data, so we can use them for keyframe tracks\n\n\t\t\tfor ( let i = 0; i < 16; i ++ ) {\n\n\t\t\t\ttransformAnimationData( keyframes, i, defaultMatrix.elements[ i ] );\n\n\t\t\t}\n\n\t\t\treturn keyframes;\n\n\t\t\t// array sort function\n\n\t\t\tfunction ascending( a, b ) {\n\n\t\t\t\treturn a.time - b.time;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst position = new Vector3();\n\t\tconst scale = new Vector3();\n\t\tconst quaternion = new Quaternion();\n\n\t\tfunction createKeyframeTracks( animation, tracks ) {\n\n\t\t\tconst keyframes = animation.keyframes;\n\t\t\tconst name = animation.name;\n\n\t\t\tconst times = [];\n\t\t\tconst positionData = [];\n\t\t\tconst quaternionData = [];\n\t\t\tconst scaleData = [];\n\n\t\t\tfor ( let i = 0, l = keyframes.length; i < l; i ++ ) {\n\n\t\t\t\tconst keyframe = keyframes[ i ];\n\n\t\t\t\tconst time = keyframe.time;\n\t\t\t\tconst value = keyframe.value;\n\n\t\t\t\tmatrix.fromArray( value ).transpose();\n\t\t\t\tmatrix.decompose( position, quaternion, scale );\n\n\t\t\t\ttimes.push( time );\n\t\t\t\tpositionData.push( position.x, position.y, position.z );\n\t\t\t\tquaternionData.push( quaternion.x, quaternion.y, quaternion.z, quaternion.w );\n\t\t\t\tscaleData.push( scale.x, scale.y, scale.z );\n\n\t\t\t}\n\n\t\t\tif ( positionData.length > 0 ) tracks.push( new VectorKeyframeTrack( name + '.position', times, positionData ) );\n\t\t\tif ( quaternionData.length > 0 ) tracks.push( new QuaternionKeyframeTrack( name + '.quaternion', times, quaternionData ) );\n\t\t\tif ( scaleData.length > 0 ) tracks.push( new VectorKeyframeTrack( name + '.scale', times, scaleData ) );\n\n\t\t\treturn tracks;\n\n\t\t}\n\n\t\tfunction transformAnimationData( keyframes, property, defaultValue ) {\n\n\t\t\tlet keyframe;\n\n\t\t\tlet empty = true;\n\t\t\tlet i, l;\n\n\t\t\t// check, if values of a property are missing in our keyframes\n\n\t\t\tfor ( i = 0, l = keyframes.length; i < l; i ++ ) {\n\n\t\t\t\tkeyframe = keyframes[ i ];\n\n\t\t\t\tif ( keyframe.value[ property ] === undefined ) {\n\n\t\t\t\t\tkeyframe.value[ property ] = null; // mark as missing\n\n\t\t\t\t} else {\n\n\t\t\t\t\tempty = false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( empty === true ) {\n\n\t\t\t\t// no values at all, so we set a default value\n\n\t\t\t\tfor ( i = 0, l = keyframes.length; i < l; i ++ ) {\n\n\t\t\t\t\tkeyframe = keyframes[ i ];\n\n\t\t\t\t\tkeyframe.value[ property ] = defaultValue;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// filling gaps\n\n\t\t\t\tcreateMissingKeyframes( keyframes, property );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction createMissingKeyframes( keyframes, property ) {\n\n\t\t\tlet prev, next;\n\n\t\t\tfor ( let i = 0, l = keyframes.length; i < l; i ++ ) {\n\n\t\t\t\tconst keyframe = keyframes[ i ];\n\n\t\t\t\tif ( keyframe.value[ property ] === null ) {\n\n\t\t\t\t\tprev = getPrev( keyframes, i, property );\n\t\t\t\t\tnext = getNext( keyframes, i, property );\n\n\t\t\t\t\tif ( prev === null ) {\n\n\t\t\t\t\t\tkeyframe.value[ property ] = next.value[ property ];\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( next === null ) {\n\n\t\t\t\t\t\tkeyframe.value[ property ] = prev.value[ property ];\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tinterpolate( keyframe, prev, next, property );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction getPrev( keyframes, i, property ) {\n\n\t\t\twhile ( i >= 0 ) {\n\n\t\t\t\tconst keyframe = keyframes[ i ];\n\n\t\t\t\tif ( keyframe.value[ property ] !== null ) return keyframe;\n\n\t\t\t\ti --;\n\n\t\t\t}\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tfunction getNext( keyframes, i, property ) {\n\n\t\t\twhile ( i < keyframes.length ) {\n\n\t\t\t\tconst keyframe = keyframes[ i ];\n\n\t\t\t\tif ( keyframe.value[ property ] !== null ) return keyframe;\n\n\t\t\t\ti ++;\n\n\t\t\t}\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tfunction interpolate( key, prev, next, property ) {\n\n\t\t\tif ( ( next.time - prev.time ) === 0 ) {\n\n\t\t\t\tkey.value[ property ] = prev.value[ property ];\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t\tkey.value[ property ] = ( ( key.time - prev.time ) * ( next.value[ property ] - prev.value[ property ] ) / ( next.time - prev.time ) ) + prev.value[ property ];\n\n\t\t}\n\n\t\t// animation clips\n\n\t\tfunction parseAnimationClip( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'id' ) || 'default',\n\t\t\t\tstart: parseFloat( xml.getAttribute( 'start' ) || 0 ),\n\t\t\t\tend: parseFloat( xml.getAttribute( 'end' ) || 0 ),\n\t\t\t\tanimations: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'instance_animation':\n\t\t\t\t\t\tdata.animations.push( parseId( child.getAttribute( 'url' ) ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.clips[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction buildAnimationClip( data ) {\n\n\t\t\tconst tracks = [];\n\n\t\t\tconst name = data.name;\n\t\t\tconst duration = ( data.end - data.start ) || - 1;\n\t\t\tconst animations = data.animations;\n\n\t\t\tfor ( let i = 0, il = animations.length; i < il; i ++ ) {\n\n\t\t\t\tconst animationTracks = getAnimation( animations[ i ] );\n\n\t\t\t\tfor ( let j = 0, jl = animationTracks.length; j < jl; j ++ ) {\n\n\t\t\t\t\ttracks.push( animationTracks[ j ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new AnimationClip( name, duration, tracks );\n\n\t\t}\n\n\t\tfunction getAnimationClip( id ) {\n\n\t\t\treturn getBuild( library.clips[ id ], buildAnimationClip );\n\n\t\t}\n\n\t\t// controller\n\n\t\tfunction parseController( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'skin':\n\t\t\t\t\t\t// there is exactly one skin per controller\n\t\t\t\t\t\tdata.id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tdata.skin = parseSkin( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'morph':\n\t\t\t\t\t\tdata.id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Morph target animation not supported yet.' );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.controllers[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parseSkin( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tsources: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'bind_shape_matrix':\n\t\t\t\t\t\tdata.bindShapeMatrix = parseFloats( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'source':\n\t\t\t\t\t\tconst id = child.getAttribute( 'id' );\n\t\t\t\t\t\tdata.sources[ id ] = parseSource( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'joints':\n\t\t\t\t\t\tdata.joints = parseJoints( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'vertex_weights':\n\t\t\t\t\t\tdata.vertexWeights = parseVertexWeights( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseJoints( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tinputs: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'input':\n\t\t\t\t\t\tconst semantic = child.getAttribute( 'semantic' );\n\t\t\t\t\t\tconst id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tdata.inputs[ semantic ] = id;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseVertexWeights( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tinputs: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'input':\n\t\t\t\t\t\tconst semantic = child.getAttribute( 'semantic' );\n\t\t\t\t\t\tconst id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tconst offset = parseInt( child.getAttribute( 'offset' ) );\n\t\t\t\t\t\tdata.inputs[ semantic ] = { id: id, offset: offset };\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'vcount':\n\t\t\t\t\t\tdata.vcount = parseInts( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'v':\n\t\t\t\t\t\tdata.v = parseInts( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildController( data ) {\n\n\t\t\tconst build = {\n\t\t\t\tid: data.id\n\t\t\t};\n\n\t\t\tconst geometry = library.geometries[ build.id ];\n\n\t\t\tif ( data.skin !== undefined ) {\n\n\t\t\t\tbuild.skin = buildSkin( data.skin );\n\n\t\t\t\t// we enhance the 'sources' property of the corresponding geometry with our skin data\n\n\t\t\t\tgeometry.sources.skinIndices = build.skin.indices;\n\t\t\t\tgeometry.sources.skinWeights = build.skin.weights;\n\n\t\t\t}\n\n\t\t\treturn build;\n\n\t\t}\n\n\t\tfunction buildSkin( data ) {\n\n\t\t\tconst BONE_LIMIT = 4;\n\n\t\t\tconst build = {\n\t\t\t\tjoints: [], // this must be an array to preserve the joint order\n\t\t\t\tindices: {\n\t\t\t\t\tarray: [],\n\t\t\t\t\tstride: BONE_LIMIT\n\t\t\t\t},\n\t\t\t\tweights: {\n\t\t\t\t\tarray: [],\n\t\t\t\t\tstride: BONE_LIMIT\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tconst sources = data.sources;\n\t\t\tconst vertexWeights = data.vertexWeights;\n\n\t\t\tconst vcount = vertexWeights.vcount;\n\t\t\tconst v = vertexWeights.v;\n\t\t\tconst jointOffset = vertexWeights.inputs.JOINT.offset;\n\t\t\tconst weightOffset = vertexWeights.inputs.WEIGHT.offset;\n\n\t\t\tconst jointSource = data.sources[ data.joints.inputs.JOINT ];\n\t\t\tconst inverseSource = data.sources[ data.joints.inputs.INV_BIND_MATRIX ];\n\n\t\t\tconst weights = sources[ vertexWeights.inputs.WEIGHT.id ].array;\n\t\t\tlet stride = 0;\n\n\t\t\tlet i, j, l;\n\n\t\t\t// process skin data for each vertex\n\n\t\t\tfor ( i = 0, l = vcount.length; i < l; i ++ ) {\n\n\t\t\t\tconst jointCount = vcount[ i ]; // this is the amount of joints that affect a single vertex\n\t\t\t\tconst vertexSkinData = [];\n\n\t\t\t\tfor ( j = 0; j < jointCount; j ++ ) {\n\n\t\t\t\t\tconst skinIndex = v[ stride + jointOffset ];\n\t\t\t\t\tconst weightId = v[ stride + weightOffset ];\n\t\t\t\t\tconst skinWeight = weights[ weightId ];\n\n\t\t\t\t\tvertexSkinData.push( { index: skinIndex, weight: skinWeight } );\n\n\t\t\t\t\tstride += 2;\n\n\t\t\t\t}\n\n\t\t\t\t// we sort the joints in descending order based on the weights.\n\t\t\t\t// this ensures, we only procced the most important joints of the vertex\n\n\t\t\t\tvertexSkinData.sort( descending );\n\n\t\t\t\t// now we provide for each vertex a set of four index and weight values.\n\t\t\t\t// the order of the skin data matches the order of vertices\n\n\t\t\t\tfor ( j = 0; j < BONE_LIMIT; j ++ ) {\n\n\t\t\t\t\tconst d = vertexSkinData[ j ];\n\n\t\t\t\t\tif ( d !== undefined ) {\n\n\t\t\t\t\t\tbuild.indices.array.push( d.index );\n\t\t\t\t\t\tbuild.weights.array.push( d.weight );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tbuild.indices.array.push( 0 );\n\t\t\t\t\t\tbuild.weights.array.push( 0 );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// setup bind matrix\n\n\t\t\tif ( data.bindShapeMatrix ) {\n\n\t\t\t\tbuild.bindMatrix = new Matrix4().fromArray( data.bindShapeMatrix ).transpose();\n\n\t\t\t} else {\n\n\t\t\t\tbuild.bindMatrix = new Matrix4().identity();\n\n\t\t\t}\n\n\t\t\t// process bones and inverse bind matrix data\n\n\t\t\tfor ( i = 0, l = jointSource.array.length; i < l; i ++ ) {\n\n\t\t\t\tconst name = jointSource.array[ i ];\n\t\t\t\tconst boneInverse = new Matrix4().fromArray( inverseSource.array, i * inverseSource.stride ).transpose();\n\n\t\t\t\tbuild.joints.push( { name: name, boneInverse: boneInverse } );\n\n\t\t\t}\n\n\t\t\treturn build;\n\n\t\t\t// array sort function\n\n\t\t\tfunction descending( a, b ) {\n\n\t\t\t\treturn b.weight - a.weight;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction getController( id ) {\n\n\t\t\treturn getBuild( library.controllers[ id ], buildController );\n\n\t\t}\n\n\t\t// image\n\n\t\tfunction parseImage( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tinit_from: getElementsByTagName( xml, 'init_from' )[ 0 ].textContent\n\t\t\t};\n\n\t\t\tlibrary.images[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction buildImage( data ) {\n\n\t\t\tif ( data.build !== undefined ) return data.build;\n\n\t\t\treturn data.init_from;\n\n\t\t}\n\n\t\tfunction getImage( id ) {\n\n\t\t\tconst data = library.images[ id ];\n\n\t\t\tif ( data !== undefined ) {\n\n\t\t\t\treturn getBuild( data, buildImage );\n\n\t\t\t}\n\n\t\t\tconsole.warn( 'THREE.ColladaLoader: Couldn\\'t find image with ID:', id );\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// effect\n\n\t\tfunction parseEffect( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'profile_COMMON':\n\t\t\t\t\t\tdata.profile = parseEffectProfileCOMMON( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.effects[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parseEffectProfileCOMMON( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tsurfaces: {},\n\t\t\t\tsamplers: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'newparam':\n\t\t\t\t\t\tparseEffectNewparam( child, data );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'technique':\n\t\t\t\t\t\tdata.technique = parseEffectTechnique( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'extra':\n\t\t\t\t\t\tdata.extra = parseEffectExtra( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectNewparam( xml, data ) {\n\n\t\t\tconst sid = xml.getAttribute( 'sid' );\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'surface':\n\t\t\t\t\t\tdata.surfaces[ sid ] = parseEffectSurface( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'sampler2D':\n\t\t\t\t\t\tdata.samplers[ sid ] = parseEffectSampler( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseEffectSurface( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'init_from':\n\t\t\t\t\t\tdata.init_from = child.textContent;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectSampler( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'source':\n\t\t\t\t\t\tdata.source = child.textContent;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectTechnique( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'constant':\n\t\t\t\t\tcase 'lambert':\n\t\t\t\t\tcase 'blinn':\n\t\t\t\t\tcase 'phong':\n\t\t\t\t\t\tdata.type = child.nodeName;\n\t\t\t\t\t\tdata.parameters = parseEffectParameters( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'extra':\n\t\t\t\t\t\tdata.extra = parseEffectExtra( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectParameters( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'emission':\n\t\t\t\t\tcase 'diffuse':\n\t\t\t\t\tcase 'specular':\n\t\t\t\t\tcase 'bump':\n\t\t\t\t\tcase 'ambient':\n\t\t\t\t\tcase 'shininess':\n\t\t\t\t\tcase 'transparency':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseEffectParameter( child );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'transparent':\n\t\t\t\t\t\tdata[ child.nodeName ] = {\n\t\t\t\t\t\t\topaque: child.hasAttribute( 'opaque' ) ? child.getAttribute( 'opaque' ) : 'A_ONE',\n\t\t\t\t\t\t\tdata: parseEffectParameter( child )\n\t\t\t\t\t\t};\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectParameter( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'color':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseFloats( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'float':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseFloat( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'texture':\n\t\t\t\t\t\tdata[ child.nodeName ] = { id: child.getAttribute( 'texture' ), extra: parseEffectParameterTexture( child ) };\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectParameterTexture( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\ttechnique: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'extra':\n\t\t\t\t\t\tparseEffectParameterTextureExtra( child, data );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectParameterTextureExtra( xml, data ) {\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique':\n\t\t\t\t\t\tparseEffectParameterTextureExtraTechnique( child, data );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseEffectParameterTextureExtraTechnique( xml, data ) {\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'repeatU':\n\t\t\t\t\tcase 'repeatV':\n\t\t\t\t\tcase 'offsetU':\n\t\t\t\t\tcase 'offsetV':\n\t\t\t\t\t\tdata.technique[ child.nodeName ] = parseFloat( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'wrapU':\n\t\t\t\t\tcase 'wrapV':\n\n\t\t\t\t\t\t// some files have values for wrapU/wrapV which become NaN via parseInt\n\n\t\t\t\t\t\tif ( child.textContent.toUpperCase() === 'TRUE' ) {\n\n\t\t\t\t\t\t\tdata.technique[ child.nodeName ] = 1;\n\n\t\t\t\t\t\t} else if ( child.textContent.toUpperCase() === 'FALSE' ) {\n\n\t\t\t\t\t\t\tdata.technique[ child.nodeName ] = 0;\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tdata.technique[ child.nodeName ] = parseInt( child.textContent );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'bump':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseEffectExtraTechniqueBump( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseEffectExtra( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique':\n\t\t\t\t\t\tdata.technique = parseEffectExtraTechnique( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectExtraTechnique( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'double_sided':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseInt( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'bump':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseEffectExtraTechniqueBump( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseEffectExtraTechniqueBump( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'texture':\n\t\t\t\t\t\tdata[ child.nodeName ] = { id: child.getAttribute( 'texture' ), texcoord: child.getAttribute( 'texcoord' ), extra: parseEffectParameterTexture( child ) };\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildEffect( data ) {\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction getEffect( id ) {\n\n\t\t\treturn getBuild( library.effects[ id ], buildEffect );\n\n\t\t}\n\n\t\t// material\n\n\t\tfunction parseMaterial( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' )\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'instance_effect':\n\t\t\t\t\t\tdata.url = parseId( child.getAttribute( 'url' ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.materials[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction getTextureLoader( image ) {\n\n\t\t\tlet loader;\n\n\t\t\tlet extension = image.slice( ( image.lastIndexOf( '.' ) - 1 >>> 0 ) + 2 ); // http://www.jstips.co/en/javascript/get-file-extension/\n\t\t\textension = extension.toLowerCase();\n\n\t\t\tswitch ( extension ) {\n\n\t\t\t\tcase 'tga':\n\t\t\t\t\tloader = tgaLoader;\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tloader = textureLoader;\n\n\t\t\t}\n\n\t\t\treturn loader;\n\n\t\t}\n\n\t\tfunction buildMaterial( data ) {\n\n\t\t\tconst effect = getEffect( data.url );\n\t\t\tconst technique = effect.profile.technique;\n\n\t\t\tlet material;\n\n\t\t\tswitch ( technique.type ) {\n\n\t\t\t\tcase 'phong':\n\t\t\t\tcase 'blinn':\n\t\t\t\t\tmaterial = new MeshPhongMaterial();\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'lambert':\n\t\t\t\t\tmaterial = new MeshLambertMaterial();\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tmaterial = new MeshBasicMaterial();\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tmaterial.name = data.name || '';\n\n\t\t\tfunction getTexture( textureObject, colorSpace = null ) {\n\n\t\t\t\tconst sampler = effect.profile.samplers[ textureObject.id ];\n\t\t\t\tlet image = null;\n\n\t\t\t\t// get image\n\n\t\t\t\tif ( sampler !== undefined ) {\n\n\t\t\t\t\tconst surface = effect.profile.surfaces[ sampler.source ];\n\t\t\t\t\timage = getImage( surface.init_from );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Undefined sampler. Access image directly (see #12530).' );\n\t\t\t\t\timage = getImage( textureObject.id );\n\n\t\t\t\t}\n\n\t\t\t\t// create texture if image is avaiable\n\n\t\t\t\tif ( image !== null ) {\n\n\t\t\t\t\tconst loader = getTextureLoader( image );\n\n\t\t\t\t\tif ( loader !== undefined ) {\n\n\t\t\t\t\t\tconst texture = loader.load( image );\n\n\t\t\t\t\t\tconst extra = textureObject.extra;\n\n\t\t\t\t\t\tif ( extra !== undefined && extra.technique !== undefined && isEmpty( extra.technique ) === false ) {\n\n\t\t\t\t\t\t\tconst technique = extra.technique;\n\n\t\t\t\t\t\t\ttexture.wrapS = technique.wrapU ? RepeatWrapping : ClampToEdgeWrapping;\n\t\t\t\t\t\t\ttexture.wrapT = technique.wrapV ? RepeatWrapping : ClampToEdgeWrapping;\n\n\t\t\t\t\t\t\ttexture.offset.set( technique.offsetU || 0, technique.offsetV || 0 );\n\t\t\t\t\t\t\ttexture.repeat.set( technique.repeatU || 1, technique.repeatV || 1 );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\ttexture.wrapS = RepeatWrapping;\n\t\t\t\t\t\t\ttexture.wrapT = RepeatWrapping;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ( colorSpace !== null ) {\n\n\t\t\t\t\t\t\ttexture.colorSpace = colorSpace;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn texture;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Loader for texture %s not found.', image );\n\n\t\t\t\t\t\treturn null;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Couldn\\'t create texture with ID:', textureObject.id );\n\n\t\t\t\t\treturn null;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tconst parameters = technique.parameters;\n\n\t\t\tfor ( const key in parameters ) {\n\n\t\t\t\tconst parameter = parameters[ key ];\n\n\t\t\t\tswitch ( key ) {\n\n\t\t\t\t\tcase 'diffuse':\n\t\t\t\t\t\tif ( parameter.color ) material.color.fromArray( parameter.color );\n\t\t\t\t\t\tif ( parameter.texture ) material.map = getTexture( parameter.texture, SRGBColorSpace );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'specular':\n\t\t\t\t\t\tif ( parameter.color && material.specular ) material.specular.fromArray( parameter.color );\n\t\t\t\t\t\tif ( parameter.texture ) material.specularMap = getTexture( parameter.texture );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'bump':\n\t\t\t\t\t\tif ( parameter.texture ) material.normalMap = getTexture( parameter.texture );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'ambient':\n\t\t\t\t\t\tif ( parameter.texture ) material.lightMap = getTexture( parameter.texture, SRGBColorSpace );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'shininess':\n\t\t\t\t\t\tif ( parameter.float && material.shininess ) material.shininess = parameter.float;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'emission':\n\t\t\t\t\t\tif ( parameter.color && material.emissive ) material.emissive.fromArray( parameter.color );\n\t\t\t\t\t\tif ( parameter.texture ) material.emissiveMap = getTexture( parameter.texture, SRGBColorSpace );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tmaterial.color.convertSRGBToLinear();\n\t\t\tif ( material.specular ) material.specular.convertSRGBToLinear();\n\t\t\tif ( material.emissive ) material.emissive.convertSRGBToLinear();\n\n\t\t\t//\n\n\t\t\tlet transparent = parameters[ 'transparent' ];\n\t\t\tlet transparency = parameters[ 'transparency' ];\n\n\t\t\t// <transparency> does not exist but <transparent>\n\n\t\t\tif ( transparency === undefined && transparent ) {\n\n\t\t\t\ttransparency = {\n\t\t\t\t\tfloat: 1\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t\t// <transparent> does not exist but <transparency>\n\n\t\t\tif ( transparent === undefined && transparency ) {\n\n\t\t\t\ttransparent = {\n\t\t\t\t\topaque: 'A_ONE',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tcolor: [ 1, 1, 1, 1 ]\n\t\t\t\t\t} };\n\n\t\t\t}\n\n\t\t\tif ( transparent && transparency ) {\n\n\t\t\t\t// handle case if a texture exists but no color\n\n\t\t\t\tif ( transparent.data.texture ) {\n\n\t\t\t\t\t// we do not set an alpha map (see #13792)\n\n\t\t\t\t\tmaterial.transparent = true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconst color = transparent.data.color;\n\n\t\t\t\t\tswitch ( transparent.opaque ) {\n\n\t\t\t\t\t\tcase 'A_ONE':\n\t\t\t\t\t\t\tmaterial.opacity = color[ 3 ] * transparency.float;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'RGB_ZERO':\n\t\t\t\t\t\t\tmaterial.opacity = 1 - ( color[ 0 ] * transparency.float );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'A_ZERO':\n\t\t\t\t\t\t\tmaterial.opacity = 1 - ( color[ 3 ] * transparency.float );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'RGB_ONE':\n\t\t\t\t\t\t\tmaterial.opacity = color[ 0 ] * transparency.float;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Invalid opaque type \"%s\" of transparent tag.', transparent.opaque );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( material.opacity < 1 ) material.transparent = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t//\n\n\n\t\t\tif ( technique.extra !== undefined && technique.extra.technique !== undefined ) {\n\n\t\t\t\tconst techniques = technique.extra.technique;\n\n\t\t\t\tfor ( const k in techniques ) {\n\n\t\t\t\t\tconst v = techniques[ k ];\n\n\t\t\t\t\tswitch ( k ) {\n\n\t\t\t\t\t\tcase 'double_sided':\n\t\t\t\t\t\t\tmaterial.side = ( v === 1 ? DoubleSide : FrontSide );\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'bump':\n\t\t\t\t\t\t\tmaterial.normalMap = getTexture( v.texture );\n\t\t\t\t\t\t\tmaterial.normalScale = new Vector2( 1, 1 );\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn material;\n\n\t\t}\n\n\t\tfunction getMaterial( id ) {\n\n\t\t\treturn getBuild( library.materials[ id ], buildMaterial );\n\n\t\t}\n\n\t\t// camera\n\n\t\tfunction parseCamera( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' )\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'optics':\n\t\t\t\t\t\tdata.optics = parseCameraOptics( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.cameras[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parseCameraOptics( xml ) {\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique_common':\n\t\t\t\t\t\treturn parseCameraTechnique( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn {};\n\n\t\t}\n\n\t\tfunction parseCameraTechnique( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'perspective':\n\t\t\t\t\tcase 'orthographic':\n\n\t\t\t\t\t\tdata.technique = child.nodeName;\n\t\t\t\t\t\tdata.parameters = parseCameraParameters( child );\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseCameraParameters( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'xfov':\n\t\t\t\t\tcase 'yfov':\n\t\t\t\t\tcase 'xmag':\n\t\t\t\t\tcase 'ymag':\n\t\t\t\t\tcase 'znear':\n\t\t\t\t\tcase 'zfar':\n\t\t\t\t\tcase 'aspect_ratio':\n\t\t\t\t\t\tdata[ child.nodeName ] = parseFloat( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildCamera( data ) {\n\n\t\t\tlet camera;\n\n\t\t\tswitch ( data.optics.technique ) {\n\n\t\t\t\tcase 'perspective':\n\t\t\t\t\tcamera = new PerspectiveCamera(\n\t\t\t\t\t\tdata.optics.parameters.yfov,\n\t\t\t\t\t\tdata.optics.parameters.aspect_ratio,\n\t\t\t\t\t\tdata.optics.parameters.znear,\n\t\t\t\t\t\tdata.optics.parameters.zfar\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'orthographic':\n\t\t\t\t\tlet ymag = data.optics.parameters.ymag;\n\t\t\t\t\tlet xmag = data.optics.parameters.xmag;\n\t\t\t\t\tconst aspectRatio = data.optics.parameters.aspect_ratio;\n\n\t\t\t\t\txmag = ( xmag === undefined ) ? ( ymag * aspectRatio ) : xmag;\n\t\t\t\t\tymag = ( ymag === undefined ) ? ( xmag / aspectRatio ) : ymag;\n\n\t\t\t\t\txmag *= 0.5;\n\t\t\t\t\tymag *= 0.5;\n\n\t\t\t\t\tcamera = new OrthographicCamera(\n\t\t\t\t\t\t- xmag, xmag, ymag, - ymag, // left, right, top, bottom\n\t\t\t\t\t\tdata.optics.parameters.znear,\n\t\t\t\t\t\tdata.optics.parameters.zfar\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tcamera = new PerspectiveCamera();\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tcamera.name = data.name || '';\n\n\t\t\treturn camera;\n\n\t\t}\n\n\t\tfunction getCamera( id ) {\n\n\t\t\tconst data = library.cameras[ id ];\n\n\t\t\tif ( data !== undefined ) {\n\n\t\t\t\treturn getBuild( data, buildCamera );\n\n\t\t\t}\n\n\t\t\tconsole.warn( 'THREE.ColladaLoader: Couldn\\'t find camera with ID:', id );\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// light\n\n\t\tfunction parseLight( xml ) {\n\n\t\t\tlet data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique_common':\n\t\t\t\t\t\tdata = parseLightTechnique( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.lights[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parseLightTechnique( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'directional':\n\t\t\t\t\tcase 'point':\n\t\t\t\t\tcase 'spot':\n\t\t\t\t\tcase 'ambient':\n\n\t\t\t\t\t\tdata.technique = child.nodeName;\n\t\t\t\t\t\tdata.parameters = parseLightParameters( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseLightParameters( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'color':\n\t\t\t\t\t\tconst array = parseFloats( child.textContent );\n\t\t\t\t\t\tdata.color = new Color().fromArray( array ).convertSRGBToLinear();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'falloff_angle':\n\t\t\t\t\t\tdata.falloffAngle = parseFloat( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'quadratic_attenuation':\n\t\t\t\t\t\tconst f = parseFloat( child.textContent );\n\t\t\t\t\t\tdata.distance = f ? Math.sqrt( 1 / f ) : 0;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildLight( data ) {\n\n\t\t\tlet light;\n\n\t\t\tswitch ( data.technique ) {\n\n\t\t\t\tcase 'directional':\n\t\t\t\t\tlight = new DirectionalLight();\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'point':\n\t\t\t\t\tlight = new PointLight();\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'spot':\n\t\t\t\t\tlight = new SpotLight();\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'ambient':\n\t\t\t\t\tlight = new AmbientLight();\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( data.parameters.color ) light.color.copy( data.parameters.color );\n\t\t\tif ( data.parameters.distance ) light.distance = data.parameters.distance;\n\n\t\t\treturn light;\n\n\t\t}\n\n\t\tfunction getLight( id ) {\n\n\t\t\tconst data = library.lights[ id ];\n\n\t\t\tif ( data !== undefined ) {\n\n\t\t\t\treturn getBuild( data, buildLight );\n\n\t\t\t}\n\n\t\t\tconsole.warn( 'THREE.ColladaLoader: Couldn\\'t find light with ID:', id );\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// geometry\n\n\t\tfunction parseGeometry( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' ),\n\t\t\t\tsources: {},\n\t\t\t\tvertices: {},\n\t\t\t\tprimitives: []\n\t\t\t};\n\n\t\t\tconst mesh = getElementsByTagName( xml, 'mesh' )[ 0 ];\n\n\t\t\t// the following tags inside geometry are not supported yet (see https://github.com/mrdoob/three.js/pull/12606): convex_mesh, spline, brep\n\t\t\tif ( mesh === undefined ) return;\n\n\t\t\tfor ( let i = 0; i < mesh.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = mesh.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tconst id = child.getAttribute( 'id' );\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'source':\n\t\t\t\t\t\tdata.sources[ id ] = parseSource( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'vertices':\n\t\t\t\t\t\t// data.sources[ id ] = data.sources[ parseId( getElementsByTagName( child, 'input' )[ 0 ].getAttribute( 'source' ) ) ];\n\t\t\t\t\t\tdata.vertices = parseGeometryVertices( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'polygons':\n\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Unsupported primitive type: ', child.nodeName );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'lines':\n\t\t\t\t\tcase 'linestrips':\n\t\t\t\t\tcase 'polylist':\n\t\t\t\t\tcase 'triangles':\n\t\t\t\t\t\tdata.primitives.push( parseGeometryPrimitive( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tconsole.log( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.geometries[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parseSource( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tarray: [],\n\t\t\t\tstride: 3\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'float_array':\n\t\t\t\t\t\tdata.array = parseFloats( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Name_array':\n\t\t\t\t\t\tdata.array = parseStrings( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'technique_common':\n\t\t\t\t\t\tconst accessor = getElementsByTagName( child, 'accessor' )[ 0 ];\n\n\t\t\t\t\t\tif ( accessor !== undefined ) {\n\n\t\t\t\t\t\t\tdata.stride = parseInt( accessor.getAttribute( 'stride' ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseGeometryVertices( xml ) {\n\n\t\t\tconst data = {};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tdata[ child.getAttribute( 'semantic' ) ] = parseId( child.getAttribute( 'source' ) );\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseGeometryPrimitive( xml ) {\n\n\t\t\tconst primitive = {\n\t\t\t\ttype: xml.nodeName,\n\t\t\t\tmaterial: xml.getAttribute( 'material' ),\n\t\t\t\tcount: parseInt( xml.getAttribute( 'count' ) ),\n\t\t\t\tinputs: {},\n\t\t\t\tstride: 0,\n\t\t\t\thasUV: false\n\t\t\t};\n\n\t\t\tfor ( let i = 0, l = xml.childNodes.length; i < l; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'input':\n\t\t\t\t\t\tconst id = parseId( child.getAttribute( 'source' ) );\n\t\t\t\t\t\tconst semantic = child.getAttribute( 'semantic' );\n\t\t\t\t\t\tconst offset = parseInt( child.getAttribute( 'offset' ) );\n\t\t\t\t\t\tconst set = parseInt( child.getAttribute( 'set' ) );\n\t\t\t\t\t\tconst inputname = ( set > 0 ? semantic + set : semantic );\n\t\t\t\t\t\tprimitive.inputs[ inputname ] = { id: id, offset: offset };\n\t\t\t\t\t\tprimitive.stride = Math.max( primitive.stride, offset + 1 );\n\t\t\t\t\t\tif ( semantic === 'TEXCOORD' ) primitive.hasUV = true;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'vcount':\n\t\t\t\t\t\tprimitive.vcount = parseInts( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'p':\n\t\t\t\t\t\tprimitive.p = parseInts( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn primitive;\n\n\t\t}\n\n\t\tfunction groupPrimitives( primitives ) {\n\n\t\t\tconst build = {};\n\n\t\t\tfor ( let i = 0; i < primitives.length; i ++ ) {\n\n\t\t\t\tconst primitive = primitives[ i ];\n\n\t\t\t\tif ( build[ primitive.type ] === undefined ) build[ primitive.type ] = [];\n\n\t\t\t\tbuild[ primitive.type ].push( primitive );\n\n\t\t\t}\n\n\t\t\treturn build;\n\n\t\t}\n\n\t\tfunction checkUVCoordinates( primitives ) {\n\n\t\t\tlet count = 0;\n\n\t\t\tfor ( let i = 0, l = primitives.length; i < l; i ++ ) {\n\n\t\t\t\tconst primitive = primitives[ i ];\n\n\t\t\t\tif ( primitive.hasUV === true ) {\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( count > 0 && count < primitives.length ) {\n\n\t\t\t\tprimitives.uvsNeedsFix = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction buildGeometry( data ) {\n\n\t\t\tconst build = {};\n\n\t\t\tconst sources = data.sources;\n\t\t\tconst vertices = data.vertices;\n\t\t\tconst primitives = data.primitives;\n\n\t\t\tif ( primitives.length === 0 ) return {};\n\n\t\t\t// our goal is to create one buffer geometry for a single type of primitives\n\t\t\t// first, we group all primitives by their type\n\n\t\t\tconst groupedPrimitives = groupPrimitives( primitives );\n\n\t\t\tfor ( const type in groupedPrimitives ) {\n\n\t\t\t\tconst primitiveType = groupedPrimitives[ type ];\n\n\t\t\t\t// second, ensure consistent uv coordinates for each type of primitives (polylist,triangles or lines)\n\n\t\t\t\tcheckUVCoordinates( primitiveType );\n\n\t\t\t\t// third, create a buffer geometry for each type of primitives\n\n\t\t\t\tbuild[ type ] = buildGeometryType( primitiveType, sources, vertices );\n\n\t\t\t}\n\n\t\t\treturn build;\n\n\t\t}\n\n\t\tfunction buildGeometryType( primitives, sources, vertices ) {\n\n\t\t\tconst build = {};\n\n\t\t\tconst position = { array: [], stride: 0 };\n\t\t\tconst normal = { array: [], stride: 0 };\n\t\t\tconst uv = { array: [], stride: 0 };\n\t\t\tconst uv1 = { array: [], stride: 0 };\n\t\t\tconst color = { array: [], stride: 0 };\n\n\t\t\tconst skinIndex = { array: [], stride: 4 };\n\t\t\tconst skinWeight = { array: [], stride: 4 };\n\n\t\t\tconst geometry = new BufferGeometry();\n\n\t\t\tconst materialKeys = [];\n\n\t\t\tlet start = 0;\n\n\t\t\tfor ( let p = 0; p < primitives.length; p ++ ) {\n\n\t\t\t\tconst primitive = primitives[ p ];\n\t\t\t\tconst inputs = primitive.inputs;\n\n\t\t\t\t// groups\n\n\t\t\t\tlet count = 0;\n\n\t\t\t\tswitch ( primitive.type ) {\n\n\t\t\t\t\tcase 'lines':\n\t\t\t\t\tcase 'linestrips':\n\t\t\t\t\t\tcount = primitive.count * 2;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'triangles':\n\t\t\t\t\t\tcount = primitive.count * 3;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'polylist':\n\n\t\t\t\t\t\tfor ( let g = 0; g < primitive.count; g ++ ) {\n\n\t\t\t\t\t\t\tconst vc = primitive.vcount[ g ];\n\n\t\t\t\t\t\t\tswitch ( vc ) {\n\n\t\t\t\t\t\t\t\tcase 3:\n\t\t\t\t\t\t\t\t\tcount += 3; // single triangle\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tcase 4:\n\t\t\t\t\t\t\t\t\tcount += 6; // quad, subdivided into two triangles\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\tcount += ( vc - 2 ) * 3; // polylist with more than four vertices\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Unknow primitive type:', primitive.type );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.addGroup( start, count, p );\n\t\t\t\tstart += count;\n\n\t\t\t\t// material\n\n\t\t\t\tif ( primitive.material ) {\n\n\t\t\t\t\tmaterialKeys.push( primitive.material );\n\n\t\t\t\t}\n\n\t\t\t\t// geometry data\n\n\t\t\t\tfor ( const name in inputs ) {\n\n\t\t\t\t\tconst input = inputs[ name ];\n\n\t\t\t\t\tswitch ( name )\t{\n\n\t\t\t\t\t\tcase 'VERTEX':\n\t\t\t\t\t\t\tfor ( const key in vertices ) {\n\n\t\t\t\t\t\t\t\tconst id = vertices[ key ];\n\n\t\t\t\t\t\t\t\tswitch ( key ) {\n\n\t\t\t\t\t\t\t\t\tcase 'POSITION':\n\t\t\t\t\t\t\t\t\t\tconst prevLength = position.array.length;\n\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ id ], input.offset, position.array );\n\t\t\t\t\t\t\t\t\t\tposition.stride = sources[ id ].stride;\n\n\t\t\t\t\t\t\t\t\t\tif ( sources.skinWeights && sources.skinIndices ) {\n\n\t\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources.skinIndices, input.offset, skinIndex.array );\n\t\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources.skinWeights, input.offset, skinWeight.array );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// see #3803\n\n\t\t\t\t\t\t\t\t\t\tif ( primitive.hasUV === false && primitives.uvsNeedsFix === true ) {\n\n\t\t\t\t\t\t\t\t\t\t\tconst count = ( position.array.length - prevLength ) / position.stride;\n\n\t\t\t\t\t\t\t\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\t// fill missing uv coordinates\n\n\t\t\t\t\t\t\t\t\t\t\t\tuv.array.push( 0, 0 );\n\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\tcase 'NORMAL':\n\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ id ], input.offset, normal.array );\n\t\t\t\t\t\t\t\t\t\tnormal.stride = sources[ id ].stride;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\tcase 'COLOR':\n\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ id ], input.offset, color.array );\n\t\t\t\t\t\t\t\t\t\tcolor.stride = sources[ id ].stride;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\tcase 'TEXCOORD':\n\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ id ], input.offset, uv.array );\n\t\t\t\t\t\t\t\t\t\tuv.stride = sources[ id ].stride;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\tcase 'TEXCOORD1':\n\t\t\t\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ id ], input.offset, uv1.array );\n\t\t\t\t\t\t\t\t\t\tuv.stride = sources[ id ].stride;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Semantic \"%s\" not handled in geometry build process.', key );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'NORMAL':\n\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ input.id ], input.offset, normal.array );\n\t\t\t\t\t\t\tnormal.stride = sources[ input.id ].stride;\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'COLOR':\n\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ input.id ], input.offset, color.array, true );\n\t\t\t\t\t\t\tcolor.stride = sources[ input.id ].stride;\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'TEXCOORD':\n\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ input.id ], input.offset, uv.array );\n\t\t\t\t\t\t\tuv.stride = sources[ input.id ].stride;\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'TEXCOORD1':\n\t\t\t\t\t\t\tbuildGeometryData( primitive, sources[ input.id ], input.offset, uv1.array );\n\t\t\t\t\t\t\tuv1.stride = sources[ input.id ].stride;\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// build geometry\n\n\t\t\tif ( position.array.length > 0 ) geometry.setAttribute( 'position', new Float32BufferAttribute( position.array, position.stride ) );\n\t\t\tif ( normal.array.length > 0 ) geometry.setAttribute( 'normal', new Float32BufferAttribute( normal.array, normal.stride ) );\n\t\t\tif ( color.array.length > 0 ) geometry.setAttribute( 'color', new Float32BufferAttribute( color.array, color.stride ) );\n\t\t\tif ( uv.array.length > 0 ) geometry.setAttribute( 'uv', new Float32BufferAttribute( uv.array, uv.stride ) );\n\t\t\tif ( uv1.array.length > 0 ) geometry.setAttribute( 'uv1', new Float32BufferAttribute( uv1.array, uv1.stride ) );\n\n\t\t\tif ( skinIndex.array.length > 0 ) geometry.setAttribute( 'skinIndex', new Float32BufferAttribute( skinIndex.array, skinIndex.stride ) );\n\t\t\tif ( skinWeight.array.length > 0 ) geometry.setAttribute( 'skinWeight', new Float32BufferAttribute( skinWeight.array, skinWeight.stride ) );\n\n\t\t\tbuild.data = geometry;\n\t\t\tbuild.type = primitives[ 0 ].type;\n\t\t\tbuild.materialKeys = materialKeys;\n\n\t\t\treturn build;\n\n\t\t}\n\n\t\tfunction buildGeometryData( primitive, source, offset, array, isColor = false ) {\n\n\t\t\tconst indices = primitive.p;\n\t\t\tconst stride = primitive.stride;\n\t\t\tconst vcount = primitive.vcount;\n\n\t\t\tfunction pushVector( i ) {\n\n\t\t\t\tlet index = indices[ i + offset ] * sourceStride;\n\t\t\t\tconst length = index + sourceStride;\n\n\t\t\t\tfor ( ; index < length; index ++ ) {\n\n\t\t\t\t\tarray.push( sourceArray[ index ] );\n\n\t\t\t\t}\n\n\t\t\t\tif ( isColor ) {\n\n\t\t\t\t\t// convert the vertex colors from srgb to linear if present\n\t\t\t\t\tconst startIndex = array.length - sourceStride - 1;\n\t\t\t\t\ttempColor.setRGB(\n\t\t\t\t\t\tarray[ startIndex + 0 ],\n\t\t\t\t\t\tarray[ startIndex + 1 ],\n\t\t\t\t\t\tarray[ startIndex + 2 ]\n\t\t\t\t\t).convertSRGBToLinear();\n\n\t\t\t\t\tarray[ startIndex + 0 ] = tempColor.r;\n\t\t\t\t\tarray[ startIndex + 1 ] = tempColor.g;\n\t\t\t\t\tarray[ startIndex + 2 ] = tempColor.b;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tconst sourceArray = source.array;\n\t\t\tconst sourceStride = source.stride;\n\n\t\t\tif ( primitive.vcount !== undefined ) {\n\n\t\t\t\tlet index = 0;\n\n\t\t\t\tfor ( let i = 0, l = vcount.length; i < l; i ++ ) {\n\n\t\t\t\t\tconst count = vcount[ i ];\n\n\t\t\t\t\tif ( count === 4 ) {\n\n\t\t\t\t\t\tconst a = index + stride * 0;\n\t\t\t\t\t\tconst b = index + stride * 1;\n\t\t\t\t\t\tconst c = index + stride * 2;\n\t\t\t\t\t\tconst d = index + stride * 3;\n\n\t\t\t\t\t\tpushVector( a ); pushVector( b ); pushVector( d );\n\t\t\t\t\t\tpushVector( b ); pushVector( c ); pushVector( d );\n\n\t\t\t\t\t} else if ( count === 3 ) {\n\n\t\t\t\t\t\tconst a = index + stride * 0;\n\t\t\t\t\t\tconst b = index + stride * 1;\n\t\t\t\t\t\tconst c = index + stride * 2;\n\n\t\t\t\t\t\tpushVector( a ); pushVector( b ); pushVector( c );\n\n\t\t\t\t\t} else if ( count > 4 ) {\n\n\t\t\t\t\t\tfor ( let k = 1, kl = ( count - 2 ); k <= kl; k ++ ) {\n\n\t\t\t\t\t\t\tconst a = index + stride * 0;\n\t\t\t\t\t\t\tconst b = index + stride * k;\n\t\t\t\t\t\t\tconst c = index + stride * ( k + 1 );\n\n\t\t\t\t\t\t\tpushVector( a ); pushVector( b ); pushVector( c );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tindex += stride * count;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tfor ( let i = 0, l = indices.length; i < l; i += stride ) {\n\n\t\t\t\t\tpushVector( i );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction getGeometry( id ) {\n\n\t\t\treturn getBuild( library.geometries[ id ], buildGeometry );\n\n\t\t}\n\n\t\t// kinematics\n\n\t\tfunction parseKinematicsModel( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' ) || '',\n\t\t\t\tjoints: {},\n\t\t\t\tlinks: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique_common':\n\t\t\t\t\t\tparseKinematicsTechniqueCommon( child, data );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.kinematicsModels[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction buildKinematicsModel( data ) {\n\n\t\t\tif ( data.build !== undefined ) return data.build;\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction getKinematicsModel( id ) {\n\n\t\t\treturn getBuild( library.kinematicsModels[ id ], buildKinematicsModel );\n\n\t\t}\n\n\t\tfunction parseKinematicsTechniqueCommon( xml, data ) {\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'joint':\n\t\t\t\t\t\tdata.joints[ child.getAttribute( 'sid' ) ] = parseKinematicsJoint( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'link':\n\t\t\t\t\t\tdata.links.push( parseKinematicsLink( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseKinematicsJoint( xml ) {\n\n\t\t\tlet data;\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'prismatic':\n\t\t\t\t\tcase 'revolute':\n\t\t\t\t\t\tdata = parseKinematicsJointParameter( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseKinematicsJointParameter( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tsid: xml.getAttribute( 'sid' ),\n\t\t\t\tname: xml.getAttribute( 'name' ) || '',\n\t\t\t\taxis: new Vector3(),\n\t\t\t\tlimits: {\n\t\t\t\t\tmin: 0,\n\t\t\t\t\tmax: 0\n\t\t\t\t},\n\t\t\t\ttype: xml.nodeName,\n\t\t\t\tstatic: false,\n\t\t\t\tzeroPosition: 0,\n\t\t\t\tmiddlePosition: 0\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'axis':\n\t\t\t\t\t\tconst array = parseFloats( child.textContent );\n\t\t\t\t\t\tdata.axis.fromArray( array );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'limits':\n\t\t\t\t\t\tconst max = child.getElementsByTagName( 'max' )[ 0 ];\n\t\t\t\t\t\tconst min = child.getElementsByTagName( 'min' )[ 0 ];\n\n\t\t\t\t\t\tdata.limits.max = parseFloat( max.textContent );\n\t\t\t\t\t\tdata.limits.min = parseFloat( min.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// if min is equal to or greater than max, consider the joint static\n\n\t\t\tif ( data.limits.min >= data.limits.max ) {\n\n\t\t\t\tdata.static = true;\n\n\t\t\t}\n\n\t\t\t// calculate middle position\n\n\t\t\tdata.middlePosition = ( data.limits.min + data.limits.max ) / 2.0;\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseKinematicsLink( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tsid: xml.getAttribute( 'sid' ),\n\t\t\t\tname: xml.getAttribute( 'name' ) || '',\n\t\t\t\tattachments: [],\n\t\t\t\ttransforms: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'attachment_full':\n\t\t\t\t\t\tdata.attachments.push( parseKinematicsAttachment( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'matrix':\n\t\t\t\t\tcase 'translate':\n\t\t\t\t\tcase 'rotate':\n\t\t\t\t\t\tdata.transforms.push( parseKinematicsTransform( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseKinematicsAttachment( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tjoint: xml.getAttribute( 'joint' ).split( '/' ).pop(),\n\t\t\t\ttransforms: [],\n\t\t\t\tlinks: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'link':\n\t\t\t\t\t\tdata.links.push( parseKinematicsLink( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'matrix':\n\t\t\t\t\tcase 'translate':\n\t\t\t\t\tcase 'rotate':\n\t\t\t\t\t\tdata.transforms.push( parseKinematicsTransform( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseKinematicsTransform( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\ttype: xml.nodeName\n\t\t\t};\n\n\t\t\tconst array = parseFloats( xml.textContent );\n\n\t\t\tswitch ( data.type ) {\n\n\t\t\t\tcase 'matrix':\n\t\t\t\t\tdata.obj = new Matrix4();\n\t\t\t\t\tdata.obj.fromArray( array ).transpose();\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'translate':\n\t\t\t\t\tdata.obj = new Vector3();\n\t\t\t\t\tdata.obj.fromArray( array );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'rotate':\n\t\t\t\t\tdata.obj = new Vector3();\n\t\t\t\t\tdata.obj.fromArray( array );\n\t\t\t\t\tdata.angle = MathUtils.degToRad( array[ 3 ] );\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\t// physics\n\n\t\tfunction parsePhysicsModel( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' ) || '',\n\t\t\t\trigidBodies: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'rigid_body':\n\t\t\t\t\t\tdata.rigidBodies[ child.getAttribute( 'name' ) ] = {};\n\t\t\t\t\t\tparsePhysicsRigidBody( child, data.rigidBodies[ child.getAttribute( 'name' ) ] );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.physicsModels[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction parsePhysicsRigidBody( xml, data ) {\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'technique_common':\n\t\t\t\t\t\tparsePhysicsTechniqueCommon( child, data );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parsePhysicsTechniqueCommon( xml, data ) {\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'inertia':\n\t\t\t\t\t\tdata.inertia = parseFloats( child.textContent );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'mass':\n\t\t\t\t\t\tdata.mass = parseFloats( child.textContent )[ 0 ];\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// scene\n\n\t\tfunction parseKinematicsScene( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tbindJointAxis: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'bind_joint_axis':\n\t\t\t\t\t\tdata.bindJointAxis.push( parseKinematicsBindJointAxis( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tlibrary.kinematicsScenes[ parseId( xml.getAttribute( 'url' ) ) ] = data;\n\n\t\t}\n\n\t\tfunction parseKinematicsBindJointAxis( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\ttarget: xml.getAttribute( 'target' ).split( '/' ).pop()\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'axis':\n\t\t\t\t\t\tconst param = child.getElementsByTagName( 'param' )[ 0 ];\n\t\t\t\t\t\tdata.axis = param.textContent;\n\t\t\t\t\t\tconst tmpJointIndex = data.axis.split( 'inst_' ).pop().split( 'axis' )[ 0 ];\n\t\t\t\t\t\tdata.jointIndex = tmpJointIndex.substring( 0, tmpJointIndex.length - 1 );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildKinematicsScene( data ) {\n\n\t\t\tif ( data.build !== undefined ) return data.build;\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction getKinematicsScene( id ) {\n\n\t\t\treturn getBuild( library.kinematicsScenes[ id ], buildKinematicsScene );\n\n\t\t}\n\n\t\tfunction setupKinematics() {\n\n\t\t\tconst kinematicsModelId = Object.keys( library.kinematicsModels )[ 0 ];\n\t\t\tconst kinematicsSceneId = Object.keys( library.kinematicsScenes )[ 0 ];\n\t\t\tconst visualSceneId = Object.keys( library.visualScenes )[ 0 ];\n\n\t\t\tif ( kinematicsModelId === undefined || kinematicsSceneId === undefined ) return;\n\n\t\t\tconst kinematicsModel = getKinematicsModel( kinematicsModelId );\n\t\t\tconst kinematicsScene = getKinematicsScene( kinematicsSceneId );\n\t\t\tconst visualScene = getVisualScene( visualSceneId );\n\n\t\t\tconst bindJointAxis = kinematicsScene.bindJointAxis;\n\t\t\tconst jointMap = {};\n\n\t\t\tfor ( let i = 0, l = bindJointAxis.length; i < l; i ++ ) {\n\n\t\t\t\tconst axis = bindJointAxis[ i ];\n\n\t\t\t\t// the result of the following query is an element of type 'translate', 'rotate','scale' or 'matrix'\n\n\t\t\t\tconst targetElement = collada.querySelector( '[sid=\"' + axis.target + '\"]' );\n\n\t\t\t\tif ( targetElement ) {\n\n\t\t\t\t\t// get the parent of the transform element\n\n\t\t\t\t\tconst parentVisualElement = targetElement.parentElement;\n\n\t\t\t\t\t// connect the joint of the kinematics model with the element in the visual scene\n\n\t\t\t\t\tconnect( axis.jointIndex, parentVisualElement );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tfunction connect( jointIndex, visualElement ) {\n\n\t\t\t\tconst visualElementName = visualElement.getAttribute( 'name' );\n\t\t\t\tconst joint = kinematicsModel.joints[ jointIndex ];\n\n\t\t\t\tvisualScene.traverse( function ( object ) {\n\n\t\t\t\t\tif ( object.name === visualElementName ) {\n\n\t\t\t\t\t\tjointMap[ jointIndex ] = {\n\t\t\t\t\t\t\tobject: object,\n\t\t\t\t\t\t\ttransforms: buildTransformList( visualElement ),\n\t\t\t\t\t\t\tjoint: joint,\n\t\t\t\t\t\t\tposition: joint.zeroPosition\n\t\t\t\t\t\t};\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tconst m0 = new Matrix4();\n\n\t\t\tkinematics = {\n\n\t\t\t\tjoints: kinematicsModel && kinematicsModel.joints,\n\n\t\t\t\tgetJointValue: function ( jointIndex ) {\n\n\t\t\t\t\tconst jointData = jointMap[ jointIndex ];\n\n\t\t\t\t\tif ( jointData ) {\n\n\t\t\t\t\t\treturn jointData.position;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Joint ' + jointIndex + ' doesn\\'t exist.' );\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t\tsetJointValue: function ( jointIndex, value ) {\n\n\t\t\t\t\tconst jointData = jointMap[ jointIndex ];\n\n\t\t\t\t\tif ( jointData ) {\n\n\t\t\t\t\t\tconst joint = jointData.joint;\n\n\t\t\t\t\t\tif ( value > joint.limits.max || value < joint.limits.min ) {\n\n\t\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Joint ' + jointIndex + ' value ' + value + ' outside of limits (min: ' + joint.limits.min + ', max: ' + joint.limits.max + ').' );\n\n\t\t\t\t\t\t} else if ( joint.static ) {\n\n\t\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Joint ' + jointIndex + ' is static.' );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tconst object = jointData.object;\n\t\t\t\t\t\t\tconst axis = joint.axis;\n\t\t\t\t\t\t\tconst transforms = jointData.transforms;\n\n\t\t\t\t\t\t\tmatrix.identity();\n\n\t\t\t\t\t\t\t// each update, we have to apply all transforms in the correct order\n\n\t\t\t\t\t\t\tfor ( let i = 0; i < transforms.length; i ++ ) {\n\n\t\t\t\t\t\t\t\tconst transform = transforms[ i ];\n\n\t\t\t\t\t\t\t\t// if there is a connection of the transform node with a joint, apply the joint value\n\n\t\t\t\t\t\t\t\tif ( transform.sid && transform.sid.indexOf( jointIndex ) !== - 1 ) {\n\n\t\t\t\t\t\t\t\t\tswitch ( joint.type ) {\n\n\t\t\t\t\t\t\t\t\t\tcase 'revolute':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.multiply( m0.makeRotationAxis( axis, MathUtils.degToRad( value ) ) );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t\tcase 'prismatic':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.multiply( m0.makeTranslation( axis.x * value, axis.y * value, axis.z * value ) );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Unknown joint type: ' + joint.type );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t\tswitch ( transform.type ) {\n\n\t\t\t\t\t\t\t\t\t\tcase 'matrix':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.multiply( transform.obj );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t\tcase 'translate':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.multiply( m0.makeTranslation( transform.obj.x, transform.obj.y, transform.obj.z ) );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t\tcase 'scale':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.scale( transform.obj );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t\tcase 'rotate':\n\t\t\t\t\t\t\t\t\t\t\tmatrix.multiply( m0.makeRotationAxis( transform.obj, transform.angle ) );\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tobject.matrix.copy( matrix );\n\t\t\t\t\t\t\tobject.matrix.decompose( object.position, object.quaternion, object.scale );\n\n\t\t\t\t\t\t\tjointMap[ jointIndex ].position = value;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tconsole.log( 'THREE.ColladaLoader: ' + jointIndex + ' does not exist.' );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t};\n\n\t\t}\n\n\t\tfunction buildTransformList( node ) {\n\n\t\t\tconst transforms = [];\n\n\t\t\tconst xml = collada.querySelector( '[id=\"' + node.id + '\"]' );\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tlet array, vector;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'matrix':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tconst matrix = new Matrix4().fromArray( array ).transpose();\n\t\t\t\t\t\ttransforms.push( {\n\t\t\t\t\t\t\tsid: child.getAttribute( 'sid' ),\n\t\t\t\t\t\t\ttype: child.nodeName,\n\t\t\t\t\t\t\tobj: matrix\n\t\t\t\t\t\t} );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'translate':\n\t\t\t\t\tcase 'scale':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tvector = new Vector3().fromArray( array );\n\t\t\t\t\t\ttransforms.push( {\n\t\t\t\t\t\t\tsid: child.getAttribute( 'sid' ),\n\t\t\t\t\t\t\ttype: child.nodeName,\n\t\t\t\t\t\t\tobj: vector\n\t\t\t\t\t\t} );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'rotate':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tvector = new Vector3().fromArray( array );\n\t\t\t\t\t\tconst angle = MathUtils.degToRad( array[ 3 ] );\n\t\t\t\t\t\ttransforms.push( {\n\t\t\t\t\t\t\tsid: child.getAttribute( 'sid' ),\n\t\t\t\t\t\t\ttype: child.nodeName,\n\t\t\t\t\t\t\tobj: vector,\n\t\t\t\t\t\t\tangle: angle\n\t\t\t\t\t\t} );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn transforms;\n\n\t\t}\n\n\t\t// nodes\n\n\t\tfunction prepareNodes( xml ) {\n\n\t\t\tconst elements = xml.getElementsByTagName( 'node' );\n\n\t\t\t// ensure all node elements have id attributes\n\n\t\t\tfor ( let i = 0; i < elements.length; i ++ ) {\n\n\t\t\t\tconst element = elements[ i ];\n\n\t\t\t\tif ( element.hasAttribute( 'id' ) === false ) {\n\n\t\t\t\t\telement.setAttribute( 'id', generateId() );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst matrix = new Matrix4();\n\t\tconst vector = new Vector3();\n\n\t\tfunction parseNode( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' ) || '',\n\t\t\t\ttype: xml.getAttribute( 'type' ),\n\t\t\t\tid: xml.getAttribute( 'id' ),\n\t\t\t\tsid: xml.getAttribute( 'sid' ),\n\t\t\t\tmatrix: new Matrix4(),\n\t\t\t\tnodes: [],\n\t\t\t\tinstanceCameras: [],\n\t\t\t\tinstanceControllers: [],\n\t\t\t\tinstanceLights: [],\n\t\t\t\tinstanceGeometries: [],\n\t\t\t\tinstanceNodes: [],\n\t\t\t\ttransforms: {}\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tif ( child.nodeType !== 1 ) continue;\n\n\t\t\t\tlet array;\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'node':\n\t\t\t\t\t\tdata.nodes.push( child.getAttribute( 'id' ) );\n\t\t\t\t\t\tparseNode( child );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'instance_camera':\n\t\t\t\t\t\tdata.instanceCameras.push( parseId( child.getAttribute( 'url' ) ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'instance_controller':\n\t\t\t\t\t\tdata.instanceControllers.push( parseNodeInstance( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'instance_light':\n\t\t\t\t\t\tdata.instanceLights.push( parseId( child.getAttribute( 'url' ) ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'instance_geometry':\n\t\t\t\t\t\tdata.instanceGeometries.push( parseNodeInstance( child ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'instance_node':\n\t\t\t\t\t\tdata.instanceNodes.push( parseId( child.getAttribute( 'url' ) ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'matrix':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tdata.matrix.multiply( matrix.fromArray( array ).transpose() );\n\t\t\t\t\t\tdata.transforms[ child.getAttribute( 'sid' ) ] = child.nodeName;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'translate':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tvector.fromArray( array );\n\t\t\t\t\t\tdata.matrix.multiply( matrix.makeTranslation( vector.x, vector.y, vector.z ) );\n\t\t\t\t\t\tdata.transforms[ child.getAttribute( 'sid' ) ] = child.nodeName;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'rotate':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tconst angle = MathUtils.degToRad( array[ 3 ] );\n\t\t\t\t\t\tdata.matrix.multiply( matrix.makeRotationAxis( vector.fromArray( array ), angle ) );\n\t\t\t\t\t\tdata.transforms[ child.getAttribute( 'sid' ) ] = child.nodeName;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'scale':\n\t\t\t\t\t\tarray = parseFloats( child.textContent );\n\t\t\t\t\t\tdata.matrix.scale( vector.fromArray( array ) );\n\t\t\t\t\t\tdata.transforms[ child.getAttribute( 'sid' ) ] = child.nodeName;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'extra':\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tconsole.log( child );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( hasNode( data.id ) ) {\n\n\t\t\t\tconsole.warn( 'THREE.ColladaLoader: There is already a node with ID %s. Exclude current node from further processing.', data.id );\n\n\t\t\t} else {\n\n\t\t\t\tlibrary.nodes[ data.id ] = data;\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction parseNodeInstance( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tid: parseId( xml.getAttribute( 'url' ) ),\n\t\t\t\tmaterials: {},\n\t\t\t\tskeletons: []\n\t\t\t};\n\n\t\t\tfor ( let i = 0; i < xml.childNodes.length; i ++ ) {\n\n\t\t\t\tconst child = xml.childNodes[ i ];\n\n\t\t\t\tswitch ( child.nodeName ) {\n\n\t\t\t\t\tcase 'bind_material':\n\t\t\t\t\t\tconst instances = child.getElementsByTagName( 'instance_material' );\n\n\t\t\t\t\t\tfor ( let j = 0; j < instances.length; j ++ ) {\n\n\t\t\t\t\t\t\tconst instance = instances[ j ];\n\t\t\t\t\t\t\tconst symbol = instance.getAttribute( 'symbol' );\n\t\t\t\t\t\t\tconst target = instance.getAttribute( 'target' );\n\n\t\t\t\t\t\t\tdata.materials[ symbol ] = parseId( target );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'skeleton':\n\t\t\t\t\t\tdata.skeletons.push( parseId( child.textContent ) );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn data;\n\n\t\t}\n\n\t\tfunction buildSkeleton( skeletons, joints ) {\n\n\t\t\tconst boneData = [];\n\t\t\tconst sortedBoneData = [];\n\n\t\t\tlet i, j, data;\n\n\t\t\t// a skeleton can have multiple root bones. collada expresses this\n\t\t\t// situtation with multiple \"skeleton\" tags per controller instance\n\n\t\t\tfor ( i = 0; i < skeletons.length; i ++ ) {\n\n\t\t\t\tconst skeleton = skeletons[ i ];\n\n\t\t\t\tlet root;\n\n\t\t\t\tif ( hasNode( skeleton ) ) {\n\n\t\t\t\t\troot = getNode( skeleton );\n\t\t\t\t\tbuildBoneHierarchy( root, joints, boneData );\n\n\t\t\t\t} else if ( hasVisualScene( skeleton ) ) {\n\n\t\t\t\t\t// handle case where the skeleton refers to the visual scene (#13335)\n\n\t\t\t\t\tconst visualScene = library.visualScenes[ skeleton ];\n\t\t\t\t\tconst children = visualScene.children;\n\n\t\t\t\t\tfor ( let j = 0; j < children.length; j ++ ) {\n\n\t\t\t\t\t\tconst child = children[ j ];\n\n\t\t\t\t\t\tif ( child.type === 'JOINT' ) {\n\n\t\t\t\t\t\t\tconst root = getNode( child.id );\n\t\t\t\t\t\t\tbuildBoneHierarchy( root, joints, boneData );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( 'THREE.ColladaLoader: Unable to find root bone of skeleton with ID:', skeleton );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// sort bone data (the order is defined in the corresponding controller)\n\n\t\t\tfor ( i = 0; i < joints.length; i ++ ) {\n\n\t\t\t\tfor ( j = 0; j < boneData.length; j ++ ) {\n\n\t\t\t\t\tdata = boneData[ j ];\n\n\t\t\t\t\tif ( data.bone.name === joints[ i ].name ) {\n\n\t\t\t\t\t\tsortedBoneData[ i ] = data;\n\t\t\t\t\t\tdata.processed = true;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// add unprocessed bone data at the end of the list\n\n\t\t\tfor ( i = 0; i < boneData.length; i ++ ) {\n\n\t\t\t\tdata = boneData[ i ];\n\n\t\t\t\tif ( data.processed === false ) {\n\n\t\t\t\t\tsortedBoneData.push( data );\n\t\t\t\t\tdata.processed = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// setup arrays for skeleton creation\n\n\t\t\tconst bones = [];\n\t\t\tconst boneInverses = [];\n\n\t\t\tfor ( i = 0; i < sortedBoneData.length; i ++ ) {\n\n\t\t\t\tdata = sortedBoneData[ i ];\n\n\t\t\t\tbones.push( data.bone );\n\t\t\t\tboneInverses.push( data.boneInverse );\n\n\t\t\t}\n\n\t\t\treturn new Skeleton( bones, boneInverses );\n\n\t\t}\n\n\t\tfunction buildBoneHierarchy( root, joints, boneData ) {\n\n\t\t\t// setup bone data from visual scene\n\n\t\t\troot.traverse( function ( object ) {\n\n\t\t\t\tif ( object.isBone === true ) {\n\n\t\t\t\t\tlet boneInverse;\n\n\t\t\t\t\t// retrieve the boneInverse from the controller data\n\n\t\t\t\t\tfor ( let i = 0; i < joints.length; i ++ ) {\n\n\t\t\t\t\t\tconst joint = joints[ i ];\n\n\t\t\t\t\t\tif ( joint.name === object.name ) {\n\n\t\t\t\t\t\t\tboneInverse = joint.boneInverse;\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( boneInverse === undefined ) {\n\n\t\t\t\t\t\t// Unfortunately, there can be joints in the visual scene that are not part of the\n\t\t\t\t\t\t// corresponding controller. In this case, we have to create a dummy boneInverse matrix\n\t\t\t\t\t\t// for the respective bone. This bone won't affect any vertices, because there are no skin indices\n\t\t\t\t\t\t// and weights defined for it. But we still have to add the bone to the sorted bone list in order to\n\t\t\t\t\t\t// ensure a correct animation of the model.\n\n\t\t\t\t\t\tboneInverse = new Matrix4();\n\n\t\t\t\t\t}\n\n\t\t\t\t\tboneData.push( { bone: object, boneInverse: boneInverse, processed: false } );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t}\n\n\t\tfunction buildNode( data ) {\n\n\t\t\tconst objects = [];\n\n\t\t\tconst matrix = data.matrix;\n\t\t\tconst nodes = data.nodes;\n\t\t\tconst type = data.type;\n\t\t\tconst instanceCameras = data.instanceCameras;\n\t\t\tconst instanceControllers = data.instanceControllers;\n\t\t\tconst instanceLights = data.instanceLights;\n\t\t\tconst instanceGeometries = data.instanceGeometries;\n\t\t\tconst instanceNodes = data.instanceNodes;\n\n\t\t\t// nodes\n\n\t\t\tfor ( let i = 0, l = nodes.length; i < l; i ++ ) {\n\n\t\t\t\tobjects.push( getNode( nodes[ i ] ) );\n\n\t\t\t}\n\n\t\t\t// instance cameras\n\n\t\t\tfor ( let i = 0, l = instanceCameras.length; i < l; i ++ ) {\n\n\t\t\t\tconst instanceCamera = getCamera( instanceCameras[ i ] );\n\n\t\t\t\tif ( instanceCamera !== null ) {\n\n\t\t\t\t\tobjects.push( instanceCamera.clone() );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// instance controllers\n\n\t\t\tfor ( let i = 0, l = instanceControllers.length; i < l; i ++ ) {\n\n\t\t\t\tconst instance = instanceControllers[ i ];\n\t\t\t\tconst controller = getController( instance.id );\n\t\t\t\tconst geometries = getGeometry( controller.id );\n\t\t\t\tconst newObjects = buildObjects( geometries, instance.materials );\n\n\t\t\t\tconst skeletons = instance.skeletons;\n\t\t\t\tconst joints = controller.skin.joints;\n\n\t\t\t\tconst skeleton = buildSkeleton( skeletons, joints );\n\n\t\t\t\tfor ( let j = 0, jl = newObjects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tconst object = newObjects[ j ];\n\n\t\t\t\t\tif ( object.isSkinnedMesh ) {\n\n\t\t\t\t\t\tobject.bind( skeleton, controller.skin.bindMatrix );\n\t\t\t\t\t\tobject.normalizeSkinWeights();\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobjects.push( object );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// instance lights\n\n\t\t\tfor ( let i = 0, l = instanceLights.length; i < l; i ++ ) {\n\n\t\t\t\tconst instanceLight = getLight( instanceLights[ i ] );\n\n\t\t\t\tif ( instanceLight !== null ) {\n\n\t\t\t\t\tobjects.push( instanceLight.clone() );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// instance geometries\n\n\t\t\tfor ( let i = 0, l = instanceGeometries.length; i < l; i ++ ) {\n\n\t\t\t\tconst instance = instanceGeometries[ i ];\n\n\t\t\t\t// a single geometry instance in collada can lead to multiple object3Ds.\n\t\t\t\t// this is the case when primitives are combined like triangles and lines\n\n\t\t\t\tconst geometries = getGeometry( instance.id );\n\t\t\t\tconst newObjects = buildObjects( geometries, instance.materials );\n\n\t\t\t\tfor ( let j = 0, jl = newObjects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tobjects.push( newObjects[ j ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// instance nodes\n\n\t\t\tfor ( let i = 0, l = instanceNodes.length; i < l; i ++ ) {\n\n\t\t\t\tobjects.push( getNode( instanceNodes[ i ] ).clone() );\n\n\t\t\t}\n\n\t\t\tlet object;\n\n\t\t\tif ( nodes.length === 0 && objects.length === 1 ) {\n\n\t\t\t\tobject = objects[ 0 ];\n\n\t\t\t} else {\n\n\t\t\t\tobject = ( type === 'JOINT' ) ? new Bone() : new Group();\n\n\t\t\t\tfor ( let i = 0; i < objects.length; i ++ ) {\n\n\t\t\t\t\tobject.add( objects[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tobject.name = ( type === 'JOINT' ) ? data.sid : data.name;\n\t\t\tobject.matrix.copy( matrix );\n\t\t\tobject.matrix.decompose( object.position, object.quaternion, object.scale );\n\n\t\t\treturn object;\n\n\t\t}\n\n\t\tconst fallbackMaterial = new MeshBasicMaterial( {\n\t\t\tname: Loader.DEFAULT_MATERIAL_NAME,\n\t\t\tcolor: 0xff00ff\n\t\t} );\n\n\t\tfunction resolveMaterialBinding( keys, instanceMaterials ) {\n\n\t\t\tconst materials = [];\n\n\t\t\tfor ( let i = 0, l = keys.length; i < l; i ++ ) {\n\n\t\t\t\tconst id = instanceMaterials[ keys[ i ] ];\n\n\t\t\t\tif ( id === undefined ) {\n\n\t\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Material with key %s not found. Apply fallback material.', keys[ i ] );\n\t\t\t\t\tmaterials.push( fallbackMaterial );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tmaterials.push( getMaterial( id ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn materials;\n\n\t\t}\n\n\t\tfunction buildObjects( geometries, instanceMaterials ) {\n\n\t\t\tconst objects = [];\n\n\t\t\tfor ( const type in geometries ) {\n\n\t\t\t\tconst geometry = geometries[ type ];\n\n\t\t\t\tconst materials = resolveMaterialBinding( geometry.materialKeys, instanceMaterials );\n\n\t\t\t\t// handle case if no materials are defined\n\n\t\t\t\tif ( materials.length === 0 ) {\n\n\t\t\t\t\tif ( type === 'lines' || type === 'linestrips' ) {\n\n\t\t\t\t\t\tmaterials.push( new LineBasicMaterial() );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tmaterials.push( new MeshPhongMaterial() );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Collada allows to use phong and lambert materials with lines. Replacing these cases with LineBasicMaterial.\n\n\t\t\t\tif ( type === 'lines' || type === 'linestrips' ) {\n\n\t\t\t\t\tfor ( let i = 0, l = materials.length; i < l; i ++ ) {\n\n\t\t\t\t\t\tconst material = materials[ i ];\n\n\t\t\t\t\t\tif ( material.isMeshPhongMaterial === true || material.isMeshLambertMaterial === true ) {\n\n\t\t\t\t\t\t\tconst lineMaterial = new LineBasicMaterial();\n\n\t\t\t\t\t\t\t// copy compatible properties\n\n\t\t\t\t\t\t\tlineMaterial.color.copy( material.color );\n\t\t\t\t\t\t\tlineMaterial.opacity = material.opacity;\n\t\t\t\t\t\t\tlineMaterial.transparent = material.transparent;\n\n\t\t\t\t\t\t\t// replace material\n\n\t\t\t\t\t\t\tmaterials[ i ] = lineMaterial;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// regard skinning\n\n\t\t\t\tconst skinning = ( geometry.data.attributes.skinIndex !== undefined );\n\n\t\t\t\t// choose between a single or multi materials (material array)\n\n\t\t\t\tconst material = ( materials.length === 1 ) ? materials[ 0 ] : materials;\n\n\t\t\t\t// now create a specific 3D object\n\n\t\t\t\tlet object;\n\n\t\t\t\tswitch ( type ) {\n\n\t\t\t\t\tcase 'lines':\n\t\t\t\t\t\tobject = new LineSegments( geometry.data, material );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'linestrips':\n\t\t\t\t\t\tobject = new Line( geometry.data, material );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'triangles':\n\t\t\t\t\tcase 'polylist':\n\t\t\t\t\t\tif ( skinning ) {\n\n\t\t\t\t\t\t\tobject = new SkinnedMesh( geometry.data, material );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tobject = new Mesh( geometry.data, material );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tobjects.push( object );\n\n\t\t\t}\n\n\t\t\treturn objects;\n\n\t\t}\n\n\t\tfunction hasNode( id ) {\n\n\t\t\treturn library.nodes[ id ] !== undefined;\n\n\t\t}\n\n\t\tfunction getNode( id ) {\n\n\t\t\treturn getBuild( library.nodes[ id ], buildNode );\n\n\t\t}\n\n\t\t// visual scenes\n\n\t\tfunction parseVisualScene( xml ) {\n\n\t\t\tconst data = {\n\t\t\t\tname: xml.getAttribute( 'name' ),\n\t\t\t\tchildren: []\n\t\t\t};\n\n\t\t\tprepareNodes( xml );\n\n\t\t\tconst elements = getElementsByTagName( xml, 'node' );\n\n\t\t\tfor ( let i = 0; i < elements.length; i ++ ) {\n\n\t\t\t\tdata.children.push( parseNode( elements[ i ] ) );\n\n\t\t\t}\n\n\t\t\tlibrary.visualScenes[ xml.getAttribute( 'id' ) ] = data;\n\n\t\t}\n\n\t\tfunction buildVisualScene( data ) {\n\n\t\t\tconst group = new Group();\n\t\t\tgroup.name = data.name;\n\n\t\t\tconst children = data.children;\n\n\t\t\tfor ( let i = 0; i < children.length; i ++ ) {\n\n\t\t\t\tconst child = children[ i ];\n\n\t\t\t\tgroup.add( getNode( child.id ) );\n\n\t\t\t}\n\n\t\t\treturn group;\n\n\t\t}\n\n\t\tfunction hasVisualScene( id ) {\n\n\t\t\treturn library.visualScenes[ id ] !== undefined;\n\n\t\t}\n\n\t\tfunction getVisualScene( id ) {\n\n\t\t\treturn getBuild( library.visualScenes[ id ], buildVisualScene );\n\n\t\t}\n\n\t\t// scenes\n\n\t\tfunction parseScene( xml ) {\n\n\t\t\tconst instance = getElementsByTagName( xml, 'instance_visual_scene' )[ 0 ];\n\t\t\treturn getVisualScene( parseId( instance.getAttribute( 'url' ) ) );\n\n\t\t}\n\n\t\tfunction setupAnimations() {\n\n\t\t\tconst clips = library.clips;\n\n\t\t\tif ( isEmpty( clips ) === true ) {\n\n\t\t\t\tif ( isEmpty( library.animations ) === false ) {\n\n\t\t\t\t\t// if there are animations but no clips, we create a default clip for playback\n\n\t\t\t\t\tconst tracks = [];\n\n\t\t\t\t\tfor ( const id in library.animations ) {\n\n\t\t\t\t\t\tconst animationTracks = getAnimation( id );\n\n\t\t\t\t\t\tfor ( let i = 0, l = animationTracks.length; i < l; i ++ ) {\n\n\t\t\t\t\t\t\ttracks.push( animationTracks[ i ] );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tanimations.push( new AnimationClip( 'default', - 1, tracks ) );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tfor ( const id in clips ) {\n\n\t\t\t\t\tanimations.push( getAnimationClip( id ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// convert the parser error element into text with each child elements text\n\t\t// separated by new lines.\n\n\t\tfunction parserErrorToText( parserError ) {\n\n\t\t\tlet result = '';\n\t\t\tconst stack = [ parserError ];\n\n\t\t\twhile ( stack.length ) {\n\n\t\t\t\tconst node = stack.shift();\n\n\t\t\t\tif ( node.nodeType === Node.TEXT_NODE ) {\n\n\t\t\t\t\tresult += node.textContent;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tresult += '\\n';\n\t\t\t\t\tstack.push.apply( stack, node.childNodes );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn result.trim();\n\n\t\t}\n\n\t\tif ( text.length === 0 ) {\n\n\t\t\treturn { scene: new Scene() };\n\n\t\t}\n\n\t\tconst xml = new DOMParser().parseFromString( text, 'application/xml' );\n\n\t\tconst collada = getElementsByTagName( xml, 'COLLADA' )[ 0 ];\n\n\t\tconst parserError = xml.getElementsByTagName( 'parsererror' )[ 0 ];\n\t\tif ( parserError !== undefined ) {\n\n\t\t\t// Chrome will return parser error with a div in it\n\n\t\t\tconst errorElement = getElementsByTagName( parserError, 'div' )[ 0 ];\n\t\t\tlet errorText;\n\n\t\t\tif ( errorElement ) {\n\n\t\t\t\terrorText = errorElement.textContent;\n\n\t\t\t} else {\n\n\t\t\t\terrorText = parserErrorToText( parserError );\n\n\t\t\t}\n\n\t\t\tconsole.error( 'THREE.ColladaLoader: Failed to parse collada file.\\n', errorText );\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// metadata\n\n\t\tconst version = collada.getAttribute( 'version' );\n\t\tconsole.debug( 'THREE.ColladaLoader: File version', version );\n\n\t\tconst asset = parseAsset( getElementsByTagName( collada, 'asset' )[ 0 ] );\n\t\tconst textureLoader = new TextureLoader( this.manager );\n\t\ttextureLoader.setPath( this.resourcePath || path ).setCrossOrigin( this.crossOrigin );\n\n\t\tlet tgaLoader;\n\n\t\tif ( TGALoader ) {\n\n\t\t\ttgaLoader = new TGALoader( this.manager );\n\t\t\ttgaLoader.setPath( this.resourcePath || path );\n\n\t\t}\n\n\t\t//\n\n\t\tconst tempColor = new Color();\n\t\tconst animations = [];\n\t\tlet kinematics = {};\n\t\tlet count = 0;\n\n\t\t//\n\n\t\tconst library = {\n\t\t\tanimations: {},\n\t\t\tclips: {},\n\t\t\tcontrollers: {},\n\t\t\timages: {},\n\t\t\teffects: {},\n\t\t\tmaterials: {},\n\t\t\tcameras: {},\n\t\t\tlights: {},\n\t\t\tgeometries: {},\n\t\t\tnodes: {},\n\t\t\tvisualScenes: {},\n\t\t\tkinematicsModels: {},\n\t\t\tphysicsModels: {},\n\t\t\tkinematicsScenes: {}\n\t\t};\n\n\t\tparseLibrary( collada, 'library_animations', 'animation', parseAnimation );\n\t\tparseLibrary( collada, 'library_animation_clips', 'animation_clip', parseAnimationClip );\n\t\tparseLibrary( collada, 'library_controllers', 'controller', parseController );\n\t\tparseLibrary( collada, 'library_images', 'image', parseImage );\n\t\tparseLibrary( collada, 'library_effects', 'effect', parseEffect );\n\t\tparseLibrary( collada, 'library_materials', 'material', parseMaterial );\n\t\tparseLibrary( collada, 'library_cameras', 'camera', parseCamera );\n\t\tparseLibrary( collada, 'library_lights', 'light', parseLight );\n\t\tparseLibrary( collada, 'library_geometries', 'geometry', parseGeometry );\n\t\tparseLibrary( collada, 'library_nodes', 'node', parseNode );\n\t\tparseLibrary( collada, 'library_visual_scenes', 'visual_scene', parseVisualScene );\n\t\tparseLibrary( collada, 'library_kinematics_models', 'kinematics_model', parseKinematicsModel );\n\t\tparseLibrary( collada, 'library_physics_models', 'physics_model', parsePhysicsModel );\n\t\tparseLibrary( collada, 'scene', 'instance_kinematics_scene', parseKinematicsScene );\n\n\t\tbuildLibrary( library.animations, buildAnimation );\n\t\tbuildLibrary( library.clips, buildAnimationClip );\n\t\tbuildLibrary( library.controllers, buildController );\n\t\tbuildLibrary( library.images, buildImage );\n\t\tbuildLibrary( library.effects, buildEffect );\n\t\tbuildLibrary( library.materials, buildMaterial );\n\t\tbuildLibrary( library.cameras, buildCamera );\n\t\tbuildLibrary( library.lights, buildLight );\n\t\tbuildLibrary( library.geometries, buildGeometry );\n\t\tbuildLibrary( library.visualScenes, buildVisualScene );\n\n\t\tsetupAnimations();\n\t\tsetupKinematics();\n\n\t\tconst scene = parseScene( getElementsByTagName( collada, 'scene' )[ 0 ] );\n\t\tscene.animations = animations;\n\n\t\tif ( asset.upAxis === 'Z_UP' ) {\n\n\t\t\tconsole.warn( 'THREE.ColladaLoader: You are loading an asset with a Z-UP coordinate system. The loader just rotates the asset to transform it into Y-UP. The vertex data are not converted, see #24289.' );\n\t\t\tscene.rotation.set( - Math.PI / 2, 0, 0 );\n\n\t\t}\n\n\t\tscene.scale.multiplyScalar( asset.unit );\n\n\t\treturn {\n\t\t\tget animations() {\n\n\t\t\t\tconsole.warn( 'THREE.ColladaLoader: Please access animations over scene.animations now.' );\n\t\t\t\treturn animations;\n\n\t\t\t},\n\t\t\tkinematics: kinematics,\n\t\t\tlibrary: library,\n\t\t\tscene: scene\n\t\t};\n\n\t}\n\n}\n\nexport { ColladaLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,YAAN,cAAwB,kBAAkB;AAAA,EAEzC,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAAA,EAEhB;AAAA,EAEA,MAAO,QAAS;AAIf,aAAS,eAAgBA,SAAS;AAEjC,cAASA,QAAO,YAAa;AAAA,QAI5B,KAAK;AAAA,QACL,KAAK;AACJ,cAAKA,QAAO,kBAAkB,OAAOA,QAAO,kBAAkB,MAAMA,QAAO,kBAAkB,GAAI;AAEhG,kBAAM,IAAI,MAAO,+DAAgE;AAAA,UAElF;AAEA;AAAA,QAID,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ,cAAKA,QAAO,eAAgB;AAE3B,kBAAM,IAAI,MAAO,gEAAiE;AAAA,UAEnF;AAEA;AAAA,QAID,KAAK;AACJ,gBAAM,IAAI,MAAO,2BAA4B;AAAA,QAI9C;AACC,gBAAM,IAAI,MAAO,mCAAmCA,QAAO,UAAW;AAAA,MAExE;AAIA,UAAKA,QAAO,SAAS,KAAKA,QAAO,UAAU,GAAI;AAE9C,cAAM,IAAI,MAAO,sCAAuC;AAAA,MAEzD;AAIA,UAAKA,QAAO,eAAe,KAAKA,QAAO,eAAe,MACrDA,QAAO,eAAe,MAAMA,QAAO,eAAe,IAAK;AAEvD,cAAM,IAAI,MAAO,yCAAyCA,QAAO,UAAW;AAAA,MAE7E;AAAA,IAED;AAIA,aAAS,SAAUC,UAASC,UAASF,SAAQG,SAAQ,MAAO;AAE3D,UAAI,YACH;AAED,YAAM,aAAaH,QAAO,cAAc;AACxC,YAAM,cAAcA,QAAO,QAAQA,QAAO,SAAS;AAIlD,UAAKE,UAAU;AAEd,mBAAW,KAAK,SAAUC,SAAQA,WAAUH,QAAO,mBAAoBA,QAAO,iBAAiB,EAAI;AAAA,MAEpG;AAIA,UAAKC,UAAU;AAEd,qBAAa,IAAI,WAAY,WAAY;AAE1C,YAAI,GAAG,OAAO;AACd,YAAI,QAAQ;AACZ,cAAM,SAAS,IAAI,WAAY,UAAW;AAE1C,eAAQ,QAAQ,aAAc;AAE7B,cAAI,KAAME,SAAU;AACpB,mBAAU,IAAI,OAAS;AAIvB,cAAK,IAAI,KAAO;AAIf,iBAAM,IAAI,GAAG,IAAI,YAAY,EAAG,GAAI;AAEnC,qBAAQ,CAAE,IAAI,KAAMA,SAAU;AAAA,YAE/B;AAIA,iBAAM,IAAI,GAAG,IAAI,OAAO,EAAG,GAAI;AAE9B,yBAAW,IAAK,QAAQ,QAAQ,IAAI,UAAW;AAAA,YAEhD;AAEA,qBAAS,aAAa;AAAA,UAEvB,OAAO;AAIN,qBAAS;AAET,iBAAM,IAAI,GAAG,IAAI,OAAO,EAAG,GAAI;AAE9B,yBAAY,QAAQ,CAAE,IAAI,KAAMA,SAAU;AAAA,YAE3C;AAEA,qBAAS;AAAA,UAEV;AAAA,QAED;AAAA,MAEA,OAAO;AAIP,qBAAa,KAAK;AAAA,UAChBA;AAAA,UAAQA,WAAYD,WAAUF,QAAO,QAAQA,QAAO,SAAS;AAAA,QAC/D;AAAA,MAEA;AAEA,aAAO;AAAA,QACP;AAAA,QACA;AAAA,MACA;AAAA,IAEF;AAEA,aAAS,qBAAsBI,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO,UAAW;AAE3G,YAAM,WAAW;AACjB,UAAI,OAAO,IAAI,GAAG,GAAG;AACrB,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAO;AAEnD,kBAAQ,MAAO,CAAE;AACjB,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AACzC,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,SAAY,QAAQ,IAAM,CAAE;AACrE,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,SAAY,QAAQ,IAAM,CAAE;AACrE,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,SAAY,QAAQ,IAAM,CAAE;AAAA,QAEtE;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,sBAAuBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAQ;AAElG,UAAI,OAAO,IAAI,GAAG,GAAG;AACrB,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAI;AAErD,kBAAQ,MAAO,IAAI,CAAE,KAAM,MAAO,IAAI,CAAE,KAAK;AAC7C,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,KAAM,QAAQ,UAAY;AAC/D,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,KAAM,QAAQ,QAAY;AAC/D,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,KAAM,QAAQ,OAAY;AAC/D,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAM,QAAQ,QAAW,IAAI;AAAA,QAEnE;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,sBAAuBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAQ;AAElG,UAAI,IAAI,GAAG,GAAG;AACd,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAI;AAErD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AACzC,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAAA,QAEvD;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,sBAAuBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAQ;AAElG,UAAI,IAAI,GAAG,GAAG;AACd,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAI;AAErD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAAA,QAEvD;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,yBAA0BA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAQ;AAErG,UAAI,OAAO,IAAI,GAAG,GAAG;AACrB,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAO;AAEnD,kBAAQ,MAAO,CAAE;AACjB,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AACzC,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AACzC,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AACzC,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI;AAAA,QAE1C;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,0BAA2BA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAQ;AAEtG,UAAI,IAAI,GAAG,GAAG;AACd,YAAM,QAAQ,OAAO;AAErB,WAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAS;AAE7C,aAAM,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAI;AAErD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACtD,UAAAA,YAAa,IAAI,QAAQ,KAAM,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAAA,QAEvD;AAAA,MAED;AAEA,aAAOA;AAAA,IAER;AAEA,aAAS,WAAY,MAAM,OAAO,QAAQ,OAAO,SAAU;AAE1D,UAAI,SACH,SACA,QACA,QACA,OACA;AAED,eAAW,OAAO,QAAQ,oBAAqB,kBAAmB;AAAA,QAEjE;AAAA,QACA,KAAK;AACJ,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR;AAAA,QAED,KAAK;AACJ,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR,oBAAU,SAAS;AACnB,mBAAS;AACT,kBAAQ;AACR;AAAA,QAED,KAAK;AACJ,oBAAU,QAAQ;AAClB,mBAAS;AACT,kBAAQ;AACR,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR;AAAA,QAED,KAAK;AACJ,oBAAU,QAAQ;AAClB,mBAAS;AACT,kBAAQ;AACR,oBAAU,SAAS;AACnB,mBAAS;AACT,kBAAQ;AACR;AAAA,MAEF;AAEA,UAAK,UAAW;AAEf,gBAAS,OAAO,YAAa;AAAA,UAE5B,KAAK;AACJ,qCAA0B,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAM;AACtF;AAAA,UAED,KAAK;AACJ,sCAA2B,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAM;AACvF;AAAA,UAED;AACC,kBAAM,IAAI,MAAO,wCAAyC;AAC1D;AAAA,QAEF;AAAA,MAED,OAAO;AAEN,gBAAS,OAAO,YAAa;AAAA,UAE5B,KAAK;AACJ,iCAAsB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO,OAAQ;AAC3F;AAAA,UAED,KAAK;AACJ,kCAAuB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAM;AACnF;AAAA,UAED,KAAK;AACJ,kCAAuB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAM;AACnF;AAAA,UAED,KAAK;AACJ,kCAAuB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAM;AACnF;AAAA,UAED;AACC,kBAAM,IAAI,MAAO,wCAAyC;AAC1D;AAAA,QAEF;AAAA,MAED;AAKA,aAAO;AAAA,IAER;AAIA,UAAM,mBAAmB,GACxB,mBAAmB,GACnB,eAAe,GACf,gBAAgB,GAChB,uBAAuB,GACvB,mBAAmB,IACnB,oBAAoB,IAEpB,kBAAkB,IAClB,mBAAmB,GACnB,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB;AAEjB,QAAK,OAAO,SAAS,GAAK,OAAM,IAAI,MAAO,qDAAsD;AAEjG,QAAI,SAAS;AAEb,UAAM,UAAU,IAAI,WAAY,MAAO,GACtC,SAAS;AAAA,MACR,WAAW,QAAS,QAAU;AAAA,MAC9B,eAAe,QAAS,QAAU;AAAA,MAClC,YAAY,QAAS,QAAU;AAAA,MAC/B,gBAAgB,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,MAC/D,iBAAiB,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,MAChE,eAAe,QAAS,QAAU;AAAA,MAClC,QAAQ;AAAA,QACP,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,QAC/C,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,MAChD;AAAA,MACA,OAAO,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,MACtD,QAAQ,QAAS,QAAU,IAAI,QAAS,QAAU,KAAK;AAAA,MACvD,YAAY,QAAS,QAAU;AAAA,MAC/B,OAAO,QAAS,QAAU;AAAA,IAC3B;AAID,mBAAgB,MAAO;AAEvB,QAAK,OAAO,YAAY,SAAS,OAAO,QAAS;AAEhD,YAAM,IAAI,MAAO,2BAA4B;AAAA,IAE9C;AAIA,cAAU,OAAO;AAIjB,QAAI,UAAU,OACb,UAAU,OACV,WAAW;AAEZ,YAAS,OAAO,YAAa;AAAA,MAE5B,KAAK;AACJ,kBAAU;AACV,kBAAU;AACV;AAAA,MAED,KAAK;AACJ,kBAAU;AACV;AAAA,MAED,KAAK;AACJ,kBAAU;AACV;AAAA,MAED,KAAK;AACJ;AAAA,MAED,KAAK;AACJ,kBAAU;AACV,mBAAW;AACX;AAAA,MAED,KAAK;AACJ,mBAAW;AACX;AAAA,IAEF;AAIA,UAAM,YAAY,IAAI,WAAY,OAAO,QAAQ,OAAO,SAAS,CAAE;AACnE,UAAM,SAAS,SAAU,SAAS,SAAS,QAAQ,QAAQ,OAAQ;AACnE,eAAY,WAAW,OAAO,OAAO,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAS;AAEvF,WAAO;AAAA,MAEN,MAAM;AAAA,MACN,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,MACf,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAW;AAAA,IAEZ;AAAA,EAED;AAED;;;ACxdA,IAAM,gBAAN,cAA4B,OAAO;AAAA,EAElC,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,OAAS,MAAM,SAAS,KAAO,YAAY,eAAgB,GAAI,IAAI,MAAM;AAE/E,UAAM,SAAS,IAAI,WAAY,MAAM,OAAQ;AAC7C,WAAO,QAAS,MAAM,IAAK;AAC3B,WAAO,iBAAkB,MAAM,aAAc;AAC7C,WAAO,mBAAoB,MAAM,eAAgB;AACjD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,eAAQ,MAAM,MAAO,MAAM,IAAK,CAAE;AAAA,MAEnC,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,MAAO,MAAM,MAAO;AAEnB,aAAS,qBAAsBC,MAAK,MAAO;AAI1C,YAAM,QAAQ,CAAC;AACf,YAAM,aAAaA,KAAI;AAEvB,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAErD,cAAM,QAAQ,WAAY,CAAE;AAE5B,YAAK,MAAM,aAAa,MAAO;AAE9B,gBAAM,KAAM,KAAM;AAAA,QAEnB;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,aAAcC,OAAO;AAE7B,UAAKA,MAAK,WAAW,EAAI,QAAO,CAAC;AAEjC,YAAM,QAAQA,MAAK,KAAK,EAAE,MAAO,KAAM;AACvC,YAAM,QAAQ,IAAI,MAAO,MAAM,MAAO;AAEtC,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,cAAO,CAAE,IAAI,MAAO,CAAE;AAAA,MAEvB;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAaA,OAAO;AAE5B,UAAKA,MAAK,WAAW,EAAI,QAAO,CAAC;AAEjC,YAAM,QAAQA,MAAK,KAAK,EAAE,MAAO,KAAM;AACvC,YAAM,QAAQ,IAAI,MAAO,MAAM,MAAO;AAEtC,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,cAAO,CAAE,IAAI,WAAY,MAAO,CAAE,CAAE;AAAA,MAErC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,UAAWA,OAAO;AAE1B,UAAKA,MAAK,WAAW,EAAI,QAAO,CAAC;AAEjC,YAAM,QAAQA,MAAK,KAAK,EAAE,MAAO,KAAM;AACvC,YAAM,QAAQ,IAAI,MAAO,MAAM,MAAO;AAEtC,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,cAAO,CAAE,IAAI,SAAU,MAAO,CAAE,CAAE;AAAA,MAEnC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,QAASA,OAAO;AAExB,aAAOA,MAAK,UAAW,CAAE;AAAA,IAE1B;AAEA,aAAS,aAAa;AAErB,aAAO,mBAAqB;AAAA,IAE7B;AAEA,aAAS,QAAS,QAAS;AAE1B,aAAO,OAAO,KAAM,MAAO,EAAE,WAAW;AAAA,IAEzC;AAIA,aAAS,WAAYD,MAAM;AAE1B,aAAO;AAAA,QACN,MAAM,eAAgB,qBAAsBA,MAAK,MAAO,EAAG,CAAE,CAAE;AAAA,QAC/D,QAAQ,iBAAkB,qBAAsBA,MAAK,SAAU,EAAG,CAAE,CAAE;AAAA,MACvE;AAAA,IAED;AAEA,aAAS,eAAgBA,MAAM;AAE9B,UAAOA,SAAQ,UAAiBA,KAAI,aAAc,OAAQ,MAAM,MAAS;AAExE,eAAO,WAAYA,KAAI,aAAc,OAAQ,CAAE;AAAA,MAEhD,OAAO;AAEN,eAAO;AAAA,MAER;AAAA,IAED;AAEA,aAAS,iBAAkBA,MAAM;AAEhC,aAAOA,SAAQ,SAAYA,KAAI,cAAc;AAAA,IAE9C;AAIA,aAAS,aAAcA,MAAK,aAAa,UAAU,QAAS;AAE3D,YAAME,WAAU,qBAAsBF,MAAK,WAAY,EAAG,CAAE;AAE5D,UAAKE,aAAY,QAAY;AAE5B,cAAM,WAAW,qBAAsBA,UAAS,QAAS;AAEzD,iBAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,iBAAQ,SAAU,CAAE,CAAE;AAAA,QAEvB;AAAA,MAED;AAAA,IAED;AAEA,aAAS,aAAc,MAAM,SAAU;AAEtC,iBAAY,QAAQ,MAAO;AAE1B,cAAM,SAAS,KAAM,IAAK;AAC1B,eAAO,QAAQ,QAAS,KAAM,IAAK,CAAE;AAAA,MAEtC;AAAA,IAED;AAIA,aAAS,SAAU,MAAM,SAAU;AAElC,UAAK,KAAK,UAAU,OAAY,QAAO,KAAK;AAE5C,WAAK,QAAQ,QAAS,IAAK;AAE3B,aAAO,KAAK;AAAA,IAEb;AAIA,aAAS,eAAgBF,MAAM;AAE9B,YAAM,OAAO;AAAA,QACZ,SAAS,CAAC;AAAA,QACV,UAAU,CAAC;AAAA,QACX,UAAU,CAAC;AAAA,MACZ;AAEA,UAAI,cAAc;AAElB,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,YAAI;AAEJ,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,MAAM,aAAc,IAAK;AAC9B,iBAAK,QAAS,EAAG,IAAI,YAAa,KAAM;AACxC;AAAA,UAED,KAAK;AACJ,iBAAK,MAAM,aAAc,IAAK;AAC9B,iBAAK,SAAU,EAAG,IAAI,sBAAuB,KAAM;AACnD;AAAA,UAED,KAAK;AACJ,iBAAK,MAAM,aAAc,QAAS;AAClC,iBAAK,SAAU,EAAG,IAAI,sBAAuB,KAAM;AACnD;AAAA,UAED,KAAK;AAEJ,2BAAgB,KAAM;AACtB,0BAAc;AACd;AAAA,UAED;AACC,oBAAQ,IAAK,KAAM;AAAA,QAErB;AAAA,MAED;AAEA,UAAK,gBAAgB,OAAQ;AAI5B,gBAAQ,WAAYA,KAAI,aAAc,IAAK,KAAK,UAAU,aAAa,CAAE,IAAI;AAAA,MAE9E;AAAA,IAED;AAEA,aAAS,sBAAuBA,MAAM;AAErC,YAAM,OAAO;AAAA,QACZ,QAAQ,CAAC;AAAA,MACV;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AACnD,kBAAM,WAAW,MAAM,aAAc,UAAW;AAChD,iBAAK,OAAQ,QAAS,IAAI;AAC1B;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,sBAAuBA,MAAM;AAErC,YAAM,OAAO,CAAC;AAEd,YAAM,SAASA,KAAI,aAAc,QAAS;AAI1C,UAAI,QAAQ,OAAO,MAAO,GAAI;AAE9B,YAAM,KAAK,MAAM,MAAM;AACvB,UAAI,MAAM,MAAM,MAAM;AAItB,YAAM,cAAgB,IAAI,QAAS,GAAI,MAAM;AAC7C,YAAM,eAAiB,IAAI,QAAS,GAAI,MAAM;AAE9C,UAAK,cAAe;AAInB,gBAAQ,IAAI,MAAO,GAAI;AACvB,cAAM,MAAM,MAAM;AAClB,aAAK,SAAS,MAAM,MAAM;AAAA,MAE3B,WAAY,aAAc;AAIzB,cAAM,UAAU,IAAI,MAAO,GAAI;AAC/B,cAAM,QAAQ,MAAM;AAEpB,iBAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAO;AAE3C,kBAAS,CAAE,IAAI,SAAU,QAAS,CAAE,EAAE,QAAS,MAAM,EAAG,CAAE;AAAA,QAE3D;AAEA,aAAK,UAAU;AAAA,MAEhB;AAEA,WAAK,KAAK;AACV,WAAK,MAAM;AAEX,WAAK,cAAc;AACnB,WAAK,eAAe;AAEpB,WAAK,UAAU,QAASA,KAAI,aAAc,QAAS,CAAE;AAErD,aAAO;AAAA,IAER;AAEA,aAAS,eAAgB,MAAO;AAE/B,YAAM,SAAS,CAAC;AAEhB,YAAM,WAAW,KAAK;AACtB,YAAM,WAAW,KAAK;AACtB,YAAM,UAAU,KAAK;AAErB,iBAAY,UAAU,UAAW;AAEhC,YAAK,SAAS,eAAgB,MAAO,GAAI;AAExC,gBAAM,UAAU,SAAU,MAAO;AACjC,gBAAM,UAAU,SAAU,QAAQ,OAAQ;AAE1C,gBAAM,UAAU,QAAQ,OAAO;AAC/B,gBAAM,WAAW,QAAQ,OAAO;AAEhC,gBAAM,cAAc,QAAS,OAAQ;AACrC,gBAAM,eAAe,QAAS,QAAS;AAEvC,gBAAM,YAAY,sBAAuB,SAAS,aAAa,YAAa;AAE5E,+BAAsB,WAAW,MAAO;AAAA,QAEzC;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,aAAc,IAAK;AAE3B,aAAO,SAAU,QAAQ,WAAY,EAAG,GAAG,cAAe;AAAA,IAE3D;AAEA,aAAS,sBAAuB,SAAS,aAAa,cAAe;AAEpE,YAAM,OAAO,QAAQ,MAAO,QAAQ,EAAG;AACvC,YAAM,WAAW,QAAS,KAAK,EAAG;AAElC,YAAM,YAAY,KAAK,WAAY,QAAQ,GAAI;AAC/C,YAAM,gBAAgB,KAAK,OAAO,MAAM,EAAE,UAAU;AAEpD,UAAI,MAAM;AACV,UAAI,GAAG,IAAI,GAAG;AAEd,YAAM,OAAO,CAAC;AAKd,cAAS,WAAY;AAAA,QAEpB,KAAK;AAEJ,eAAM,IAAI,GAAG,KAAK,YAAY,MAAM,QAAQ,IAAI,IAAI,KAAO;AAE1D,mBAAO,YAAY,MAAO,CAAE;AAC5B,qBAAS,IAAI,aAAa;AAE1B,gBAAK,KAAM,IAAK,MAAM,OAAY,MAAM,IAAK,IAAI,CAAC;AAElD,gBAAK,QAAQ,gBAAgB,MAAO;AAEnC,oBAAM,QAAQ,aAAa,MAAO,MAAO;AACzC,oBAAM,QAAQ,QAAQ,QAAS,CAAE,IAAI,IAAI,QAAQ,QAAS,CAAE;AAE5D,mBAAM,IAAK,EAAG,KAAM,IAAI;AAAA,YAEzB,OAAO;AAEN,mBAAM,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAO;AAErD,qBAAM,IAAK,EAAG,CAAE,IAAI,aAAa,MAAO,SAAS,CAAE;AAAA,cAEpD;AAAA,YAED;AAAA,UAED;AAEA;AAAA,QAED,KAAK;AACJ,kBAAQ,KAAM,2EAA2E,SAAU;AACnG;AAAA,QAED,KAAK;AACJ,kBAAQ,KAAM,2EAA2E,SAAU;AACnG;AAAA,QAED,KAAK;AACJ,kBAAQ,KAAM,2EAA2E,SAAU;AACnG;AAAA,MAEF;AAEA,YAAM,YAAY,qBAAsB,MAAM,aAAc;AAE5D,YAAM,YAAY;AAAA,QACjB,MAAM,SAAS;AAAA,QACf;AAAA,MACD;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsB,MAAM,eAAgB;AAEpD,YAAM,YAAY,CAAC;AAInB,iBAAY,QAAQ,MAAO;AAE1B,kBAAU,KAAM,EAAE,MAAM,WAAY,IAAK,GAAG,OAAO,KAAM,IAAK,EAAE,CAAE;AAAA,MAEnE;AAIA,gBAAU,KAAM,SAAU;AAI1B,eAAU,IAAI,GAAG,IAAI,IAAI,KAAO;AAE/B,+BAAwB,WAAW,GAAG,cAAc,SAAU,CAAE,CAAE;AAAA,MAEnE;AAEA,aAAO;AAIP,eAAS,UAAW,GAAG,GAAI;AAE1B,eAAO,EAAE,OAAO,EAAE;AAAA,MAEnB;AAAA,IAED;AAEA,UAAM,WAAW,IAAI,QAAQ;AAC7B,UAAM,QAAQ,IAAI,QAAQ;AAC1B,UAAM,aAAa,IAAI,WAAW;AAElC,aAAS,qBAAsB,WAAW,QAAS;AAElD,YAAM,YAAY,UAAU;AAC5B,YAAM,OAAO,UAAU;AAEvB,YAAM,QAAQ,CAAC;AACf,YAAM,eAAe,CAAC;AACtB,YAAM,iBAAiB,CAAC;AACxB,YAAM,YAAY,CAAC;AAEnB,eAAU,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAO;AAEpD,cAAM,WAAW,UAAW,CAAE;AAE9B,cAAM,OAAO,SAAS;AACtB,cAAM,QAAQ,SAAS;AAEvB,eAAO,UAAW,KAAM,EAAE,UAAU;AACpC,eAAO,UAAW,UAAU,YAAY,KAAM;AAE9C,cAAM,KAAM,IAAK;AACjB,qBAAa,KAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAE;AACtD,uBAAe,KAAM,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,CAAE;AAC5E,kBAAU,KAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAE;AAAA,MAE3C;AAEA,UAAK,aAAa,SAAS,EAAI,QAAO,KAAM,IAAI,oBAAqB,OAAO,aAAa,OAAO,YAAa,CAAE;AAC/G,UAAK,eAAe,SAAS,EAAI,QAAO,KAAM,IAAI,wBAAyB,OAAO,eAAe,OAAO,cAAe,CAAE;AACzH,UAAK,UAAU,SAAS,EAAI,QAAO,KAAM,IAAI,oBAAqB,OAAO,UAAU,OAAO,SAAU,CAAE;AAEtG,aAAO;AAAA,IAER;AAEA,aAAS,uBAAwB,WAAW,UAAU,cAAe;AAEpE,UAAI;AAEJ,UAAI,QAAQ;AACZ,UAAI,GAAG;AAIP,WAAM,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAO;AAEhD,mBAAW,UAAW,CAAE;AAExB,YAAK,SAAS,MAAO,QAAS,MAAM,QAAY;AAE/C,mBAAS,MAAO,QAAS,IAAI;AAAA,QAE9B,OAAO;AAEN,kBAAQ;AAAA,QAET;AAAA,MAED;AAEA,UAAK,UAAU,MAAO;AAIrB,aAAM,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAO;AAEhD,qBAAW,UAAW,CAAE;AAExB,mBAAS,MAAO,QAAS,IAAI;AAAA,QAE9B;AAAA,MAED,OAAO;AAIN,+BAAwB,WAAW,QAAS;AAAA,MAE7C;AAAA,IAED;AAEA,aAAS,uBAAwB,WAAW,UAAW;AAEtD,UAAI,MAAM;AAEV,eAAU,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAO;AAEpD,cAAM,WAAW,UAAW,CAAE;AAE9B,YAAK,SAAS,MAAO,QAAS,MAAM,MAAO;AAE1C,iBAAO,QAAS,WAAW,GAAG,QAAS;AACvC,iBAAO,QAAS,WAAW,GAAG,QAAS;AAEvC,cAAK,SAAS,MAAO;AAEpB,qBAAS,MAAO,QAAS,IAAI,KAAK,MAAO,QAAS;AAClD;AAAA,UAED;AAEA,cAAK,SAAS,MAAO;AAEpB,qBAAS,MAAO,QAAS,IAAI,KAAK,MAAO,QAAS;AAClD;AAAA,UAED;AAEA,sBAAa,UAAU,MAAM,MAAM,QAAS;AAAA,QAE7C;AAAA,MAED;AAAA,IAED;AAEA,aAAS,QAAS,WAAW,GAAG,UAAW;AAE1C,aAAQ,KAAK,GAAI;AAEhB,cAAM,WAAW,UAAW,CAAE;AAE9B,YAAK,SAAS,MAAO,QAAS,MAAM,KAAO,QAAO;AAElD;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,QAAS,WAAW,GAAG,UAAW;AAE1C,aAAQ,IAAI,UAAU,QAAS;AAE9B,cAAM,WAAW,UAAW,CAAE;AAE9B,YAAK,SAAS,MAAO,QAAS,MAAM,KAAO,QAAO;AAElD;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAa,KAAK,MAAM,MAAM,UAAW;AAEjD,UAAO,KAAK,OAAO,KAAK,SAAW,GAAI;AAEtC,YAAI,MAAO,QAAS,IAAI,KAAK,MAAO,QAAS;AAC7C;AAAA,MAED;AAEA,UAAI,MAAO,QAAS,KAAQ,IAAI,OAAO,KAAK,SAAW,KAAK,MAAO,QAAS,IAAI,KAAK,MAAO,QAAS,MAAQ,KAAK,OAAO,KAAK,QAAW,KAAK,MAAO,QAAS;AAAA,IAE/J;AAIA,aAAS,mBAAoBA,MAAM;AAElC,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,IAAK,KAAK;AAAA,QAClC,OAAO,WAAYA,KAAI,aAAc,OAAQ,KAAK,CAAE;AAAA,QACpD,KAAK,WAAYA,KAAI,aAAc,KAAM,KAAK,CAAE;AAAA,QAChD,YAAY,CAAC;AAAA,MACd;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,WAAW,KAAM,QAAS,MAAM,aAAc,KAAM,CAAE,CAAE;AAC7D;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,MAAOA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAE7C;AAEA,aAAS,mBAAoB,MAAO;AAEnC,YAAM,SAAS,CAAC;AAEhB,YAAM,OAAO,KAAK;AAClB,YAAM,WAAa,KAAK,MAAM,KAAK,SAAW;AAC9C,YAAMG,cAAa,KAAK;AAExB,eAAU,IAAI,GAAG,KAAKA,YAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,cAAM,kBAAkB,aAAcA,YAAY,CAAE,CAAE;AAEtD,iBAAU,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAO;AAE5D,iBAAO,KAAM,gBAAiB,CAAE,CAAE;AAAA,QAEnC;AAAA,MAED;AAEA,aAAO,IAAI,cAAe,MAAM,UAAU,MAAO;AAAA,IAElD;AAEA,aAAS,iBAAkB,IAAK;AAE/B,aAAO,SAAU,QAAQ,MAAO,EAAG,GAAG,kBAAmB;AAAA,IAE1D;AAIA,aAAS,gBAAiBH,MAAM;AAE/B,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAEJ,iBAAK,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AAClD,iBAAK,OAAO,UAAW,KAAM;AAC7B;AAAA,UAED,KAAK;AACJ,iBAAK,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AAClD,oBAAQ,KAAM,gEAAiE;AAC/E;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,YAAaA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAEnD;AAEA,aAAS,UAAWA,MAAM;AAEzB,YAAM,OAAO;AAAA,QACZ,SAAS,CAAC;AAAA,MACX;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,kBAAkB,YAAa,MAAM,WAAY;AACtD;AAAA,UAED,KAAK;AACJ,kBAAM,KAAK,MAAM,aAAc,IAAK;AACpC,iBAAK,QAAS,EAAG,IAAI,YAAa,KAAM;AACxC;AAAA,UAED,KAAK;AACJ,iBAAK,SAAS,YAAa,KAAM;AACjC;AAAA,UAED,KAAK;AACJ,iBAAK,gBAAgB,mBAAoB,KAAM;AAC/C;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAaA,MAAM;AAE3B,YAAM,OAAO;AAAA,QACZ,QAAQ,CAAC;AAAA,MACV;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,WAAW,MAAM,aAAc,UAAW;AAChD,kBAAM,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AACnD,iBAAK,OAAQ,QAAS,IAAI;AAC1B;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,mBAAoBA,MAAM;AAElC,YAAM,OAAO;AAAA,QACZ,QAAQ,CAAC;AAAA,MACV;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,WAAW,MAAM,aAAc,UAAW;AAChD,kBAAM,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AACnD,kBAAM,SAAS,SAAU,MAAM,aAAc,QAAS,CAAE;AACxD,iBAAK,OAAQ,QAAS,IAAI,EAAE,IAAQ,OAAe;AACnD;AAAA,UAED,KAAK;AACJ,iBAAK,SAAS,UAAW,MAAM,WAAY;AAC3C;AAAA,UAED,KAAK;AACJ,iBAAK,IAAI,UAAW,MAAM,WAAY;AACtC;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,gBAAiB,MAAO;AAEhC,YAAM,QAAQ;AAAA,QACb,IAAI,KAAK;AAAA,MACV;AAEA,YAAM,WAAW,QAAQ,WAAY,MAAM,EAAG;AAE9C,UAAK,KAAK,SAAS,QAAY;AAE9B,cAAM,OAAO,UAAW,KAAK,IAAK;AAIlC,iBAAS,QAAQ,cAAc,MAAM,KAAK;AAC1C,iBAAS,QAAQ,cAAc,MAAM,KAAK;AAAA,MAE3C;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,UAAW,MAAO;AAE1B,YAAM,aAAa;AAEnB,YAAM,QAAQ;AAAA,QACb,QAAQ,CAAC;AAAA;AAAA,QACT,SAAS;AAAA,UACR,OAAO,CAAC;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACR,OAAO,CAAC;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,MACD;AAEA,YAAM,UAAU,KAAK;AACrB,YAAM,gBAAgB,KAAK;AAE3B,YAAM,SAAS,cAAc;AAC7B,YAAM,IAAI,cAAc;AACxB,YAAM,cAAc,cAAc,OAAO,MAAM;AAC/C,YAAM,eAAe,cAAc,OAAO,OAAO;AAEjD,YAAM,cAAc,KAAK,QAAS,KAAK,OAAO,OAAO,KAAM;AAC3D,YAAM,gBAAgB,KAAK,QAAS,KAAK,OAAO,OAAO,eAAgB;AAEvE,YAAM,UAAU,QAAS,cAAc,OAAO,OAAO,EAAG,EAAE;AAC1D,UAAI,SAAS;AAEb,UAAI,GAAG,GAAG;AAIV,WAAM,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAO;AAE7C,cAAM,aAAa,OAAQ,CAAE;AAC7B,cAAM,iBAAiB,CAAC;AAExB,aAAM,IAAI,GAAG,IAAI,YAAY,KAAO;AAEnC,gBAAM,YAAY,EAAG,SAAS,WAAY;AAC1C,gBAAM,WAAW,EAAG,SAAS,YAAa;AAC1C,gBAAM,aAAa,QAAS,QAAS;AAErC,yBAAe,KAAM,EAAE,OAAO,WAAW,QAAQ,WAAW,CAAE;AAE9D,oBAAU;AAAA,QAEX;AAKA,uBAAe,KAAM,UAAW;AAKhC,aAAM,IAAI,GAAG,IAAI,YAAY,KAAO;AAEnC,gBAAM,IAAI,eAAgB,CAAE;AAE5B,cAAK,MAAM,QAAY;AAEtB,kBAAM,QAAQ,MAAM,KAAM,EAAE,KAAM;AAClC,kBAAM,QAAQ,MAAM,KAAM,EAAE,MAAO;AAAA,UAEpC,OAAO;AAEN,kBAAM,QAAQ,MAAM,KAAM,CAAE;AAC5B,kBAAM,QAAQ,MAAM,KAAM,CAAE;AAAA,UAE7B;AAAA,QAED;AAAA,MAED;AAIA,UAAK,KAAK,iBAAkB;AAE3B,cAAM,aAAa,IAAI,QAAQ,EAAE,UAAW,KAAK,eAAgB,EAAE,UAAU;AAAA,MAE9E,OAAO;AAEN,cAAM,aAAa,IAAI,QAAQ,EAAE,SAAS;AAAA,MAE3C;AAIA,WAAM,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,IAAI,GAAG,KAAO;AAExD,cAAM,OAAO,YAAY,MAAO,CAAE;AAClC,cAAM,cAAc,IAAI,QAAQ,EAAE,UAAW,cAAc,OAAO,IAAI,cAAc,MAAO,EAAE,UAAU;AAEvG,cAAM,OAAO,KAAM,EAAE,MAAY,YAAyB,CAAE;AAAA,MAE7D;AAEA,aAAO;AAIP,eAAS,WAAY,GAAG,GAAI;AAE3B,eAAO,EAAE,SAAS,EAAE;AAAA,MAErB;AAAA,IAED;AAEA,aAAS,cAAe,IAAK;AAE5B,aAAO,SAAU,QAAQ,YAAa,EAAG,GAAG,eAAgB;AAAA,IAE7D;AAIA,aAAS,WAAYA,MAAM;AAE1B,YAAM,OAAO;AAAA,QACZ,WAAW,qBAAsBA,MAAK,WAAY,EAAG,CAAE,EAAE;AAAA,MAC1D;AAEA,cAAQ,OAAQA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAE9C;AAEA,aAAS,WAAY,MAAO;AAE3B,UAAK,KAAK,UAAU,OAAY,QAAO,KAAK;AAE5C,aAAO,KAAK;AAAA,IAEb;AAEA,aAAS,SAAU,IAAK;AAEvB,YAAM,OAAO,QAAQ,OAAQ,EAAG;AAEhC,UAAK,SAAS,QAAY;AAEzB,eAAO,SAAU,MAAM,UAAW;AAAA,MAEnC;AAEA,cAAQ,KAAM,qDAAsD,EAAG;AAEvE,aAAO;AAAA,IAER;AAIA,aAAS,YAAaA,MAAM;AAE3B,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,UAAU,yBAA0B,KAAM;AAC/C;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,QAASA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAE/C;AAEA,aAAS,yBAA0BA,MAAM;AAExC,YAAM,OAAO;AAAA,QACZ,UAAU,CAAC;AAAA,QACX,UAAU,CAAC;AAAA,MACZ;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,gCAAqB,OAAO,IAAK;AACjC;AAAA,UAED,KAAK;AACJ,iBAAK,YAAY,qBAAsB,KAAM;AAC7C;AAAA,UAED,KAAK;AACJ,iBAAK,QAAQ,iBAAkB,KAAM;AACrC;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,oBAAqBA,MAAK,MAAO;AAEzC,YAAM,MAAMA,KAAI,aAAc,KAAM;AAEpC,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,SAAU,GAAI,IAAI,mBAAoB,KAAM;AACjD;AAAA,UAED,KAAK;AACJ,iBAAK,SAAU,GAAI,IAAI,mBAAoB,KAAM;AACjD;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,aAAS,mBAAoBA,MAAM;AAElC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,YAAY,MAAM;AACvB;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,mBAAoBA,MAAM;AAElC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,SAAS,MAAM;AACpB;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsBA,MAAM;AAEpC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAK,OAAO,MAAM;AAClB,iBAAK,aAAa,sBAAuB,KAAM;AAC/C;AAAA,UAED,KAAK;AACJ,iBAAK,QAAQ,iBAAkB,KAAM;AACrC;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,sBAAuBA,MAAM;AAErC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,qBAAsB,KAAM;AACrD;AAAA,UACD,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI;AAAA,cACxB,QAAQ,MAAM,aAAc,QAAS,IAAI,MAAM,aAAc,QAAS,IAAI;AAAA,cAC1E,MAAM,qBAAsB,KAAM;AAAA,YACnC;AACA;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsBA,MAAM;AAEpC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,YAAa,MAAM,WAAY;AACxD;AAAA,UAED,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,WAAY,MAAM,WAAY;AACvD;AAAA,UAED,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,EAAE,IAAI,MAAM,aAAc,SAAU,GAAG,OAAO,4BAA6B,KAAM,EAAE;AAC5G;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,4BAA6BA,MAAM;AAE3C,YAAM,OAAO;AAAA,QACZ,WAAW,CAAC;AAAA,MACb;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,6CAAkC,OAAO,IAAK;AAC9C;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,iCAAkCA,MAAK,MAAO;AAEtD,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,sDAA2C,OAAO,IAAK;AACvD;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,aAAS,0CAA2CA,MAAK,MAAO;AAE/D,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAK,UAAW,MAAM,QAAS,IAAI,WAAY,MAAM,WAAY;AACjE;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AAIJ,gBAAK,MAAM,YAAY,YAAY,MAAM,QAAS;AAEjD,mBAAK,UAAW,MAAM,QAAS,IAAI;AAAA,YAEpC,WAAY,MAAM,YAAY,YAAY,MAAM,SAAU;AAEzD,mBAAK,UAAW,MAAM,QAAS,IAAI;AAAA,YAEpC,OAAO;AAEN,mBAAK,UAAW,MAAM,QAAS,IAAI,SAAU,MAAM,WAAY;AAAA,YAEhE;AAEA;AAAA,UAED,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,8BAA+B,KAAM;AAC9D;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,aAAS,iBAAkBA,MAAM;AAEhC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,YAAY,0BAA2B,KAAM;AAClD;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,0BAA2BA,MAAM;AAEzC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,SAAU,MAAM,WAAY;AACrD;AAAA,UAED,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,8BAA+B,KAAM;AAC9D;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,8BAA+BA,MAAM;AAE7C,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,EAAE,IAAI,MAAM,aAAc,SAAU,GAAG,UAAU,MAAM,aAAc,UAAW,GAAG,OAAO,4BAA6B,KAAM,EAAE;AACxJ;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAa,MAAO;AAE5B,aAAO;AAAA,IAER;AAEA,aAAS,UAAW,IAAK;AAExB,aAAO,SAAU,QAAQ,QAAS,EAAG,GAAG,WAAY;AAAA,IAErD;AAIA,aAAS,cAAeA,MAAM;AAE7B,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO;AAAA,MAChC;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,MAAM,QAAS,MAAM,aAAc,KAAM,CAAE;AAChD;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,UAAWA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAEjD;AAEA,aAAS,iBAAkB,OAAQ;AAElC,UAAI;AAEJ,UAAI,YAAY,MAAM,OAAS,MAAM,YAAa,GAAI,IAAI,MAAM,KAAM,CAAE;AACxE,kBAAY,UAAU,YAAY;AAElC,cAAS,WAAY;AAAA,QAEpB,KAAK;AACJ,mBAAS;AACT;AAAA,QAED;AACC,mBAAS;AAAA,MAEX;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,cAAe,MAAO;AAE9B,YAAM,SAAS,UAAW,KAAK,GAAI;AACnC,YAAM,YAAY,OAAO,QAAQ;AAEjC,UAAI;AAEJ,cAAS,UAAU,MAAO;AAAA,QAEzB,KAAK;AAAA,QACL,KAAK;AACJ,qBAAW,IAAI,kBAAkB;AACjC;AAAA,QAED,KAAK;AACJ,qBAAW,IAAI,oBAAoB;AACnC;AAAA,QAED;AACC,qBAAW,IAAI,kBAAkB;AACjC;AAAA,MAEF;AAEA,eAAS,OAAO,KAAK,QAAQ;AAE7B,eAAS,WAAY,eAAe,aAAa,MAAO;AAEvD,cAAM,UAAU,OAAO,QAAQ,SAAU,cAAc,EAAG;AAC1D,YAAI,QAAQ;AAIZ,YAAK,YAAY,QAAY;AAE5B,gBAAM,UAAU,OAAO,QAAQ,SAAU,QAAQ,MAAO;AACxD,kBAAQ,SAAU,QAAQ,SAAU;AAAA,QAErC,OAAO;AAEN,kBAAQ,KAAM,6EAA8E;AAC5F,kBAAQ,SAAU,cAAc,EAAG;AAAA,QAEpC;AAIA,YAAK,UAAU,MAAO;AAErB,gBAAM,SAAS,iBAAkB,KAAM;AAEvC,cAAK,WAAW,QAAY;AAE3B,kBAAM,UAAU,OAAO,KAAM,KAAM;AAEnC,kBAAM,QAAQ,cAAc;AAE5B,gBAAK,UAAU,UAAa,MAAM,cAAc,UAAa,QAAS,MAAM,SAAU,MAAM,OAAQ;AAEnG,oBAAMI,aAAY,MAAM;AAExB,sBAAQ,QAAQA,WAAU,QAAQ,iBAAiB;AACnD,sBAAQ,QAAQA,WAAU,QAAQ,iBAAiB;AAEnD,sBAAQ,OAAO,IAAKA,WAAU,WAAW,GAAGA,WAAU,WAAW,CAAE;AACnE,sBAAQ,OAAO,IAAKA,WAAU,WAAW,GAAGA,WAAU,WAAW,CAAE;AAAA,YAEpE,OAAO;AAEN,sBAAQ,QAAQ;AAChB,sBAAQ,QAAQ;AAAA,YAEjB;AAEA,gBAAK,eAAe,MAAO;AAE1B,sBAAQ,aAAa;AAAA,YAEtB;AAEA,mBAAO;AAAA,UAER,OAAO;AAEN,oBAAQ,KAAM,yDAAyD,KAAM;AAE7E,mBAAO;AAAA,UAER;AAAA,QAED,OAAO;AAEN,kBAAQ,KAAM,yDAA0D,cAAc,EAAG;AAEzF,iBAAO;AAAA,QAER;AAAA,MAED;AAEA,YAAM,aAAa,UAAU;AAE7B,iBAAY,OAAO,YAAa;AAE/B,cAAM,YAAY,WAAY,GAAI;AAElC,gBAAS,KAAM;AAAA,UAEd,KAAK;AACJ,gBAAK,UAAU,MAAQ,UAAS,MAAM,UAAW,UAAU,KAAM;AACjE,gBAAK,UAAU,QAAU,UAAS,MAAM,WAAY,UAAU,SAAS,cAAe;AACtF;AAAA,UACD,KAAK;AACJ,gBAAK,UAAU,SAAS,SAAS,SAAW,UAAS,SAAS,UAAW,UAAU,KAAM;AACzF,gBAAK,UAAU,QAAU,UAAS,cAAc,WAAY,UAAU,OAAQ;AAC9E;AAAA,UACD,KAAK;AACJ,gBAAK,UAAU,QAAU,UAAS,YAAY,WAAY,UAAU,OAAQ;AAC5E;AAAA,UACD,KAAK;AACJ,gBAAK,UAAU,QAAU,UAAS,WAAW,WAAY,UAAU,SAAS,cAAe;AAC3F;AAAA,UACD,KAAK;AACJ,gBAAK,UAAU,SAAS,SAAS,UAAY,UAAS,YAAY,UAAU;AAC5E;AAAA,UACD,KAAK;AACJ,gBAAK,UAAU,SAAS,SAAS,SAAW,UAAS,SAAS,UAAW,UAAU,KAAM;AACzF,gBAAK,UAAU,QAAU,UAAS,cAAc,WAAY,UAAU,SAAS,cAAe;AAC9F;AAAA,QAEF;AAAA,MAED;AAEA,eAAS,MAAM,oBAAoB;AACnC,UAAK,SAAS,SAAW,UAAS,SAAS,oBAAoB;AAC/D,UAAK,SAAS,SAAW,UAAS,SAAS,oBAAoB;AAI/D,UAAI,cAAc,WAAY,aAAc;AAC5C,UAAI,eAAe,WAAY,cAAe;AAI9C,UAAK,iBAAiB,UAAa,aAAc;AAEhD,uBAAe;AAAA,UACd,OAAO;AAAA,QACR;AAAA,MAED;AAIA,UAAK,gBAAgB,UAAa,cAAe;AAEhD,sBAAc;AAAA,UACb,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,OAAO,CAAE,GAAG,GAAG,GAAG,CAAE;AAAA,UACrB;AAAA,QAAE;AAAA,MAEJ;AAEA,UAAK,eAAe,cAAe;AAIlC,YAAK,YAAY,KAAK,SAAU;AAI/B,mBAAS,cAAc;AAAA,QAExB,OAAO;AAEN,gBAAM,QAAQ,YAAY,KAAK;AAE/B,kBAAS,YAAY,QAAS;AAAA,YAE7B,KAAK;AACJ,uBAAS,UAAU,MAAO,CAAE,IAAI,aAAa;AAC7C;AAAA,YACD,KAAK;AACJ,uBAAS,UAAU,IAAM,MAAO,CAAE,IAAI,aAAa;AACnD;AAAA,YACD,KAAK;AACJ,uBAAS,UAAU,IAAM,MAAO,CAAE,IAAI,aAAa;AACnD;AAAA,YACD,KAAK;AACJ,uBAAS,UAAU,MAAO,CAAE,IAAI,aAAa;AAC7C;AAAA,YACD;AACC,sBAAQ,KAAM,qEAAqE,YAAY,MAAO;AAAA,UAExG;AAEA,cAAK,SAAS,UAAU,EAAI,UAAS,cAAc;AAAA,QAEpD;AAAA,MAED;AAKA,UAAK,UAAU,UAAU,UAAa,UAAU,MAAM,cAAc,QAAY;AAE/E,cAAM,aAAa,UAAU,MAAM;AAEnC,mBAAY,KAAK,YAAa;AAE7B,gBAAM,IAAI,WAAY,CAAE;AAExB,kBAAS,GAAI;AAAA,YAEZ,KAAK;AACJ,uBAAS,OAAS,MAAM,IAAI,aAAa;AACzC;AAAA,YAED,KAAK;AACJ,uBAAS,YAAY,WAAY,EAAE,OAAQ;AAC3C,uBAAS,cAAc,IAAI,QAAS,GAAG,CAAE;AACzC;AAAA,UAEF;AAAA,QAED;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAa,IAAK;AAE1B,aAAO,SAAU,QAAQ,UAAW,EAAG,GAAG,aAAc;AAAA,IAEzD;AAIA,aAAS,YAAaJ,MAAM;AAE3B,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO;AAAA,MAChC;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,SAAS,kBAAmB,KAAM;AACvC;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,QAASA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAE/C;AAEA,aAAS,kBAAmBA,MAAM;AAEjC,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,mBAAO,qBAAsB,KAAM;AAAA,QAErC;AAAA,MAED;AAEA,aAAO,CAAC;AAAA,IAET;AAEA,aAAS,qBAAsBA,MAAM;AAEpC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAEJ,iBAAK,YAAY,MAAM;AACvB,iBAAK,aAAa,sBAAuB,KAAM;AAE/C;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,sBAAuBA,MAAM;AAErC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAM,MAAM,QAAS,IAAI,WAAY,MAAM,WAAY;AACvD;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAa,MAAO;AAE5B,UAAI;AAEJ,cAAS,KAAK,OAAO,WAAY;AAAA,QAEhC,KAAK;AACJ,mBAAS,IAAI;AAAA,YACZ,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,UACxB;AACA;AAAA,QAED,KAAK;AACJ,cAAI,OAAO,KAAK,OAAO,WAAW;AAClC,cAAI,OAAO,KAAK,OAAO,WAAW;AAClC,gBAAM,cAAc,KAAK,OAAO,WAAW;AAE3C,iBAAS,SAAS,SAAgB,OAAO,cAAgB;AACzD,iBAAS,SAAS,SAAgB,OAAO,cAAgB;AAEzD,kBAAQ;AACR,kBAAQ;AAER,mBAAS,IAAI;AAAA,YACZ,CAAE;AAAA,YAAM;AAAA,YAAM;AAAA,YAAM,CAAE;AAAA;AAAA,YACtB,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,UACxB;AACA;AAAA,QAED;AACC,mBAAS,IAAI,kBAAkB;AAC/B;AAAA,MAEF;AAEA,aAAO,OAAO,KAAK,QAAQ;AAE3B,aAAO;AAAA,IAER;AAEA,aAAS,UAAW,IAAK;AAExB,YAAM,OAAO,QAAQ,QAAS,EAAG;AAEjC,UAAK,SAAS,QAAY;AAEzB,eAAO,SAAU,MAAM,WAAY;AAAA,MAEpC;AAEA,cAAQ,KAAM,sDAAuD,EAAG;AAExE,aAAO;AAAA,IAER;AAIA,aAAS,WAAYA,MAAM;AAE1B,UAAI,OAAO,CAAC;AAEZ,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,mBAAO,oBAAqB,KAAM;AAClC;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,OAAQA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAE9C;AAEA,aAAS,oBAAqBA,MAAM;AAEnC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAEJ,iBAAK,YAAY,MAAM;AACvB,iBAAK,aAAa,qBAAsB,KAAM;AAAA,QAEhD;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsBA,MAAM;AAEpC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,QAAQ,YAAa,MAAM,WAAY;AAC7C,iBAAK,QAAQ,IAAI,MAAM,EAAE,UAAW,KAAM,EAAE,oBAAoB;AAChE;AAAA,UAED,KAAK;AACJ,iBAAK,eAAe,WAAY,MAAM,WAAY;AAClD;AAAA,UAED,KAAK;AACJ,kBAAM,IAAI,WAAY,MAAM,WAAY;AACxC,iBAAK,WAAW,IAAI,KAAK,KAAM,IAAI,CAAE,IAAI;AACzC;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,WAAY,MAAO;AAE3B,UAAI;AAEJ,cAAS,KAAK,WAAY;AAAA,QAEzB,KAAK;AACJ,kBAAQ,IAAI,iBAAiB;AAC7B;AAAA,QAED,KAAK;AACJ,kBAAQ,IAAI,WAAW;AACvB;AAAA,QAED,KAAK;AACJ,kBAAQ,IAAI,UAAU;AACtB;AAAA,QAED,KAAK;AACJ,kBAAQ,IAAI,aAAa;AACzB;AAAA,MAEF;AAEA,UAAK,KAAK,WAAW,MAAQ,OAAM,MAAM,KAAM,KAAK,WAAW,KAAM;AACrE,UAAK,KAAK,WAAW,SAAW,OAAM,WAAW,KAAK,WAAW;AAEjE,aAAO;AAAA,IAER;AAEA,aAAS,SAAU,IAAK;AAEvB,YAAM,OAAO,QAAQ,OAAQ,EAAG;AAEhC,UAAK,SAAS,QAAY;AAEzB,eAAO,SAAU,MAAM,UAAW;AAAA,MAEnC;AAEA,cAAQ,KAAM,qDAAsD,EAAG;AAEvE,aAAO;AAAA,IAER;AAIA,aAAS,cAAeA,MAAM;AAE7B,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO;AAAA,QAC/B,SAAS,CAAC;AAAA,QACV,UAAU,CAAC;AAAA,QACX,YAAY,CAAC;AAAA,MACd;AAEA,YAAM,OAAO,qBAAsBA,MAAK,MAAO,EAAG,CAAE;AAGpD,UAAK,SAAS,OAAY;AAE1B,eAAU,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAO;AAEnD,cAAM,QAAQ,KAAK,WAAY,CAAE;AAEjC,YAAK,MAAM,aAAa,EAAI;AAE5B,cAAM,KAAK,MAAM,aAAc,IAAK;AAEpC,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,QAAS,EAAG,IAAI,YAAa,KAAM;AACxC;AAAA,UAED,KAAK;AAEJ,iBAAK,WAAW,sBAAuB,KAAM;AAC7C;AAAA,UAED,KAAK;AACJ,oBAAQ,KAAM,qDAAqD,MAAM,QAAS;AAClF;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAK,WAAW,KAAM,uBAAwB,KAAM,CAAE;AACtD;AAAA,UAED;AACC,oBAAQ,IAAK,KAAM;AAAA,QAErB;AAAA,MAED;AAEA,cAAQ,WAAYA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAElD;AAEA,aAAS,YAAaA,MAAM;AAE3B,YAAM,OAAO;AAAA,QACZ,OAAO,CAAC;AAAA,QACR,QAAQ;AAAA,MACT;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,QAAQ,YAAa,MAAM,WAAY;AAC5C;AAAA,UAED,KAAK;AACJ,iBAAK,QAAQ,aAAc,MAAM,WAAY;AAC7C;AAAA,UAED,KAAK;AACJ,kBAAM,WAAW,qBAAsB,OAAO,UAAW,EAAG,CAAE;AAE9D,gBAAK,aAAa,QAAY;AAE7B,mBAAK,SAAS,SAAU,SAAS,aAAc,QAAS,CAAE;AAAA,YAE3D;AAEA;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,sBAAuBA,MAAM;AAErC,YAAM,OAAO,CAAC;AAEd,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,aAAM,MAAM,aAAc,UAAW,CAAE,IAAI,QAAS,MAAM,aAAc,QAAS,CAAE;AAAA,MAEpF;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,uBAAwBA,MAAM;AAEtC,YAAM,YAAY;AAAA,QACjB,MAAMA,KAAI;AAAA,QACV,UAAUA,KAAI,aAAc,UAAW;AAAA,QACvC,OAAO,SAAUA,KAAI,aAAc,OAAQ,CAAE;AAAA,QAC7C,QAAQ,CAAC;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,KAAK,QAAS,MAAM,aAAc,QAAS,CAAE;AACnD,kBAAM,WAAW,MAAM,aAAc,UAAW;AAChD,kBAAM,SAAS,SAAU,MAAM,aAAc,QAAS,CAAE;AACxD,kBAAM,MAAM,SAAU,MAAM,aAAc,KAAM,CAAE;AAClD,kBAAM,YAAc,MAAM,IAAI,WAAW,MAAM;AAC/C,sBAAU,OAAQ,SAAU,IAAI,EAAE,IAAQ,OAAe;AACzD,sBAAU,SAAS,KAAK,IAAK,UAAU,QAAQ,SAAS,CAAE;AAC1D,gBAAK,aAAa,WAAa,WAAU,QAAQ;AACjD;AAAA,UAED,KAAK;AACJ,sBAAU,SAAS,UAAW,MAAM,WAAY;AAChD;AAAA,UAED,KAAK;AACJ,sBAAU,IAAI,UAAW,MAAM,WAAY;AAC3C;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,gBAAiB,YAAa;AAEtC,YAAM,QAAQ,CAAC;AAEf,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,cAAM,YAAY,WAAY,CAAE;AAEhC,YAAK,MAAO,UAAU,IAAK,MAAM,OAAY,OAAO,UAAU,IAAK,IAAI,CAAC;AAExE,cAAO,UAAU,IAAK,EAAE,KAAM,SAAU;AAAA,MAEzC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,mBAAoB,YAAa;AAEzC,UAAIK,SAAQ;AAEZ,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAErD,cAAM,YAAY,WAAY,CAAE;AAEhC,YAAK,UAAU,UAAU,MAAO;AAE/B,UAAAA;AAAA,QAED;AAAA,MAED;AAEA,UAAKA,SAAQ,KAAKA,SAAQ,WAAW,QAAS;AAE7C,mBAAW,cAAc;AAAA,MAE1B;AAAA,IAED;AAEA,aAAS,cAAe,MAAO;AAE9B,YAAM,QAAQ,CAAC;AAEf,YAAM,UAAU,KAAK;AACrB,YAAM,WAAW,KAAK;AACtB,YAAM,aAAa,KAAK;AAExB,UAAK,WAAW,WAAW,EAAI,QAAO,CAAC;AAKvC,YAAM,oBAAoB,gBAAiB,UAAW;AAEtD,iBAAY,QAAQ,mBAAoB;AAEvC,cAAM,gBAAgB,kBAAmB,IAAK;AAI9C,2BAAoB,aAAc;AAIlC,cAAO,IAAK,IAAI,kBAAmB,eAAe,SAAS,QAAS;AAAA,MAErE;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,kBAAmB,YAAY,SAAS,UAAW;AAE3D,YAAM,QAAQ,CAAC;AAEf,YAAMC,YAAW,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AACxC,YAAM,SAAS,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AACtC,YAAM,KAAK,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AAClC,YAAM,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AACnC,YAAM,QAAQ,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AAErC,YAAM,YAAY,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AACzC,YAAM,aAAa,EAAE,OAAO,CAAC,GAAG,QAAQ,EAAE;AAE1C,YAAM,WAAW,IAAI,eAAe;AAEpC,YAAM,eAAe,CAAC;AAEtB,UAAI,QAAQ;AAEZ,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,cAAM,YAAY,WAAY,CAAE;AAChC,cAAM,SAAS,UAAU;AAIzB,YAAID,SAAQ;AAEZ,gBAAS,UAAU,MAAO;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AACJ,YAAAA,SAAQ,UAAU,QAAQ;AAC1B;AAAA,UAED,KAAK;AACJ,YAAAA,SAAQ,UAAU,QAAQ;AAC1B;AAAA,UAED,KAAK;AAEJ,qBAAU,IAAI,GAAG,IAAI,UAAU,OAAO,KAAO;AAE5C,oBAAM,KAAK,UAAU,OAAQ,CAAE;AAE/B,sBAAS,IAAK;AAAA,gBAEb,KAAK;AACJ,kBAAAA,UAAS;AACT;AAAA,gBAED,KAAK;AACJ,kBAAAA,UAAS;AACT;AAAA,gBAED;AACC,kBAAAA,WAAW,KAAK,KAAM;AACtB;AAAA,cAEF;AAAA,YAED;AAEA;AAAA,UAED;AACC,oBAAQ,KAAM,+CAA+C,UAAU,IAAK;AAAA,QAE9E;AAEA,iBAAS,SAAU,OAAOA,QAAO,CAAE;AACnC,iBAASA;AAIT,YAAK,UAAU,UAAW;AAEzB,uBAAa,KAAM,UAAU,QAAS;AAAA,QAEvC;AAIA,mBAAY,QAAQ,QAAS;AAE5B,gBAAM,QAAQ,OAAQ,IAAK;AAE3B,kBAAS,MAAO;AAAA,YAEf,KAAK;AACJ,yBAAY,OAAO,UAAW;AAE7B,sBAAM,KAAK,SAAU,GAAI;AAEzB,wBAAS,KAAM;AAAA,kBAEd,KAAK;AACJ,0BAAM,aAAaC,UAAS,MAAM;AAClC,sCAAmB,WAAW,QAAS,EAAG,GAAG,MAAM,QAAQA,UAAS,KAAM;AAC1E,oBAAAA,UAAS,SAAS,QAAS,EAAG,EAAE;AAEhC,wBAAK,QAAQ,eAAe,QAAQ,aAAc;AAEjD,wCAAmB,WAAW,QAAQ,aAAa,MAAM,QAAQ,UAAU,KAAM;AACjF,wCAAmB,WAAW,QAAQ,aAAa,MAAM,QAAQ,WAAW,KAAM;AAAA,oBAEnF;AAIA,wBAAK,UAAU,UAAU,SAAS,WAAW,gBAAgB,MAAO;AAEnE,4BAAMD,UAAUC,UAAS,MAAM,SAAS,cAAeA,UAAS;AAEhE,+BAAU,IAAI,GAAG,IAAID,QAAO,KAAO;AAIlC,2BAAG,MAAM,KAAM,GAAG,CAAE;AAAA,sBAErB;AAAA,oBAED;AAEA;AAAA,kBAED,KAAK;AACJ,sCAAmB,WAAW,QAAS,EAAG,GAAG,MAAM,QAAQ,OAAO,KAAM;AACxE,2BAAO,SAAS,QAAS,EAAG,EAAE;AAC9B;AAAA,kBAED,KAAK;AACJ,sCAAmB,WAAW,QAAS,EAAG,GAAG,MAAM,QAAQ,MAAM,KAAM;AACvE,0BAAM,SAAS,QAAS,EAAG,EAAE;AAC7B;AAAA,kBAED,KAAK;AACJ,sCAAmB,WAAW,QAAS,EAAG,GAAG,MAAM,QAAQ,GAAG,KAAM;AACpE,uBAAG,SAAS,QAAS,EAAG,EAAE;AAC1B;AAAA,kBAED,KAAK;AACJ,sCAAmB,WAAW,QAAS,EAAG,GAAG,MAAM,QAAQ,IAAI,KAAM;AACrE,uBAAG,SAAS,QAAS,EAAG,EAAE;AAC1B;AAAA,kBAED;AACC,4BAAQ,KAAM,6EAA6E,GAAI;AAAA,gBAEjG;AAAA,cAED;AAEA;AAAA,YAED,KAAK;AACJ,gCAAmB,WAAW,QAAS,MAAM,EAAG,GAAG,MAAM,QAAQ,OAAO,KAAM;AAC9E,qBAAO,SAAS,QAAS,MAAM,EAAG,EAAE;AACpC;AAAA,YAED,KAAK;AACJ,gCAAmB,WAAW,QAAS,MAAM,EAAG,GAAG,MAAM,QAAQ,MAAM,OAAO,IAAK;AACnF,oBAAM,SAAS,QAAS,MAAM,EAAG,EAAE;AACnC;AAAA,YAED,KAAK;AACJ,gCAAmB,WAAW,QAAS,MAAM,EAAG,GAAG,MAAM,QAAQ,GAAG,KAAM;AAC1E,iBAAG,SAAS,QAAS,MAAM,EAAG,EAAE;AAChC;AAAA,YAED,KAAK;AACJ,gCAAmB,WAAW,QAAS,MAAM,EAAG,GAAG,MAAM,QAAQ,IAAI,KAAM;AAC3E,kBAAI,SAAS,QAAS,MAAM,EAAG,EAAE;AACjC;AAAA,UAEF;AAAA,QAED;AAAA,MAED;AAIA,UAAKC,UAAS,MAAM,SAAS,EAAI,UAAS,aAAc,YAAY,IAAI,uBAAwBA,UAAS,OAAOA,UAAS,MAAO,CAAE;AAClI,UAAK,OAAO,MAAM,SAAS,EAAI,UAAS,aAAc,UAAU,IAAI,uBAAwB,OAAO,OAAO,OAAO,MAAO,CAAE;AAC1H,UAAK,MAAM,MAAM,SAAS,EAAI,UAAS,aAAc,SAAS,IAAI,uBAAwB,MAAM,OAAO,MAAM,MAAO,CAAE;AACtH,UAAK,GAAG,MAAM,SAAS,EAAI,UAAS,aAAc,MAAM,IAAI,uBAAwB,GAAG,OAAO,GAAG,MAAO,CAAE;AAC1G,UAAK,IAAI,MAAM,SAAS,EAAI,UAAS,aAAc,OAAO,IAAI,uBAAwB,IAAI,OAAO,IAAI,MAAO,CAAE;AAE9G,UAAK,UAAU,MAAM,SAAS,EAAI,UAAS,aAAc,aAAa,IAAI,uBAAwB,UAAU,OAAO,UAAU,MAAO,CAAE;AACtI,UAAK,WAAW,MAAM,SAAS,EAAI,UAAS,aAAc,cAAc,IAAI,uBAAwB,WAAW,OAAO,WAAW,MAAO,CAAE;AAE1I,YAAM,OAAO;AACb,YAAM,OAAO,WAAY,CAAE,EAAE;AAC7B,YAAM,eAAe;AAErB,aAAO;AAAA,IAER;AAEA,aAAS,kBAAmB,WAAW,QAAQ,QAAQ,OAAO,UAAU,OAAQ;AAE/E,YAAM,UAAU,UAAU;AAC1B,YAAM,SAAS,UAAU;AACzB,YAAM,SAAS,UAAU;AAEzB,eAAS,WAAY,GAAI;AAExB,YAAI,QAAQ,QAAS,IAAI,MAAO,IAAI;AACpC,cAAM,SAAS,QAAQ;AAEvB,eAAQ,QAAQ,QAAQ,SAAW;AAElC,gBAAM,KAAM,YAAa,KAAM,CAAE;AAAA,QAElC;AAEA,YAAK,SAAU;AAGd,gBAAM,aAAa,MAAM,SAAS,eAAe;AACjD,oBAAU;AAAA,YACT,MAAO,aAAa,CAAE;AAAA,YACtB,MAAO,aAAa,CAAE;AAAA,YACtB,MAAO,aAAa,CAAE;AAAA,UACvB,EAAE,oBAAoB;AAEtB,gBAAO,aAAa,CAAE,IAAI,UAAU;AACpC,gBAAO,aAAa,CAAE,IAAI,UAAU;AACpC,gBAAO,aAAa,CAAE,IAAI,UAAU;AAAA,QAErC;AAAA,MAED;AAEA,YAAM,cAAc,OAAO;AAC3B,YAAM,eAAe,OAAO;AAE5B,UAAK,UAAU,WAAW,QAAY;AAErC,YAAI,QAAQ;AAEZ,iBAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAO;AAEjD,gBAAMD,SAAQ,OAAQ,CAAE;AAExB,cAAKA,WAAU,GAAI;AAElB,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAE3B,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAChD,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAAA,UAEjD,WAAYA,WAAU,GAAI;AAEzB,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAE3B,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAAG,uBAAY,CAAE;AAAA,UAEjD,WAAYA,SAAQ,GAAI;AAEvB,qBAAU,IAAI,GAAG,KAAOA,SAAQ,GAAK,KAAK,IAAI,KAAO;AAEpD,oBAAM,IAAI,QAAQ,SAAS;AAC3B,oBAAM,IAAI,QAAQ,SAAS;AAC3B,oBAAM,IAAI,QAAQ,UAAW,IAAI;AAEjC,yBAAY,CAAE;AAAG,yBAAY,CAAE;AAAG,yBAAY,CAAE;AAAA,YAEjD;AAAA,UAED;AAEA,mBAAS,SAASA;AAAA,QAEnB;AAAA,MAED,OAAO;AAEN,iBAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,QAAS;AAEzD,qBAAY,CAAE;AAAA,QAEf;AAAA,MAED;AAAA,IAED;AAEA,aAAS,YAAa,IAAK;AAE1B,aAAO,SAAU,QAAQ,WAAY,EAAG,GAAG,aAAc;AAAA,IAE1D;AAIA,aAAS,qBAAsBL,MAAM;AAEpC,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO,KAAK;AAAA,QACpC,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,MACT;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,2CAAgC,OAAO,IAAK;AAC5C;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,iBAAkBA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAExD;AAEA,aAAS,qBAAsB,MAAO;AAErC,UAAK,KAAK,UAAU,OAAY,QAAO,KAAK;AAE5C,aAAO;AAAA,IAER;AAEA,aAAS,mBAAoB,IAAK;AAEjC,aAAO,SAAU,QAAQ,iBAAkB,EAAG,GAAG,oBAAqB;AAAA,IAEvE;AAEA,aAAS,+BAAgCA,MAAK,MAAO;AAEpD,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,OAAQ,MAAM,aAAc,KAAM,CAAE,IAAI,qBAAsB,KAAM;AACzE;AAAA,UAED,KAAK;AACJ,iBAAK,MAAM,KAAM,oBAAqB,KAAM,CAAE;AAC9C;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,aAAS,qBAAsBA,MAAM;AAEpC,UAAI;AAEJ,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AAAA,UACL,KAAK;AACJ,mBAAO,8BAA+B,KAAM;AAC5C;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,8BAA+BA,MAAM;AAE7C,YAAM,OAAO;AAAA,QACZ,KAAKA,KAAI,aAAc,KAAM;AAAA,QAC7B,MAAMA,KAAI,aAAc,MAAO,KAAK;AAAA,QACpC,MAAM,IAAI,QAAQ;AAAA,QAClB,QAAQ;AAAA,UACP,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACA,MAAMA,KAAI;AAAA,QACV,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,gBAAgB;AAAA,MACjB;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,QAAQ,YAAa,MAAM,WAAY;AAC7C,iBAAK,KAAK,UAAW,KAAM;AAC3B;AAAA,UACD,KAAK;AACJ,kBAAM,MAAM,MAAM,qBAAsB,KAAM,EAAG,CAAE;AACnD,kBAAM,MAAM,MAAM,qBAAsB,KAAM,EAAG,CAAE;AAEnD,iBAAK,OAAO,MAAM,WAAY,IAAI,WAAY;AAC9C,iBAAK,OAAO,MAAM,WAAY,IAAI,WAAY;AAC9C;AAAA,QAEF;AAAA,MAED;AAIA,UAAK,KAAK,OAAO,OAAO,KAAK,OAAO,KAAM;AAEzC,aAAK,SAAS;AAAA,MAEf;AAIA,WAAK,kBAAmB,KAAK,OAAO,MAAM,KAAK,OAAO,OAAQ;AAE9D,aAAO;AAAA,IAER;AAEA,aAAS,oBAAqBA,MAAM;AAEnC,YAAM,OAAO;AAAA,QACZ,KAAKA,KAAI,aAAc,KAAM;AAAA,QAC7B,MAAMA,KAAI,aAAc,MAAO,KAAK;AAAA,QACpC,aAAa,CAAC;AAAA,QACd,YAAY,CAAC;AAAA,MACd;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,YAAY,KAAM,0BAA2B,KAAM,CAAE;AAC1D;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAK,WAAW,KAAM,yBAA0B,KAAM,CAAE;AACxD;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,0BAA2BA,MAAM;AAEzC,YAAM,OAAO;AAAA,QACZ,OAAOA,KAAI,aAAc,OAAQ,EAAE,MAAO,GAAI,EAAE,IAAI;AAAA,QACpD,YAAY,CAAC;AAAA,QACb,OAAO,CAAC;AAAA,MACT;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,MAAM,KAAM,oBAAqB,KAAM,CAAE;AAC9C;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,iBAAK,WAAW,KAAM,yBAA0B,KAAM,CAAE;AACxD;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,yBAA0BA,MAAM;AAExC,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI;AAAA,MACX;AAEA,YAAM,QAAQ,YAAaA,KAAI,WAAY;AAE3C,cAAS,KAAK,MAAO;AAAA,QAEpB,KAAK;AACJ,eAAK,MAAM,IAAI,QAAQ;AACvB,eAAK,IAAI,UAAW,KAAM,EAAE,UAAU;AACtC;AAAA,QAED,KAAK;AACJ,eAAK,MAAM,IAAI,QAAQ;AACvB,eAAK,IAAI,UAAW,KAAM;AAC1B;AAAA,QAED,KAAK;AACJ,eAAK,MAAM,IAAI,QAAQ;AACvB,eAAK,IAAI,UAAW,KAAM;AAC1B,eAAK,QAAQ,UAAU,SAAU,MAAO,CAAE,CAAE;AAC5C;AAAA,MAEF;AAEA,aAAO;AAAA,IAER;AAIA,aAAS,kBAAmBA,MAAM;AAEjC,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO,KAAK;AAAA,QACpC,aAAa,CAAC;AAAA,MACf;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,YAAa,MAAM,aAAc,MAAO,CAAE,IAAI,CAAC;AACpD,kCAAuB,OAAO,KAAK,YAAa,MAAM,aAAc,MAAO,CAAE,CAAE;AAC/E;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,cAAeA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAErD;AAEA,aAAS,sBAAuBA,MAAK,MAAO;AAE3C,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,wCAA6B,OAAO,IAAK;AACzC;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,aAAS,4BAA6BA,MAAK,MAAO;AAEjD,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,UAAU,YAAa,MAAM,WAAY;AAC9C;AAAA,UAED,KAAK;AACJ,iBAAK,OAAO,YAAa,MAAM,WAAY,EAAG,CAAE;AAChD;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAIA,aAAS,qBAAsBA,MAAM;AAEpC,YAAM,OAAO;AAAA,QACZ,eAAe,CAAC;AAAA,MACjB;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,cAAc,KAAM,6BAA8B,KAAM,CAAE;AAC/D;AAAA,QAEF;AAAA,MAED;AAEA,cAAQ,iBAAkB,QAASA,KAAI,aAAc,KAAM,CAAE,CAAE,IAAI;AAAA,IAEpE;AAEA,aAAS,6BAA8BA,MAAM;AAE5C,YAAM,OAAO;AAAA,QACZ,QAAQA,KAAI,aAAc,QAAS,EAAE,MAAO,GAAI,EAAE,IAAI;AAAA,MACvD;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,QAAQ,MAAM,qBAAsB,OAAQ,EAAG,CAAE;AACvD,iBAAK,OAAO,MAAM;AAClB,kBAAM,gBAAgB,KAAK,KAAK,MAAO,OAAQ,EAAE,IAAI,EAAE,MAAO,MAAO,EAAG,CAAE;AAC1E,iBAAK,aAAa,cAAc,UAAW,GAAG,cAAc,SAAS,CAAE;AACvE;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsB,MAAO;AAErC,UAAK,KAAK,UAAU,OAAY,QAAO,KAAK;AAE5C,aAAO;AAAA,IAER;AAEA,aAAS,mBAAoB,IAAK;AAEjC,aAAO,SAAU,QAAQ,iBAAkB,EAAG,GAAG,oBAAqB;AAAA,IAEvE;AAEA,aAAS,kBAAkB;AAE1B,YAAM,oBAAoB,OAAO,KAAM,QAAQ,gBAAiB,EAAG,CAAE;AACrE,YAAM,oBAAoB,OAAO,KAAM,QAAQ,gBAAiB,EAAG,CAAE;AACrE,YAAM,gBAAgB,OAAO,KAAM,QAAQ,YAAa,EAAG,CAAE;AAE7D,UAAK,sBAAsB,UAAa,sBAAsB,OAAY;AAE1E,YAAM,kBAAkB,mBAAoB,iBAAkB;AAC9D,YAAM,kBAAkB,mBAAoB,iBAAkB;AAC9D,YAAM,cAAc,eAAgB,aAAc;AAElD,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,WAAW,CAAC;AAElB,eAAU,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAO;AAExD,cAAM,OAAO,cAAe,CAAE;AAI9B,cAAM,gBAAgB,QAAQ,cAAe,WAAW,KAAK,SAAS,IAAK;AAE3E,YAAK,eAAgB;AAIpB,gBAAM,sBAAsB,cAAc;AAI1C,kBAAS,KAAK,YAAY,mBAAoB;AAAA,QAE/C;AAAA,MAED;AAEA,eAAS,QAAS,YAAY,eAAgB;AAE7C,cAAM,oBAAoB,cAAc,aAAc,MAAO;AAC7D,cAAM,QAAQ,gBAAgB,OAAQ,UAAW;AAEjD,oBAAY,SAAU,SAAW,QAAS;AAEzC,cAAK,OAAO,SAAS,mBAAoB;AAExC,qBAAU,UAAW,IAAI;AAAA,cACxB;AAAA,cACA,YAAY,mBAAoB,aAAc;AAAA,cAC9C;AAAA,cACA,UAAU,MAAM;AAAA,YACjB;AAAA,UAED;AAAA,QAED,CAAE;AAAA,MAEH;AAEA,YAAM,KAAK,IAAI,QAAQ;AAEvB,mBAAa;AAAA,QAEZ,QAAQ,mBAAmB,gBAAgB;AAAA,QAE3C,eAAe,SAAW,YAAa;AAEtC,gBAAM,YAAY,SAAU,UAAW;AAEvC,cAAK,WAAY;AAEhB,mBAAO,UAAU;AAAA,UAElB,OAAO;AAEN,oBAAQ,KAAM,gCAAgC,aAAa,iBAAmB;AAAA,UAE/E;AAAA,QAED;AAAA,QAEA,eAAe,SAAW,YAAY,OAAQ;AAE7C,gBAAM,YAAY,SAAU,UAAW;AAEvC,cAAK,WAAY;AAEhB,kBAAM,QAAQ,UAAU;AAExB,gBAAK,QAAQ,MAAM,OAAO,OAAO,QAAQ,MAAM,OAAO,KAAM;AAE3D,sBAAQ,KAAM,gCAAgC,aAAa,YAAY,QAAQ,8BAA8B,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,IAAK;AAAA,YAErK,WAAY,MAAM,QAAS;AAE1B,sBAAQ,KAAM,gCAAgC,aAAa,aAAc;AAAA,YAE1E,OAAO;AAEN,oBAAM,SAAS,UAAU;AACzB,oBAAM,OAAO,MAAM;AACnB,oBAAM,aAAa,UAAU;AAE7B,qBAAO,SAAS;AAIhB,uBAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,sBAAM,YAAY,WAAY,CAAE;AAIhC,oBAAK,UAAU,OAAO,UAAU,IAAI,QAAS,UAAW,MAAM,IAAM;AAEnE,0BAAS,MAAM,MAAO;AAAA,oBAErB,KAAK;AACJ,6BAAO,SAAU,GAAG,iBAAkB,MAAM,UAAU,SAAU,KAAM,CAAE,CAAE;AAC1E;AAAA,oBAED,KAAK;AACJ,6BAAO,SAAU,GAAG,gBAAiB,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,KAAM,CAAE;AACtF;AAAA,oBAED;AACC,8BAAQ,KAAM,8CAA8C,MAAM,IAAK;AACvE;AAAA,kBAEF;AAAA,gBAED,OAAO;AAEN,0BAAS,UAAU,MAAO;AAAA,oBAEzB,KAAK;AACJ,6BAAO,SAAU,UAAU,GAAI;AAC/B;AAAA,oBAED,KAAK;AACJ,6BAAO,SAAU,GAAG,gBAAiB,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG,UAAU,IAAI,CAAE,CAAE;AACzF;AAAA,oBAED,KAAK;AACJ,6BAAO,MAAO,UAAU,GAAI;AAC5B;AAAA,oBAED,KAAK;AACJ,6BAAO,SAAU,GAAG,iBAAkB,UAAU,KAAK,UAAU,KAAM,CAAE;AACvE;AAAA,kBAEF;AAAA,gBAED;AAAA,cAED;AAEA,qBAAO,OAAO,KAAM,MAAO;AAC3B,qBAAO,OAAO,UAAW,OAAO,UAAU,OAAO,YAAY,OAAO,KAAM;AAE1E,uBAAU,UAAW,EAAE,WAAW;AAAA,YAEnC;AAAA,UAED,OAAO;AAEN,oBAAQ,IAAK,0BAA0B,aAAa,kBAAmB;AAAA,UAExE;AAAA,QAED;AAAA,MAED;AAAA,IAED;AAEA,aAAS,mBAAoB,MAAO;AAEnC,YAAM,aAAa,CAAC;AAEpB,YAAMA,OAAM,QAAQ,cAAe,UAAU,KAAK,KAAK,IAAK;AAE5D,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,YAAI,OAAOO;AAEX,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,kBAAMC,UAAS,IAAI,QAAQ,EAAE,UAAW,KAAM,EAAE,UAAU;AAC1D,uBAAW,KAAM;AAAA,cAChB,KAAK,MAAM,aAAc,KAAM;AAAA,cAC/B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,YACN,CAAE;AACF;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,YAAAD,UAAS,IAAI,QAAQ,EAAE,UAAW,KAAM;AACxC,uBAAW,KAAM;AAAA,cAChB,KAAK,MAAM,aAAc,KAAM;AAAA,cAC/B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,YACN,CAAE;AACF;AAAA,UAED,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,YAAAA,UAAS,IAAI,QAAQ,EAAE,UAAW,KAAM;AACxC,kBAAM,QAAQ,UAAU,SAAU,MAAO,CAAE,CAAE;AAC7C,uBAAW,KAAM;AAAA,cAChB,KAAK,MAAM,aAAc,KAAM;AAAA,cAC/B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,cACL;AAAA,YACD,CAAE;AACF;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAIA,aAAS,aAAcP,MAAM;AAE5B,YAAM,WAAWA,KAAI,qBAAsB,MAAO;AAIlD,eAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,cAAM,UAAU,SAAU,CAAE;AAE5B,YAAK,QAAQ,aAAc,IAAK,MAAM,OAAQ;AAE7C,kBAAQ,aAAc,MAAM,WAAW,CAAE;AAAA,QAE1C;AAAA,MAED;AAAA,IAED;AAEA,UAAM,SAAS,IAAI,QAAQ;AAC3B,UAAM,SAAS,IAAI,QAAQ;AAE3B,aAAS,UAAWA,MAAM;AAEzB,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO,KAAK;AAAA,QACpC,MAAMA,KAAI,aAAc,MAAO;AAAA,QAC/B,IAAIA,KAAI,aAAc,IAAK;AAAA,QAC3B,KAAKA,KAAI,aAAc,KAAM;AAAA,QAC7B,QAAQ,IAAI,QAAQ;AAAA,QACpB,OAAO,CAAC;AAAA,QACR,iBAAiB,CAAC;AAAA,QAClB,qBAAqB,CAAC;AAAA,QACtB,gBAAgB,CAAC;AAAA,QACjB,oBAAoB,CAAC;AAAA,QACrB,eAAe,CAAC;AAAA,QAChB,YAAY,CAAC;AAAA,MACd;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,YAAK,MAAM,aAAa,EAAI;AAE5B,YAAI;AAEJ,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,iBAAK,MAAM,KAAM,MAAM,aAAc,IAAK,CAAE;AAC5C,sBAAW,KAAM;AACjB;AAAA,UAED,KAAK;AACJ,iBAAK,gBAAgB,KAAM,QAAS,MAAM,aAAc,KAAM,CAAE,CAAE;AAClE;AAAA,UAED,KAAK;AACJ,iBAAK,oBAAoB,KAAM,kBAAmB,KAAM,CAAE;AAC1D;AAAA,UAED,KAAK;AACJ,iBAAK,eAAe,KAAM,QAAS,MAAM,aAAc,KAAM,CAAE,CAAE;AACjE;AAAA,UAED,KAAK;AACJ,iBAAK,mBAAmB,KAAM,kBAAmB,KAAM,CAAE;AACzD;AAAA,UAED,KAAK;AACJ,iBAAK,cAAc,KAAM,QAAS,MAAM,aAAc,KAAM,CAAE,CAAE;AAChE;AAAA,UAED,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,iBAAK,OAAO,SAAU,OAAO,UAAW,KAAM,EAAE,UAAU,CAAE;AAC5D,iBAAK,WAAY,MAAM,aAAc,KAAM,CAAE,IAAI,MAAM;AACvD;AAAA,UAED,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,mBAAO,UAAW,KAAM;AACxB,iBAAK,OAAO,SAAU,OAAO,gBAAiB,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE,CAAE;AAC7E,iBAAK,WAAY,MAAM,aAAc,KAAM,CAAE,IAAI,MAAM;AACvD;AAAA,UAED,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,kBAAM,QAAQ,UAAU,SAAU,MAAO,CAAE,CAAE;AAC7C,iBAAK,OAAO,SAAU,OAAO,iBAAkB,OAAO,UAAW,KAAM,GAAG,KAAM,CAAE;AAClF,iBAAK,WAAY,MAAM,aAAc,KAAM,CAAE,IAAI,MAAM;AACvD;AAAA,UAED,KAAK;AACJ,oBAAQ,YAAa,MAAM,WAAY;AACvC,iBAAK,OAAO,MAAO,OAAO,UAAW,KAAM,CAAE;AAC7C,iBAAK,WAAY,MAAM,aAAc,KAAM,CAAE,IAAI,MAAM;AACvD;AAAA,UAED,KAAK;AACJ;AAAA,UAED;AACC,oBAAQ,IAAK,KAAM;AAAA,QAErB;AAAA,MAED;AAEA,UAAK,QAAS,KAAK,EAAG,GAAI;AAEzB,gBAAQ,KAAM,0GAA0G,KAAK,EAAG;AAAA,MAEjI,OAAO;AAEN,gBAAQ,MAAO,KAAK,EAAG,IAAI;AAAA,MAE5B;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,kBAAmBA,MAAM;AAEjC,YAAM,OAAO;AAAA,QACZ,IAAI,QAASA,KAAI,aAAc,KAAM,CAAE;AAAA,QACvC,WAAW,CAAC;AAAA,QACZ,WAAW,CAAC;AAAA,MACb;AAEA,eAAU,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAO;AAElD,cAAM,QAAQA,KAAI,WAAY,CAAE;AAEhC,gBAAS,MAAM,UAAW;AAAA,UAEzB,KAAK;AACJ,kBAAM,YAAY,MAAM,qBAAsB,mBAAoB;AAElE,qBAAU,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAO;AAE7C,oBAAM,WAAW,UAAW,CAAE;AAC9B,oBAAM,SAAS,SAAS,aAAc,QAAS;AAC/C,oBAAM,SAAS,SAAS,aAAc,QAAS;AAE/C,mBAAK,UAAW,MAAO,IAAI,QAAS,MAAO;AAAA,YAE5C;AAEA;AAAA,UAED,KAAK;AACJ,iBAAK,UAAU,KAAM,QAAS,MAAM,WAAY,CAAE;AAClD;AAAA,UAED;AACC;AAAA,QAEF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,cAAe,WAAW,QAAS;AAE3C,YAAM,WAAW,CAAC;AAClB,YAAM,iBAAiB,CAAC;AAExB,UAAI,GAAG,GAAG;AAKV,WAAM,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAO;AAEzC,cAAM,WAAW,UAAW,CAAE;AAE9B,YAAI;AAEJ,YAAK,QAAS,QAAS,GAAI;AAE1B,iBAAO,QAAS,QAAS;AACzB,6BAAoB,MAAM,QAAQ,QAAS;AAAA,QAE5C,WAAY,eAAgB,QAAS,GAAI;AAIxC,gBAAM,cAAc,QAAQ,aAAc,QAAS;AACnD,gBAAM,WAAW,YAAY;AAE7B,mBAAUS,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAO;AAE5C,kBAAM,QAAQ,SAAUA,EAAE;AAE1B,gBAAK,MAAM,SAAS,SAAU;AAE7B,oBAAMC,QAAO,QAAS,MAAM,EAAG;AAC/B,iCAAoBA,OAAM,QAAQ,QAAS;AAAA,YAE5C;AAAA,UAED;AAAA,QAED,OAAO;AAEN,kBAAQ,MAAO,sEAAsE,QAAS;AAAA,QAE/F;AAAA,MAED;AAIA,WAAM,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAEtC,aAAM,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAExC,iBAAO,SAAU,CAAE;AAEnB,cAAK,KAAK,KAAK,SAAS,OAAQ,CAAE,EAAE,MAAO;AAE1C,2BAAgB,CAAE,IAAI;AACtB,iBAAK,YAAY;AACjB;AAAA,UAED;AAAA,QAED;AAAA,MAED;AAIA,WAAM,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAExC,eAAO,SAAU,CAAE;AAEnB,YAAK,KAAK,cAAc,OAAQ;AAE/B,yBAAe,KAAM,IAAK;AAC1B,eAAK,YAAY;AAAA,QAElB;AAAA,MAED;AAIA,YAAM,QAAQ,CAAC;AACf,YAAM,eAAe,CAAC;AAEtB,WAAM,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAO;AAE9C,eAAO,eAAgB,CAAE;AAEzB,cAAM,KAAM,KAAK,IAAK;AACtB,qBAAa,KAAM,KAAK,WAAY;AAAA,MAErC;AAEA,aAAO,IAAI,SAAU,OAAO,YAAa;AAAA,IAE1C;AAEA,aAAS,mBAAoB,MAAM,QAAQ,UAAW;AAIrD,WAAK,SAAU,SAAW,QAAS;AAElC,YAAK,OAAO,WAAW,MAAO;AAE7B,cAAI;AAIJ,mBAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAE1C,kBAAM,QAAQ,OAAQ,CAAE;AAExB,gBAAK,MAAM,SAAS,OAAO,MAAO;AAEjC,4BAAc,MAAM;AACpB;AAAA,YAED;AAAA,UAED;AAEA,cAAK,gBAAgB,QAAY;AAQhC,0BAAc,IAAI,QAAQ;AAAA,UAE3B;AAEA,mBAAS,KAAM,EAAE,MAAM,QAAQ,aAA0B,WAAW,MAAM,CAAE;AAAA,QAE7E;AAAA,MAED,CAAE;AAAA,IAEH;AAEA,aAAS,UAAW,MAAO;AAE1B,YAAM,UAAU,CAAC;AAEjB,YAAMF,UAAS,KAAK;AACpB,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAO,KAAK;AAClB,YAAM,kBAAkB,KAAK;AAC7B,YAAM,sBAAsB,KAAK;AACjC,YAAM,iBAAiB,KAAK;AAC5B,YAAM,qBAAqB,KAAK;AAChC,YAAM,gBAAgB,KAAK;AAI3B,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,gBAAQ,KAAM,QAAS,MAAO,CAAE,CAAE,CAAE;AAAA,MAErC;AAIA,eAAU,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAO;AAE1D,cAAM,iBAAiB,UAAW,gBAAiB,CAAE,CAAE;AAEvD,YAAK,mBAAmB,MAAO;AAE9B,kBAAQ,KAAM,eAAe,MAAM,CAAE;AAAA,QAEtC;AAAA,MAED;AAIA,eAAU,IAAI,GAAG,IAAI,oBAAoB,QAAQ,IAAI,GAAG,KAAO;AAE9D,cAAM,WAAW,oBAAqB,CAAE;AACxC,cAAM,aAAa,cAAe,SAAS,EAAG;AAC9C,cAAM,aAAa,YAAa,WAAW,EAAG;AAC9C,cAAM,aAAa,aAAc,YAAY,SAAS,SAAU;AAEhE,cAAM,YAAY,SAAS;AAC3B,cAAM,SAAS,WAAW,KAAK;AAE/B,cAAM,WAAW,cAAe,WAAW,MAAO;AAElD,iBAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,gBAAMG,UAAS,WAAY,CAAE;AAE7B,cAAKA,QAAO,eAAgB;AAE3B,YAAAA,QAAO,KAAM,UAAU,WAAW,KAAK,UAAW;AAClD,YAAAA,QAAO,qBAAqB;AAAA,UAE7B;AAEA,kBAAQ,KAAMA,OAAO;AAAA,QAEtB;AAAA,MAED;AAIA,eAAU,IAAI,GAAG,IAAI,eAAe,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,gBAAgB,SAAU,eAAgB,CAAE,CAAE;AAEpD,YAAK,kBAAkB,MAAO;AAE7B,kBAAQ,KAAM,cAAc,MAAM,CAAE;AAAA,QAErC;AAAA,MAED;AAIA,eAAU,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAO;AAE7D,cAAM,WAAW,mBAAoB,CAAE;AAKvC,cAAM,aAAa,YAAa,SAAS,EAAG;AAC5C,cAAM,aAAa,aAAc,YAAY,SAAS,SAAU;AAEhE,iBAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,kBAAQ,KAAM,WAAY,CAAE,CAAE;AAAA,QAE/B;AAAA,MAED;AAIA,eAAU,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAO;AAExD,gBAAQ,KAAM,QAAS,cAAe,CAAE,CAAE,EAAE,MAAM,CAAE;AAAA,MAErD;AAEA,UAAI;AAEJ,UAAK,MAAM,WAAW,KAAK,QAAQ,WAAW,GAAI;AAEjD,iBAAS,QAAS,CAAE;AAAA,MAErB,OAAO;AAEN,iBAAW,SAAS,UAAY,IAAI,KAAK,IAAI,IAAI,MAAM;AAEvD,iBAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAO;AAE3C,iBAAO,IAAK,QAAS,CAAE,CAAE;AAAA,QAE1B;AAAA,MAED;AAEA,aAAO,OAAS,SAAS,UAAY,KAAK,MAAM,KAAK;AACrD,aAAO,OAAO,KAAMH,OAAO;AAC3B,aAAO,OAAO,UAAW,OAAO,UAAU,OAAO,YAAY,OAAO,KAAM;AAE1E,aAAO;AAAA,IAER;AAEA,UAAM,mBAAmB,IAAI,kBAAmB;AAAA,MAC/C,MAAM,OAAO;AAAA,MACb,OAAO;AAAA,IACR,CAAE;AAEF,aAAS,uBAAwB,MAAM,mBAAoB;AAE1D,YAAM,YAAY,CAAC;AAEnB,eAAU,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAO;AAE/C,cAAM,KAAK,kBAAmB,KAAM,CAAE,CAAE;AAExC,YAAK,OAAO,QAAY;AAEvB,kBAAQ,KAAM,iFAAiF,KAAM,CAAE,CAAE;AACzG,oBAAU,KAAM,gBAAiB;AAAA,QAElC,OAAO;AAEN,oBAAU,KAAM,YAAa,EAAG,CAAE;AAAA,QAEnC;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,aAAc,YAAY,mBAAoB;AAEtD,YAAM,UAAU,CAAC;AAEjB,iBAAY,QAAQ,YAAa;AAEhC,cAAM,WAAW,WAAY,IAAK;AAElC,cAAM,YAAY,uBAAwB,SAAS,cAAc,iBAAkB;AAInF,YAAK,UAAU,WAAW,GAAI;AAE7B,cAAK,SAAS,WAAW,SAAS,cAAe;AAEhD,sBAAU,KAAM,IAAI,kBAAkB,CAAE;AAAA,UAEzC,OAAO;AAEN,sBAAU,KAAM,IAAI,kBAAkB,CAAE;AAAA,UAEzC;AAAA,QAED;AAIA,YAAK,SAAS,WAAW,SAAS,cAAe;AAEhD,mBAAU,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAO;AAEpD,kBAAMI,YAAW,UAAW,CAAE;AAE9B,gBAAKA,UAAS,wBAAwB,QAAQA,UAAS,0BAA0B,MAAO;AAEvF,oBAAM,eAAe,IAAI,kBAAkB;AAI3C,2BAAa,MAAM,KAAMA,UAAS,KAAM;AACxC,2BAAa,UAAUA,UAAS;AAChC,2BAAa,cAAcA,UAAS;AAIpC,wBAAW,CAAE,IAAI;AAAA,YAElB;AAAA,UAED;AAAA,QAED;AAIA,cAAM,WAAa,SAAS,KAAK,WAAW,cAAc;AAI1D,cAAM,WAAa,UAAU,WAAW,IAAM,UAAW,CAAE,IAAI;AAI/D,YAAI;AAEJ,gBAAS,MAAO;AAAA,UAEf,KAAK;AACJ,qBAAS,IAAI,aAAc,SAAS,MAAM,QAAS;AACnD;AAAA,UAED,KAAK;AACJ,qBAAS,IAAI,KAAM,SAAS,MAAM,QAAS;AAC3C;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AACJ,gBAAK,UAAW;AAEf,uBAAS,IAAI,YAAa,SAAS,MAAM,QAAS;AAAA,YAEnD,OAAO;AAEN,uBAAS,IAAI,KAAM,SAAS,MAAM,QAAS;AAAA,YAE5C;AAEA;AAAA,QAEF;AAEA,gBAAQ,KAAM,MAAO;AAAA,MAEtB;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,QAAS,IAAK;AAEtB,aAAO,QAAQ,MAAO,EAAG,MAAM;AAAA,IAEhC;AAEA,aAAS,QAAS,IAAK;AAEtB,aAAO,SAAU,QAAQ,MAAO,EAAG,GAAG,SAAU;AAAA,IAEjD;AAIA,aAAS,iBAAkBZ,MAAM;AAEhC,YAAM,OAAO;AAAA,QACZ,MAAMA,KAAI,aAAc,MAAO;AAAA,QAC/B,UAAU,CAAC;AAAA,MACZ;AAEA,mBAAcA,IAAI;AAElB,YAAM,WAAW,qBAAsBA,MAAK,MAAO;AAEnD,eAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,aAAK,SAAS,KAAM,UAAW,SAAU,CAAE,CAAE,CAAE;AAAA,MAEhD;AAEA,cAAQ,aAAcA,KAAI,aAAc,IAAK,CAAE,IAAI;AAAA,IAEpD;AAEA,aAAS,iBAAkB,MAAO;AAEjC,YAAM,QAAQ,IAAI,MAAM;AACxB,YAAM,OAAO,KAAK;AAElB,YAAM,WAAW,KAAK;AAEtB,eAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,cAAM,QAAQ,SAAU,CAAE;AAE1B,cAAM,IAAK,QAAS,MAAM,EAAG,CAAE;AAAA,MAEhC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,eAAgB,IAAK;AAE7B,aAAO,QAAQ,aAAc,EAAG,MAAM;AAAA,IAEvC;AAEA,aAAS,eAAgB,IAAK;AAE7B,aAAO,SAAU,QAAQ,aAAc,EAAG,GAAG,gBAAiB;AAAA,IAE/D;AAIA,aAAS,WAAYA,MAAM;AAE1B,YAAM,WAAW,qBAAsBA,MAAK,uBAAwB,EAAG,CAAE;AACzE,aAAO,eAAgB,QAAS,SAAS,aAAc,KAAM,CAAE,CAAE;AAAA,IAElE;AAEA,aAAS,kBAAkB;AAE1B,YAAM,QAAQ,QAAQ;AAEtB,UAAK,QAAS,KAAM,MAAM,MAAO;AAEhC,YAAK,QAAS,QAAQ,UAAW,MAAM,OAAQ;AAI9C,gBAAM,SAAS,CAAC;AAEhB,qBAAY,MAAM,QAAQ,YAAa;AAEtC,kBAAM,kBAAkB,aAAc,EAAG;AAEzC,qBAAU,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAO;AAE1D,qBAAO,KAAM,gBAAiB,CAAE,CAAE;AAAA,YAEnC;AAAA,UAED;AAEA,qBAAW,KAAM,IAAI,cAAe,WAAW,IAAK,MAAO,CAAE;AAAA,QAE9D;AAAA,MAED,OAAO;AAEN,mBAAY,MAAM,OAAQ;AAEzB,qBAAW,KAAM,iBAAkB,EAAG,CAAE;AAAA,QAEzC;AAAA,MAED;AAAA,IAED;AAKA,aAAS,kBAAmBa,cAAc;AAEzC,UAAI,SAAS;AACb,YAAM,QAAQ,CAAEA,YAAY;AAE5B,aAAQ,MAAM,QAAS;AAEtB,cAAM,OAAO,MAAM,MAAM;AAEzB,YAAK,KAAK,aAAa,KAAK,WAAY;AAEvC,oBAAU,KAAK;AAAA,QAEhB,OAAO;AAEN,oBAAU;AACV,gBAAM,KAAK,MAAO,OAAO,KAAK,UAAW;AAAA,QAE1C;AAAA,MAED;AAEA,aAAO,OAAO,KAAK;AAAA,IAEpB;AAEA,QAAK,KAAK,WAAW,GAAI;AAExB,aAAO,EAAE,OAAO,IAAI,MAAM,EAAE;AAAA,IAE7B;AAEA,UAAM,MAAM,IAAI,UAAU,EAAE,gBAAiB,MAAM,iBAAkB;AAErE,UAAM,UAAU,qBAAsB,KAAK,SAAU,EAAG,CAAE;AAE1D,UAAM,cAAc,IAAI,qBAAsB,aAAc,EAAG,CAAE;AACjE,QAAK,gBAAgB,QAAY;AAIhC,YAAM,eAAe,qBAAsB,aAAa,KAAM,EAAG,CAAE;AACnE,UAAI;AAEJ,UAAK,cAAe;AAEnB,oBAAY,aAAa;AAAA,MAE1B,OAAO;AAEN,oBAAY,kBAAmB,WAAY;AAAA,MAE5C;AAEA,cAAQ,MAAO,wDAAwD,SAAU;AAEjF,aAAO;AAAA,IAER;AAIA,UAAM,UAAU,QAAQ,aAAc,SAAU;AAChD,YAAQ,MAAO,qCAAqC,OAAQ;AAE5D,UAAM,QAAQ,WAAY,qBAAsB,SAAS,OAAQ,EAAG,CAAE,CAAE;AACxE,UAAM,gBAAgB,IAAI,cAAe,KAAK,OAAQ;AACtD,kBAAc,QAAS,KAAK,gBAAgB,IAAK,EAAE,eAAgB,KAAK,WAAY;AAEpF,QAAI;AAEJ,QAAK,WAAY;AAEhB,kBAAY,IAAI,UAAW,KAAK,OAAQ;AACxC,gBAAU,QAAS,KAAK,gBAAgB,IAAK;AAAA,IAE9C;AAIA,UAAM,YAAY,IAAI,MAAM;AAC5B,UAAM,aAAa,CAAC;AACpB,QAAI,aAAa,CAAC;AAClB,QAAI,QAAQ;AAIZ,UAAM,UAAU;AAAA,MACf,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,aAAa,CAAC;AAAA,MACd,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC;AAAA,MACV,WAAW,CAAC;AAAA,MACZ,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,cAAc,CAAC;AAAA,MACf,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,kBAAkB,CAAC;AAAA,IACpB;AAEA,iBAAc,SAAS,sBAAsB,aAAa,cAAe;AACzE,iBAAc,SAAS,2BAA2B,kBAAkB,kBAAmB;AACvF,iBAAc,SAAS,uBAAuB,cAAc,eAAgB;AAC5E,iBAAc,SAAS,kBAAkB,SAAS,UAAW;AAC7D,iBAAc,SAAS,mBAAmB,UAAU,WAAY;AAChE,iBAAc,SAAS,qBAAqB,YAAY,aAAc;AACtE,iBAAc,SAAS,mBAAmB,UAAU,WAAY;AAChE,iBAAc,SAAS,kBAAkB,SAAS,UAAW;AAC7D,iBAAc,SAAS,sBAAsB,YAAY,aAAc;AACvE,iBAAc,SAAS,iBAAiB,QAAQ,SAAU;AAC1D,iBAAc,SAAS,yBAAyB,gBAAgB,gBAAiB;AACjF,iBAAc,SAAS,6BAA6B,oBAAoB,oBAAqB;AAC7F,iBAAc,SAAS,0BAA0B,iBAAiB,iBAAkB;AACpF,iBAAc,SAAS,SAAS,6BAA6B,oBAAqB;AAElF,iBAAc,QAAQ,YAAY,cAAe;AACjD,iBAAc,QAAQ,OAAO,kBAAmB;AAChD,iBAAc,QAAQ,aAAa,eAAgB;AACnD,iBAAc,QAAQ,QAAQ,UAAW;AACzC,iBAAc,QAAQ,SAAS,WAAY;AAC3C,iBAAc,QAAQ,WAAW,aAAc;AAC/C,iBAAc,QAAQ,SAAS,WAAY;AAC3C,iBAAc,QAAQ,QAAQ,UAAW;AACzC,iBAAc,QAAQ,YAAY,aAAc;AAChD,iBAAc,QAAQ,cAAc,gBAAiB;AAErD,oBAAgB;AAChB,oBAAgB;AAEhB,UAAM,QAAQ,WAAY,qBAAsB,SAAS,OAAQ,EAAG,CAAE,CAAE;AACxE,UAAM,aAAa;AAEnB,QAAK,MAAM,WAAW,QAAS;AAE9B,cAAQ,KAAM,0LAA2L;AACzM,YAAM,SAAS,IAAK,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE;AAAA,IAEzC;AAEA,UAAM,MAAM,eAAgB,MAAM,IAAK;AAEvC,WAAO;AAAA,MACN,IAAI,aAAa;AAEhB,gBAAQ,KAAM,0EAA2E;AACzF,eAAO;AAAA,MAER;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAED;", "names": ["header", "use_rle", "use_pal", "offset", "imageData", "xml", "text", "library", "animations", "technique", "count", "position", "vector", "matrix", "j", "root", "object", "material", "parserE<PERSON>r"]}