{"version": 3, "sources": ["../../three/examples/jsm/libs/fflate.module.js", "../../three/examples/jsm/curves/NURBSUtils.js", "../../three/examples/jsm/curves/NURBSCurve.js", "../../three/examples/jsm/loaders/FBXLoader.js"], "sourcesContent": ["/*!\nfflate - fast JavaScript compression/decompression\n<https://101arrowz.github.io/fflate>\nLicensed under MIT. https://github.com/101arrowz/fflate/blob/master/LICENSE\nversion 0.6.9\n*/\n\n// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar durl = function (c) { return URL.createObjectURL(new Blob([c], { type: 'text/javascript' })); };\nvar cwk = function (u) { return new Worker(u); };\ntry {\n    URL.revokeObjectURL(durl(''));\n}\ncatch (e) {\n    // We're in Deno or a very old browser\n    durl = function (c) { return 'data:application/javascript;charset=UTF-8,' + encodeURI(c); };\n    // If Deno, this is necessary; if not, this changes nothing\n    cwk = function (u) { return new Worker(u, { type: 'module' }); };\n}\nvar wk = (function (c, id, msg, transfer, cb) {\n    var w = cwk(ch2[id] || (ch2[id] = durl(c)));\n    w.onerror = function (e) { return cb(e.error, null); };\n    w.onmessage = function (e) { return cb(null, e.data); };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new u32(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && 0xEDB88320) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n", "import {\n\tVector3,\n\tVector4\n} from 'three';\n\n/**\n * NURBS utils\n *\n * See NURBSCurve and NURBSSurface.\n **/\n\n\n/**************************************************************\n *\tNURBS Utils\n **************************************************************/\n\n/*\nFinds knot vector span.\n\np : degree\nu : parametric value\nU : knot vector\n\nreturns the span\n*/\nfunction findSpan( p, u, U ) {\n\n\tconst n = U.length - p - 1;\n\n\tif ( u >= U[ n ] ) {\n\n\t\treturn n - 1;\n\n\t}\n\n\tif ( u <= U[ p ] ) {\n\n\t\treturn p;\n\n\t}\n\n\tlet low = p;\n\tlet high = n;\n\tlet mid = Math.floor( ( low + high ) / 2 );\n\n\twhile ( u < U[ mid ] || u >= U[ mid + 1 ] ) {\n\n\t\tif ( u < U[ mid ] ) {\n\n\t\t\thigh = mid;\n\n\t\t} else {\n\n\t\t\tlow = mid;\n\n\t\t}\n\n\t\tmid = Math.floor( ( low + high ) / 2 );\n\n\t}\n\n\treturn mid;\n\n}\n\n\n/*\nCalculate basis functions. See The NURBS Book, page 70, algorithm A2.2\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nU    : knot vector\n\nreturns array[p+1] with basis functions values.\n*/\nfunction calcBasisFunctions( span, u, p, U ) {\n\n\tconst N = [];\n\tconst left = [];\n\tconst right = [];\n\tN[ 0 ] = 1.0;\n\n\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\tleft[ j ] = u - U[ span + 1 - j ];\n\t\tright[ j ] = U[ span + j ] - u;\n\n\t\tlet saved = 0.0;\n\n\t\tfor ( let r = 0; r < j; ++ r ) {\n\n\t\t\tconst rv = right[ r + 1 ];\n\t\t\tconst lv = left[ j - r ];\n\t\t\tconst temp = N[ r ] / ( rv + lv );\n\t\t\tN[ r ] = saved + rv * temp;\n\t\t\tsaved = lv * temp;\n\n\t\t}\n\n\t\tN[ j ] = saved;\n\n\t}\n\n\treturn N;\n\n}\n\n\n/*\nCalculate B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n\np : degree of B-Spline\nU : knot vector\nP : control points (x, y, z, w)\nu : parametric point\n\nreturns point for given u\n*/\nfunction calcBSplinePoint( p, U, P, u ) {\n\n\tconst span = findSpan( p, u, U );\n\tconst N = calcBasisFunctions( span, u, p, U );\n\tconst C = new Vector4( 0, 0, 0, 0 );\n\n\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\tconst point = P[ span - p + j ];\n\t\tconst Nj = N[ j ];\n\t\tconst wNj = point.w * Nj;\n\t\tC.x += point.x * wNj;\n\t\tC.y += point.y * wNj;\n\t\tC.z += point.z * wNj;\n\t\tC.w += point.w * Nj;\n\n\t}\n\n\treturn C;\n\n}\n\n\n/*\nCalculate basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nn    : number of derivatives to calculate\nU    : knot vector\n\nreturns array[n+1][p+1] with basis functions derivatives\n*/\nfunction calcBasisFunctionDerivatives( span, u, p, n, U ) {\n\n\tconst zeroArr = [];\n\tfor ( let i = 0; i <= p; ++ i )\n\t\tzeroArr[ i ] = 0.0;\n\n\tconst ders = [];\n\n\tfor ( let i = 0; i <= n; ++ i )\n\t\tders[ i ] = zeroArr.slice( 0 );\n\n\tconst ndu = [];\n\n\tfor ( let i = 0; i <= p; ++ i )\n\t\tndu[ i ] = zeroArr.slice( 0 );\n\n\tndu[ 0 ][ 0 ] = 1.0;\n\n\tconst left = zeroArr.slice( 0 );\n\tconst right = zeroArr.slice( 0 );\n\n\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\tleft[ j ] = u - U[ span + 1 - j ];\n\t\tright[ j ] = U[ span + j ] - u;\n\n\t\tlet saved = 0.0;\n\n\t\tfor ( let r = 0; r < j; ++ r ) {\n\n\t\t\tconst rv = right[ r + 1 ];\n\t\t\tconst lv = left[ j - r ];\n\t\t\tndu[ j ][ r ] = rv + lv;\n\n\t\t\tconst temp = ndu[ r ][ j - 1 ] / ndu[ j ][ r ];\n\t\t\tndu[ r ][ j ] = saved + rv * temp;\n\t\t\tsaved = lv * temp;\n\n\t\t}\n\n\t\tndu[ j ][ j ] = saved;\n\n\t}\n\n\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\tders[ 0 ][ j ] = ndu[ j ][ p ];\n\n\t}\n\n\tfor ( let r = 0; r <= p; ++ r ) {\n\n\t\tlet s1 = 0;\n\t\tlet s2 = 1;\n\n\t\tconst a = [];\n\t\tfor ( let i = 0; i <= p; ++ i ) {\n\n\t\t\ta[ i ] = zeroArr.slice( 0 );\n\n\t\t}\n\n\t\ta[ 0 ][ 0 ] = 1.0;\n\n\t\tfor ( let k = 1; k <= n; ++ k ) {\n\n\t\t\tlet d = 0.0;\n\t\t\tconst rk = r - k;\n\t\t\tconst pk = p - k;\n\n\t\t\tif ( r >= k ) {\n\n\t\t\t\ta[ s2 ][ 0 ] = a[ s1 ][ 0 ] / ndu[ pk + 1 ][ rk ];\n\t\t\t\td = a[ s2 ][ 0 ] * ndu[ rk ][ pk ];\n\n\t\t\t}\n\n\t\t\tconst j1 = ( rk >= - 1 ) ? 1 : - rk;\n\t\t\tconst j2 = ( r - 1 <= pk ) ? k - 1 : p - r;\n\n\t\t\tfor ( let j = j1; j <= j2; ++ j ) {\n\n\t\t\t\ta[ s2 ][ j ] = ( a[ s1 ][ j ] - a[ s1 ][ j - 1 ] ) / ndu[ pk + 1 ][ rk + j ];\n\t\t\t\td += a[ s2 ][ j ] * ndu[ rk + j ][ pk ];\n\n\t\t\t}\n\n\t\t\tif ( r <= pk ) {\n\n\t\t\t\ta[ s2 ][ k ] = - a[ s1 ][ k - 1 ] / ndu[ pk + 1 ][ r ];\n\t\t\t\td += a[ s2 ][ k ] * ndu[ r ][ pk ];\n\n\t\t\t}\n\n\t\t\tders[ k ][ r ] = d;\n\n\t\t\tconst j = s1;\n\t\t\ts1 = s2;\n\t\t\ts2 = j;\n\n\t\t}\n\n\t}\n\n\tlet r = p;\n\n\tfor ( let k = 1; k <= n; ++ k ) {\n\n\t\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\t\tders[ k ][ j ] *= r;\n\n\t\t}\n\n\t\tr *= p - k;\n\n\t}\n\n\treturn ders;\n\n}\n\n\n/*\n\tCalculate derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n\n\tp  : degree\n\tU  : knot vector\n\tP  : control points\n\tu  : Parametric points\n\tnd : number of derivatives\n\n\treturns array[d+1] with derivatives\n\t*/\nfunction calcBSplineDerivatives( p, U, P, u, nd ) {\n\n\tconst du = nd < p ? nd : p;\n\tconst CK = [];\n\tconst span = findSpan( p, u, U );\n\tconst nders = calcBasisFunctionDerivatives( span, u, p, du, U );\n\tconst Pw = [];\n\n\tfor ( let i = 0; i < P.length; ++ i ) {\n\n\t\tconst point = P[ i ].clone();\n\t\tconst w = point.w;\n\n\t\tpoint.x *= w;\n\t\tpoint.y *= w;\n\t\tpoint.z *= w;\n\n\t\tPw[ i ] = point;\n\n\t}\n\n\tfor ( let k = 0; k <= du; ++ k ) {\n\n\t\tconst point = Pw[ span - p ].clone().multiplyScalar( nders[ k ][ 0 ] );\n\n\t\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\t\tpoint.add( Pw[ span - p + j ].clone().multiplyScalar( nders[ k ][ j ] ) );\n\n\t\t}\n\n\t\tCK[ k ] = point;\n\n\t}\n\n\tfor ( let k = du + 1; k <= nd + 1; ++ k ) {\n\n\t\tCK[ k ] = new Vector4( 0, 0, 0 );\n\n\t}\n\n\treturn CK;\n\n}\n\n\n/*\nCalculate \"K over I\"\n\nreturns k!/(i!(k-i)!)\n*/\nfunction calcKoverI( k, i ) {\n\n\tlet nom = 1;\n\n\tfor ( let j = 2; j <= k; ++ j ) {\n\n\t\tnom *= j;\n\n\t}\n\n\tlet denom = 1;\n\n\tfor ( let j = 2; j <= i; ++ j ) {\n\n\t\tdenom *= j;\n\n\t}\n\n\tfor ( let j = 2; j <= k - i; ++ j ) {\n\n\t\tdenom *= j;\n\n\t}\n\n\treturn nom / denom;\n\n}\n\n\n/*\nCalculate derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n\nPders : result of function calcBSplineDerivatives\n\nreturns array with derivatives for rational curve.\n*/\nfunction calcRationalCurveDerivatives( Pders ) {\n\n\tconst nd = Pders.length;\n\tconst Aders = [];\n\tconst wders = [];\n\n\tfor ( let i = 0; i < nd; ++ i ) {\n\n\t\tconst point = Pders[ i ];\n\t\tAders[ i ] = new Vector3( point.x, point.y, point.z );\n\t\twders[ i ] = point.w;\n\n\t}\n\n\tconst CK = [];\n\n\tfor ( let k = 0; k < nd; ++ k ) {\n\n\t\tconst v = Aders[ k ].clone();\n\n\t\tfor ( let i = 1; i <= k; ++ i ) {\n\n\t\t\tv.sub( CK[ k - i ].clone().multiplyScalar( calcKoverI( k, i ) * wders[ i ] ) );\n\n\t\t}\n\n\t\tCK[ k ] = v.divideScalar( wders[ 0 ] );\n\n\t}\n\n\treturn CK;\n\n}\n\n\n/*\nCalculate NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n\np  : degree\nU  : knot vector\nP  : control points in homogeneous space\nu  : parametric points\nnd : number of derivatives\n\nreturns array with derivatives.\n*/\nfunction calcNURBSDerivatives( p, U, P, u, nd ) {\n\n\tconst Pders = calcBSplineDerivatives( p, U, P, u, nd );\n\treturn calcRationalCurveDerivatives( Pders );\n\n}\n\n\n/*\nCalculate rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n\np1, p2 : degrees of B-Spline surface\nU1, U2 : knot vectors\nP      : control points (x, y, z, w)\nu, v   : parametric values\n\nreturns point for given (u, v)\n*/\nfunction calcSurfacePoint( p, q, U, V, P, u, v, target ) {\n\n\tconst uspan = findSpan( p, u, U );\n\tconst vspan = findSpan( q, v, V );\n\tconst Nu = calcBasisFunctions( uspan, u, p, U );\n\tconst Nv = calcBasisFunctions( vspan, v, q, V );\n\tconst temp = [];\n\n\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\ttemp[ l ] = new Vector4( 0, 0, 0, 0 );\n\t\tfor ( let k = 0; k <= p; ++ k ) {\n\n\t\t\tconst point = P[ uspan - p + k ][ vspan - q + l ].clone();\n\t\t\tconst w = point.w;\n\t\t\tpoint.x *= w;\n\t\t\tpoint.y *= w;\n\t\t\tpoint.z *= w;\n\t\t\ttemp[ l ].add( point.multiplyScalar( Nu[ k ] ) );\n\n\t\t}\n\n\t}\n\n\tconst Sw = new Vector4( 0, 0, 0, 0 );\n\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\tSw.add( temp[ l ].multiplyScalar( Nv[ l ] ) );\n\n\t}\n\n\tSw.divideScalar( Sw.w );\n\ttarget.set( Sw.x, Sw.y, Sw.z );\n\n}\n\n\n\nexport {\n\tfindSpan,\n\tcalcBasisFunctions,\n\tcalcBSplinePoint,\n\tcalcBasisFunctionDerivatives,\n\tcalcBSplineDerivatives,\n\tcalcKoverI,\n\tcalcRationalCurveDerivatives,\n\tcalcNURBSDerivatives,\n\tcalcSurfacePoint,\n};\n", "import {\n\tCurve,\n\tVector3,\n\tVector4\n} from 'three';\nimport * as NURBSUtils from '../curves/NURBSUtils.js';\n\n/**\n * NURBS curve object\n *\n * Derives from Curve, overriding getPoint and getTangent.\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n *\n **/\n\nclass NURBSCurve extends Curve {\n\n\tconstructor(\n\t\tdegree,\n\t\tknots /* array of reals */,\n\t\tcontrolPoints /* array of Vector(2|3|4) */,\n\t\tstartKnot /* index in knots */,\n\t\tendKnot /* index in knots */\n\t) {\n\n\t\tsuper();\n\n\t\tthis.degree = degree;\n\t\tthis.knots = knots;\n\t\tthis.controlPoints = [];\n\t\t// Used by periodic NURBS to remove hidden spans\n\t\tthis.startKnot = startKnot || 0;\n\t\tthis.endKnot = endKnot || ( this.knots.length - 1 );\n\n\t\tfor ( let i = 0; i < controlPoints.length; ++ i ) {\n\n\t\t\t// ensure Vector4 for control points\n\t\t\tconst point = controlPoints[ i ];\n\t\t\tthis.controlPoints[ i ] = new Vector4( point.x, point.y, point.z, point.w );\n\n\t\t}\n\n\t}\n\n\tgetPoint( t, optionalTarget = new Vector3() ) {\n\n\t\tconst point = optionalTarget;\n\n\t\tconst u = this.knots[ this.startKnot ] + t * ( this.knots[ this.endKnot ] - this.knots[ this.startKnot ] ); // linear mapping t->u\n\n\t\t// following results in (wx, wy, wz, w) homogeneous point\n\t\tconst hpoint = NURBSUtils.calcBSplinePoint( this.degree, this.knots, this.controlPoints, u );\n\n\t\tif ( hpoint.w !== 1.0 ) {\n\n\t\t\t// project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n\t\t\thpoint.divideScalar( hpoint.w );\n\n\t\t}\n\n\t\treturn point.set( hpoint.x, hpoint.y, hpoint.z );\n\n\t}\n\n\tgetTangent( t, optionalTarget = new Vector3() ) {\n\n\t\tconst tangent = optionalTarget;\n\n\t\tconst u = this.knots[ 0 ] + t * ( this.knots[ this.knots.length - 1 ] - this.knots[ 0 ] );\n\t\tconst ders = NURBSUtils.calcNURBSDerivatives( this.degree, this.knots, this.controlPoints, u, 1 );\n\t\ttangent.copy( ders[ 1 ] ).normalize();\n\n\t\treturn tangent;\n\n\t}\n\n}\n\nexport { NURBSCurve };\n", "import {\n\tAmbientLight,\n\tAnimationClip,\n\tBone,\n\tBufferGeometry,\n\tClampToEdgeWrapping,\n\tColor,\n\tDirectionalLight,\n\tEquirectangularReflectionMapping,\n\tEuler,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tGroup,\n\tLine,\n\tLineBasicMaterial,\n\tLoader,\n\tLoaderUtils,\n\tMathUtils,\n\tMatrix3,\n\tMatrix4,\n\tMesh,\n\tMeshLambertMaterial,\n\tMeshPhongMaterial,\n\tNumberKeyframeTrack,\n\tObject3D,\n\tOrthographicCamera,\n\tPerspectiveCamera,\n\tPointLight,\n\tPropertyBinding,\n\tQuaternion,\n\tQuaternionKeyframeTrack,\n\tRepeatWrapping,\n\tSkeleton,\n\tSkinnedMesh,\n\tSpotLight,\n\tTexture,\n\tTextureLoader,\n\tUint16BufferAttribute,\n\tVector2,\n\tVector3,\n\tVector4,\n\tVectorKeyframeTrack,\n\tSRGBColorSpace,\n\tShapeUtils\n} from 'three';\nimport * as fflate from '../libs/fflate.module.js';\nimport { NURBSCurve } from '../curves/NURBSCurve.js';\n\n/**\n * Loader loads FBX file and generates Group representing FBX scene.\n * Requires FBX file to be >= 7.0 and in ASCII or >= 6400 in Binary format\n * Versions lower than this may load but will probably have errors\n *\n * Needs Support:\n *  Morph normals / blend shape normals\n *\n * FBX format references:\n * \thttps://help.autodesk.com/view/FBX/2017/ENU/?guid=__cpp_ref_index_html (C++ SDK reference)\n *\n * Binary format specification:\n *\thttps://code.blender.org/2013/08/fbx-binary-file-format-specification/\n */\n\n\nlet fbxTree;\nlet connections;\nlet sceneGraph;\n\nclass FBXLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst path = ( scope.path === '' ) ? LoaderUtils.extractUrlBase( url ) : scope.path;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( scope.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( scope.requestHeader );\n\t\tloader.setWithCredentials( scope.withCredentials );\n\n\t\tloader.load( url, function ( buffer ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( buffer, path ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tparse( FBXBuffer, path ) {\n\n\t\tif ( isFbxFormatBinary( FBXBuffer ) ) {\n\n\t\t\tfbxTree = new BinaryParser().parse( FBXBuffer );\n\n\t\t} else {\n\n\t\t\tconst FBXText = convertArrayBufferToString( FBXBuffer );\n\n\t\t\tif ( ! isFbxFormatASCII( FBXText ) ) {\n\n\t\t\t\tthrow new Error( 'THREE.FBXLoader: Unknown format.' );\n\n\t\t\t}\n\n\t\t\tif ( getFbxVersion( FBXText ) < 7000 ) {\n\n\t\t\t\tthrow new Error( 'THREE.FBXLoader: FBX version not supported, FileVersion: ' + getFbxVersion( FBXText ) );\n\n\t\t\t}\n\n\t\t\tfbxTree = new TextParser().parse( FBXText );\n\n\t\t}\n\n\t\t// console.log( fbxTree );\n\n\t\tconst textureLoader = new TextureLoader( this.manager ).setPath( this.resourcePath || path ).setCrossOrigin( this.crossOrigin );\n\n\t\treturn new FBXTreeParser( textureLoader, this.manager ).parse( fbxTree );\n\n\t}\n\n}\n\n// Parse the FBXTree object returned by the BinaryParser or TextParser and return a Group\nclass FBXTreeParser {\n\n\tconstructor( textureLoader, manager ) {\n\n\t\tthis.textureLoader = textureLoader;\n\t\tthis.manager = manager;\n\n\t}\n\n\tparse() {\n\n\t\tconnections = this.parseConnections();\n\n\t\tconst images = this.parseImages();\n\t\tconst textures = this.parseTextures( images );\n\t\tconst materials = this.parseMaterials( textures );\n\t\tconst deformers = this.parseDeformers();\n\t\tconst geometryMap = new GeometryParser().parse( deformers );\n\n\t\tthis.parseScene( deformers, geometryMap, materials );\n\n\t\treturn sceneGraph;\n\n\t}\n\n\t// Parses FBXTree.Connections which holds parent-child connections between objects (e.g. material -> texture, model->geometry )\n\t// and details the connection type\n\tparseConnections() {\n\n\t\tconst connectionMap = new Map();\n\n\t\tif ( 'Connections' in fbxTree ) {\n\n\t\t\tconst rawConnections = fbxTree.Connections.connections;\n\n\t\t\trawConnections.forEach( function ( rawConnection ) {\n\n\t\t\t\tconst fromID = rawConnection[ 0 ];\n\t\t\t\tconst toID = rawConnection[ 1 ];\n\t\t\t\tconst relationship = rawConnection[ 2 ];\n\n\t\t\t\tif ( ! connectionMap.has( fromID ) ) {\n\n\t\t\t\t\tconnectionMap.set( fromID, {\n\t\t\t\t\t\tparents: [],\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t\tconst parentRelationship = { ID: toID, relationship: relationship };\n\t\t\t\tconnectionMap.get( fromID ).parents.push( parentRelationship );\n\n\t\t\t\tif ( ! connectionMap.has( toID ) ) {\n\n\t\t\t\t\tconnectionMap.set( toID, {\n\t\t\t\t\t\tparents: [],\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t\tconst childRelationship = { ID: fromID, relationship: relationship };\n\t\t\t\tconnectionMap.get( toID ).children.push( childRelationship );\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn connectionMap;\n\n\t}\n\n\t// Parse FBXTree.Objects.Video for embedded image data\n\t// These images are connected to textures in FBXTree.Objects.Textures\n\t// via FBXTree.Connections.\n\tparseImages() {\n\n\t\tconst images = {};\n\t\tconst blobs = {};\n\n\t\tif ( 'Video' in fbxTree.Objects ) {\n\n\t\t\tconst videoNodes = fbxTree.Objects.Video;\n\n\t\t\tfor ( const nodeID in videoNodes ) {\n\n\t\t\t\tconst videoNode = videoNodes[ nodeID ];\n\n\t\t\t\tconst id = parseInt( nodeID );\n\n\t\t\t\timages[ id ] = videoNode.RelativeFilename || videoNode.Filename;\n\n\t\t\t\t// raw image data is in videoNode.Content\n\t\t\t\tif ( 'Content' in videoNode ) {\n\n\t\t\t\t\tconst arrayBufferContent = ( videoNode.Content instanceof ArrayBuffer ) && ( videoNode.Content.byteLength > 0 );\n\t\t\t\t\tconst base64Content = ( typeof videoNode.Content === 'string' ) && ( videoNode.Content !== '' );\n\n\t\t\t\t\tif ( arrayBufferContent || base64Content ) {\n\n\t\t\t\t\t\tconst image = this.parseImage( videoNodes[ nodeID ] );\n\n\t\t\t\t\t\tblobs[ videoNode.RelativeFilename || videoNode.Filename ] = image;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( const id in images ) {\n\n\t\t\tconst filename = images[ id ];\n\n\t\t\tif ( blobs[ filename ] !== undefined ) images[ id ] = blobs[ filename ];\n\t\t\telse images[ id ] = images[ id ].split( '\\\\' ).pop();\n\n\t\t}\n\n\t\treturn images;\n\n\t}\n\n\t// Parse embedded image data in FBXTree.Video.Content\n\tparseImage( videoNode ) {\n\n\t\tconst content = videoNode.Content;\n\t\tconst fileName = videoNode.RelativeFilename || videoNode.Filename;\n\t\tconst extension = fileName.slice( fileName.lastIndexOf( '.' ) + 1 ).toLowerCase();\n\n\t\tlet type;\n\n\t\tswitch ( extension ) {\n\n\t\t\tcase 'bmp':\n\n\t\t\t\ttype = 'image/bmp';\n\t\t\t\tbreak;\n\n\t\t\tcase 'jpg':\n\t\t\tcase 'jpeg':\n\n\t\t\t\ttype = 'image/jpeg';\n\t\t\t\tbreak;\n\n\t\t\tcase 'png':\n\n\t\t\t\ttype = 'image/png';\n\t\t\t\tbreak;\n\n\t\t\tcase 'tif':\n\n\t\t\t\ttype = 'image/tiff';\n\t\t\t\tbreak;\n\n\t\t\tcase 'tga':\n\n\t\t\t\tif ( this.manager.getHandler( '.tga' ) === null ) {\n\n\t\t\t\t\tconsole.warn( 'FBXLoader: TGA loader not found, skipping ', fileName );\n\n\t\t\t\t}\n\n\t\t\t\ttype = 'image/tga';\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tconsole.warn( 'FBXLoader: Image type \"' + extension + '\" is not supported.' );\n\t\t\t\treturn;\n\n\t\t}\n\n\t\tif ( typeof content === 'string' ) { // ASCII format\n\n\t\t\treturn 'data:' + type + ';base64,' + content;\n\n\t\t} else { // Binary Format\n\n\t\t\tconst array = new Uint8Array( content );\n\t\t\treturn window.URL.createObjectURL( new Blob( [ array ], { type: type } ) );\n\n\t\t}\n\n\t}\n\n\t// Parse nodes in FBXTree.Objects.Texture\n\t// These contain details such as UV scaling, cropping, rotation etc and are connected\n\t// to images in FBXTree.Objects.Video\n\tparseTextures( images ) {\n\n\t\tconst textureMap = new Map();\n\n\t\tif ( 'Texture' in fbxTree.Objects ) {\n\n\t\t\tconst textureNodes = fbxTree.Objects.Texture;\n\t\t\tfor ( const nodeID in textureNodes ) {\n\n\t\t\t\tconst texture = this.parseTexture( textureNodes[ nodeID ], images );\n\t\t\t\ttextureMap.set( parseInt( nodeID ), texture );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn textureMap;\n\n\t}\n\n\t// Parse individual node in FBXTree.Objects.Texture\n\tparseTexture( textureNode, images ) {\n\n\t\tconst texture = this.loadTexture( textureNode, images );\n\n\t\ttexture.ID = textureNode.id;\n\n\t\ttexture.name = textureNode.attrName;\n\n\t\tconst wrapModeU = textureNode.WrapModeU;\n\t\tconst wrapModeV = textureNode.WrapModeV;\n\n\t\tconst valueU = wrapModeU !== undefined ? wrapModeU.value : 0;\n\t\tconst valueV = wrapModeV !== undefined ? wrapModeV.value : 0;\n\n\t\t// http://download.autodesk.com/us/fbx/SDKdocs/FBX_SDK_Help/files/fbxsdkref/class_k_fbx_texture.html#889640e63e2e681259ea81061b85143a\n\t\t// 0: repeat(default), 1: clamp\n\n\t\ttexture.wrapS = valueU === 0 ? RepeatWrapping : ClampToEdgeWrapping;\n\t\ttexture.wrapT = valueV === 0 ? RepeatWrapping : ClampToEdgeWrapping;\n\n\t\tif ( 'Scaling' in textureNode ) {\n\n\t\t\tconst values = textureNode.Scaling.value;\n\n\t\t\ttexture.repeat.x = values[ 0 ];\n\t\t\ttexture.repeat.y = values[ 1 ];\n\n\t\t}\n\n\t\tif ( 'Translation' in textureNode ) {\n\n\t\t\tconst values = textureNode.Translation.value;\n\n\t\t\ttexture.offset.x = values[ 0 ];\n\t\t\ttexture.offset.y = values[ 1 ];\n\n\t\t}\n\n\t\treturn texture;\n\n\t}\n\n\t// load a texture specified as a blob or data URI, or via an external URL using TextureLoader\n\tloadTexture( textureNode, images ) {\n\n\t\tlet fileName;\n\n\t\tconst currentPath = this.textureLoader.path;\n\n\t\tconst children = connections.get( textureNode.id ).children;\n\n\t\tif ( children !== undefined && children.length > 0 && images[ children[ 0 ].ID ] !== undefined ) {\n\n\t\t\tfileName = images[ children[ 0 ].ID ];\n\n\t\t\tif ( fileName.indexOf( 'blob:' ) === 0 || fileName.indexOf( 'data:' ) === 0 ) {\n\n\t\t\t\tthis.textureLoader.setPath( undefined );\n\n\t\t\t}\n\n\t\t}\n\n\t\tlet texture;\n\n\t\tconst extension = textureNode.FileName.slice( - 3 ).toLowerCase();\n\n\t\tif ( extension === 'tga' ) {\n\n\t\t\tconst loader = this.manager.getHandler( '.tga' );\n\n\t\t\tif ( loader === null ) {\n\n\t\t\t\tconsole.warn( 'FBXLoader: TGA loader not found, creating placeholder texture for', textureNode.RelativeFilename );\n\t\t\t\ttexture = new Texture();\n\n\t\t\t} else {\n\n\t\t\t\tloader.setPath( this.textureLoader.path );\n\t\t\t\ttexture = loader.load( fileName );\n\n\t\t\t}\n\n\t\t} else if ( extension === 'dds' ) {\n\n\t\t\tconst loader = this.manager.getHandler( '.dds' );\n\n\t\t\tif ( loader === null ) {\n\n\t\t\t\tconsole.warn( 'FBXLoader: DDS loader not found, creating placeholder texture for', textureNode.RelativeFilename );\n\t\t\t\ttexture = new Texture();\n\n\t\t\t} else {\n\n\t\t\t\tloader.setPath( this.textureLoader.path );\n\t\t\t\ttexture = loader.load( fileName );\n\n\t\t\t}\n\n\t\t} else if ( extension === 'psd' ) {\n\n\t\t\tconsole.warn( 'FBXLoader: PSD textures are not supported, creating placeholder texture for', textureNode.RelativeFilename );\n\t\t\ttexture = new Texture();\n\n\t\t} else {\n\n\t\t\ttexture = this.textureLoader.load( fileName );\n\n\t\t}\n\n\t\tthis.textureLoader.setPath( currentPath );\n\n\t\treturn texture;\n\n\t}\n\n\t// Parse nodes in FBXTree.Objects.Material\n\tparseMaterials( textureMap ) {\n\n\t\tconst materialMap = new Map();\n\n\t\tif ( 'Material' in fbxTree.Objects ) {\n\n\t\t\tconst materialNodes = fbxTree.Objects.Material;\n\n\t\t\tfor ( const nodeID in materialNodes ) {\n\n\t\t\t\tconst material = this.parseMaterial( materialNodes[ nodeID ], textureMap );\n\n\t\t\t\tif ( material !== null ) materialMap.set( parseInt( nodeID ), material );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn materialMap;\n\n\t}\n\n\t// Parse single node in FBXTree.Objects.Material\n\t// Materials are connected to texture maps in FBXTree.Objects.Textures\n\t// FBX format currently only supports Lambert and Phong shading models\n\tparseMaterial( materialNode, textureMap ) {\n\n\t\tconst ID = materialNode.id;\n\t\tconst name = materialNode.attrName;\n\t\tlet type = materialNode.ShadingModel;\n\n\t\t// Case where FBX wraps shading model in property object.\n\t\tif ( typeof type === 'object' ) {\n\n\t\t\ttype = type.value;\n\n\t\t}\n\n\t\t// Ignore unused materials which don't have any connections.\n\t\tif ( ! connections.has( ID ) ) return null;\n\n\t\tconst parameters = this.parseParameters( materialNode, textureMap, ID );\n\n\t\tlet material;\n\n\t\tswitch ( type.toLowerCase() ) {\n\n\t\t\tcase 'phong':\n\t\t\t\tmaterial = new MeshPhongMaterial();\n\t\t\t\tbreak;\n\t\t\tcase 'lambert':\n\t\t\t\tmaterial = new MeshLambertMaterial();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tconsole.warn( 'THREE.FBXLoader: unknown material type \"%s\". Defaulting to MeshPhongMaterial.', type );\n\t\t\t\tmaterial = new MeshPhongMaterial();\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tmaterial.setValues( parameters );\n\t\tmaterial.name = name;\n\n\t\treturn material;\n\n\t}\n\n\t// Parse FBX material and return parameters suitable for a three.js material\n\t// Also parse the texture map and return any textures associated with the material\n\tparseParameters( materialNode, textureMap, ID ) {\n\n\t\tconst parameters = {};\n\n\t\tif ( materialNode.BumpFactor ) {\n\n\t\t\tparameters.bumpScale = materialNode.BumpFactor.value;\n\n\t\t}\n\n\t\tif ( materialNode.Diffuse ) {\n\n\t\t\tparameters.color = new Color().fromArray( materialNode.Diffuse.value ).convertSRGBToLinear();\n\n\t\t} else if ( materialNode.DiffuseColor && ( materialNode.DiffuseColor.type === 'Color' || materialNode.DiffuseColor.type === 'ColorRGB' ) ) {\n\n\t\t\t// The blender exporter exports diffuse here instead of in materialNode.Diffuse\n\t\t\tparameters.color = new Color().fromArray( materialNode.DiffuseColor.value ).convertSRGBToLinear();\n\n\t\t}\n\n\t\tif ( materialNode.DisplacementFactor ) {\n\n\t\t\tparameters.displacementScale = materialNode.DisplacementFactor.value;\n\n\t\t}\n\n\t\tif ( materialNode.Emissive ) {\n\n\t\t\tparameters.emissive = new Color().fromArray( materialNode.Emissive.value ).convertSRGBToLinear();\n\n\t\t} else if ( materialNode.EmissiveColor && ( materialNode.EmissiveColor.type === 'Color' || materialNode.EmissiveColor.type === 'ColorRGB' ) ) {\n\n\t\t\t// The blender exporter exports emissive color here instead of in materialNode.Emissive\n\t\t\tparameters.emissive = new Color().fromArray( materialNode.EmissiveColor.value ).convertSRGBToLinear();\n\n\t\t}\n\n\t\tif ( materialNode.EmissiveFactor ) {\n\n\t\t\tparameters.emissiveIntensity = parseFloat( materialNode.EmissiveFactor.value );\n\n\t\t}\n\n\t\tif ( materialNode.Opacity ) {\n\n\t\t\tparameters.opacity = parseFloat( materialNode.Opacity.value );\n\n\t\t}\n\n\t\tif ( parameters.opacity < 1.0 ) {\n\n\t\t\tparameters.transparent = true;\n\n\t\t}\n\n\t\tif ( materialNode.ReflectionFactor ) {\n\n\t\t\tparameters.reflectivity = materialNode.ReflectionFactor.value;\n\n\t\t}\n\n\t\tif ( materialNode.Shininess ) {\n\n\t\t\tparameters.shininess = materialNode.Shininess.value;\n\n\t\t}\n\n\t\tif ( materialNode.Specular ) {\n\n\t\t\tparameters.specular = new Color().fromArray( materialNode.Specular.value ).convertSRGBToLinear();\n\n\t\t} else if ( materialNode.SpecularColor && materialNode.SpecularColor.type === 'Color' ) {\n\n\t\t\t// The blender exporter exports specular color here instead of in materialNode.Specular\n\t\t\tparameters.specular = new Color().fromArray( materialNode.SpecularColor.value ).convertSRGBToLinear();\n\n\t\t}\n\n\t\tconst scope = this;\n\t\tconnections.get( ID ).children.forEach( function ( child ) {\n\n\t\t\tconst type = child.relationship;\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 'Bump':\n\t\t\t\t\tparameters.bumpMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Maya|TEX_ao_map':\n\t\t\t\t\tparameters.aoMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'DiffuseColor':\n\t\t\t\tcase 'Maya|TEX_color_map':\n\t\t\t\t\tparameters.map = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tif ( parameters.map !== undefined ) {\n\n\t\t\t\t\t\tparameters.map.colorSpace = SRGBColorSpace;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'DisplacementColor':\n\t\t\t\t\tparameters.displacementMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'EmissiveColor':\n\t\t\t\t\tparameters.emissiveMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tif ( parameters.emissiveMap !== undefined ) {\n\n\t\t\t\t\t\tparameters.emissiveMap.colorSpace = SRGBColorSpace;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'NormalMap':\n\t\t\t\tcase 'Maya|TEX_normal_map':\n\t\t\t\t\tparameters.normalMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'ReflectionColor':\n\t\t\t\t\tparameters.envMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tif ( parameters.envMap !== undefined ) {\n\n\t\t\t\t\t\tparameters.envMap.mapping = EquirectangularReflectionMapping;\n\t\t\t\t\t\tparameters.envMap.colorSpace = SRGBColorSpace;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'SpecularColor':\n\t\t\t\t\tparameters.specularMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tif ( parameters.specularMap !== undefined ) {\n\n\t\t\t\t\t\tparameters.specularMap.colorSpace = SRGBColorSpace;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'TransparentColor':\n\t\t\t\tcase 'TransparencyFactor':\n\t\t\t\t\tparameters.alphaMap = scope.getTexture( textureMap, child.ID );\n\t\t\t\t\tparameters.transparent = true;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'AmbientColor':\n\t\t\t\tcase 'ShininessExponent': // AKA glossiness map\n\t\t\t\tcase 'SpecularFactor': // AKA specularLevel\n\t\t\t\tcase 'VectorDisplacementColor': // NOTE: Seems to be a copy of DisplacementColor\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.warn( 'THREE.FBXLoader: %s map is not supported in three.js, skipping texture.', type );\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t} );\n\n\t\treturn parameters;\n\n\t}\n\n\t// get a texture from the textureMap for use by a material.\n\tgetTexture( textureMap, id ) {\n\n\t\t// if the texture is a layered texture, just use the first layer and issue a warning\n\t\tif ( 'LayeredTexture' in fbxTree.Objects && id in fbxTree.Objects.LayeredTexture ) {\n\n\t\t\tconsole.warn( 'THREE.FBXLoader: layered textures are not supported in three.js. Discarding all but first layer.' );\n\t\t\tid = connections.get( id ).children[ 0 ].ID;\n\n\t\t}\n\n\t\treturn textureMap.get( id );\n\n\t}\n\n\t// Parse nodes in FBXTree.Objects.Deformer\n\t// Deformer node can contain skinning or Vertex Cache animation data, however only skinning is supported here\n\t// Generates map of Skeleton-like objects for use later when generating and binding skeletons.\n\tparseDeformers() {\n\n\t\tconst skeletons = {};\n\t\tconst morphTargets = {};\n\n\t\tif ( 'Deformer' in fbxTree.Objects ) {\n\n\t\t\tconst DeformerNodes = fbxTree.Objects.Deformer;\n\n\t\t\tfor ( const nodeID in DeformerNodes ) {\n\n\t\t\t\tconst deformerNode = DeformerNodes[ nodeID ];\n\n\t\t\t\tconst relationships = connections.get( parseInt( nodeID ) );\n\n\t\t\t\tif ( deformerNode.attrType === 'Skin' ) {\n\n\t\t\t\t\tconst skeleton = this.parseSkeleton( relationships, DeformerNodes );\n\t\t\t\t\tskeleton.ID = nodeID;\n\n\t\t\t\t\tif ( relationships.parents.length > 1 ) console.warn( 'THREE.FBXLoader: skeleton attached to more than one geometry is not supported.' );\n\t\t\t\t\tskeleton.geometryID = relationships.parents[ 0 ].ID;\n\n\t\t\t\t\tskeletons[ nodeID ] = skeleton;\n\n\t\t\t\t} else if ( deformerNode.attrType === 'BlendShape' ) {\n\n\t\t\t\t\tconst morphTarget = {\n\t\t\t\t\t\tid: nodeID,\n\t\t\t\t\t};\n\n\t\t\t\t\tmorphTarget.rawTargets = this.parseMorphTargets( relationships, DeformerNodes );\n\t\t\t\t\tmorphTarget.id = nodeID;\n\n\t\t\t\t\tif ( relationships.parents.length > 1 ) console.warn( 'THREE.FBXLoader: morph target attached to more than one geometry is not supported.' );\n\n\t\t\t\t\tmorphTargets[ nodeID ] = morphTarget;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn {\n\n\t\t\tskeletons: skeletons,\n\t\t\tmorphTargets: morphTargets,\n\n\t\t};\n\n\t}\n\n\t// Parse single nodes in FBXTree.Objects.Deformer\n\t// The top level skeleton node has type 'Skin' and sub nodes have type 'Cluster'\n\t// Each skin node represents a skeleton and each cluster node represents a bone\n\tparseSkeleton( relationships, deformerNodes ) {\n\n\t\tconst rawBones = [];\n\n\t\trelationships.children.forEach( function ( child ) {\n\n\t\t\tconst boneNode = deformerNodes[ child.ID ];\n\n\t\t\tif ( boneNode.attrType !== 'Cluster' ) return;\n\n\t\t\tconst rawBone = {\n\n\t\t\t\tID: child.ID,\n\t\t\t\tindices: [],\n\t\t\t\tweights: [],\n\t\t\t\ttransformLink: new Matrix4().fromArray( boneNode.TransformLink.a ),\n\t\t\t\t// transform: new Matrix4().fromArray( boneNode.Transform.a ),\n\t\t\t\t// linkMode: boneNode.Mode,\n\n\t\t\t};\n\n\t\t\tif ( 'Indexes' in boneNode ) {\n\n\t\t\t\trawBone.indices = boneNode.Indexes.a;\n\t\t\t\trawBone.weights = boneNode.Weights.a;\n\n\t\t\t}\n\n\t\t\trawBones.push( rawBone );\n\n\t\t} );\n\n\t\treturn {\n\n\t\t\trawBones: rawBones,\n\t\t\tbones: []\n\n\t\t};\n\n\t}\n\n\t// The top level morph deformer node has type \"BlendShape\" and sub nodes have type \"BlendShapeChannel\"\n\tparseMorphTargets( relationships, deformerNodes ) {\n\n\t\tconst rawMorphTargets = [];\n\n\t\tfor ( let i = 0; i < relationships.children.length; i ++ ) {\n\n\t\t\tconst child = relationships.children[ i ];\n\n\t\t\tconst morphTargetNode = deformerNodes[ child.ID ];\n\n\t\t\tconst rawMorphTarget = {\n\n\t\t\t\tname: morphTargetNode.attrName,\n\t\t\t\tinitialWeight: morphTargetNode.DeformPercent,\n\t\t\t\tid: morphTargetNode.id,\n\t\t\t\tfullWeights: morphTargetNode.FullWeights.a\n\n\t\t\t};\n\n\t\t\tif ( morphTargetNode.attrType !== 'BlendShapeChannel' ) return;\n\n\t\t\trawMorphTarget.geoID = connections.get( parseInt( child.ID ) ).children.filter( function ( child ) {\n\n\t\t\t\treturn child.relationship === undefined;\n\n\t\t\t} )[ 0 ].ID;\n\n\t\t\trawMorphTargets.push( rawMorphTarget );\n\n\t\t}\n\n\t\treturn rawMorphTargets;\n\n\t}\n\n\t// create the main Group() to be returned by the loader\n\tparseScene( deformers, geometryMap, materialMap ) {\n\n\t\tsceneGraph = new Group();\n\n\t\tconst modelMap = this.parseModels( deformers.skeletons, geometryMap, materialMap );\n\n\t\tconst modelNodes = fbxTree.Objects.Model;\n\n\t\tconst scope = this;\n\t\tmodelMap.forEach( function ( model ) {\n\n\t\t\tconst modelNode = modelNodes[ model.ID ];\n\t\t\tscope.setLookAtProperties( model, modelNode );\n\n\t\t\tconst parentConnections = connections.get( model.ID ).parents;\n\n\t\t\tparentConnections.forEach( function ( connection ) {\n\n\t\t\t\tconst parent = modelMap.get( connection.ID );\n\t\t\t\tif ( parent !== undefined ) parent.add( model );\n\n\t\t\t} );\n\n\t\t\tif ( model.parent === null ) {\n\n\t\t\t\tsceneGraph.add( model );\n\n\t\t\t}\n\n\n\t\t} );\n\n\t\tthis.bindSkeleton( deformers.skeletons, geometryMap, modelMap );\n\n\t\tthis.addGlobalSceneSettings();\n\n\t\tsceneGraph.traverse( function ( node ) {\n\n\t\t\tif ( node.userData.transformData ) {\n\n\t\t\t\tif ( node.parent ) {\n\n\t\t\t\t\tnode.userData.transformData.parentMatrix = node.parent.matrix;\n\t\t\t\t\tnode.userData.transformData.parentMatrixWorld = node.parent.matrixWorld;\n\n\t\t\t\t}\n\n\t\t\t\tconst transform = generateTransform( node.userData.transformData );\n\n\t\t\t\tnode.applyMatrix4( transform );\n\t\t\t\tnode.updateWorldMatrix();\n\n\t\t\t}\n\n\t\t} );\n\n\t\tconst animations = new AnimationParser().parse();\n\n\t\t// if all the models where already combined in a single group, just return that\n\t\tif ( sceneGraph.children.length === 1 && sceneGraph.children[ 0 ].isGroup ) {\n\n\t\t\tsceneGraph.children[ 0 ].animations = animations;\n\t\t\tsceneGraph = sceneGraph.children[ 0 ];\n\n\t\t}\n\n\t\tsceneGraph.animations = animations;\n\n\t}\n\n\t// parse nodes in FBXTree.Objects.Model\n\tparseModels( skeletons, geometryMap, materialMap ) {\n\n\t\tconst modelMap = new Map();\n\t\tconst modelNodes = fbxTree.Objects.Model;\n\n\t\tfor ( const nodeID in modelNodes ) {\n\n\t\t\tconst id = parseInt( nodeID );\n\t\t\tconst node = modelNodes[ nodeID ];\n\t\t\tconst relationships = connections.get( id );\n\n\t\t\tlet model = this.buildSkeleton( relationships, skeletons, id, node.attrName );\n\n\t\t\tif ( ! model ) {\n\n\t\t\t\tswitch ( node.attrType ) {\n\n\t\t\t\t\tcase 'Camera':\n\t\t\t\t\t\tmodel = this.createCamera( relationships );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'Light':\n\t\t\t\t\t\tmodel = this.createLight( relationships );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'Mesh':\n\t\t\t\t\t\tmodel = this.createMesh( relationships, geometryMap, materialMap );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'NurbsCurve':\n\t\t\t\t\t\tmodel = this.createCurve( relationships, geometryMap );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'LimbNode':\n\t\t\t\t\tcase 'Root':\n\t\t\t\t\t\tmodel = new Bone();\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'Null':\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tmodel = new Group();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tmodel.name = node.attrName ? PropertyBinding.sanitizeNodeName( node.attrName ) : '';\n\t\t\t\tmodel.userData.originalName = node.attrName;\n\n\t\t\t\tmodel.ID = id;\n\n\t\t\t}\n\n\t\t\tthis.getTransformData( model, node );\n\t\t\tmodelMap.set( id, model );\n\n\t\t}\n\n\t\treturn modelMap;\n\n\t}\n\n\tbuildSkeleton( relationships, skeletons, id, name ) {\n\n\t\tlet bone = null;\n\n\t\trelationships.parents.forEach( function ( parent ) {\n\n\t\t\tfor ( const ID in skeletons ) {\n\n\t\t\t\tconst skeleton = skeletons[ ID ];\n\n\t\t\t\tskeleton.rawBones.forEach( function ( rawBone, i ) {\n\n\t\t\t\t\tif ( rawBone.ID === parent.ID ) {\n\n\t\t\t\t\t\tconst subBone = bone;\n\t\t\t\t\t\tbone = new Bone();\n\n\t\t\t\t\t\tbone.matrixWorld.copy( rawBone.transformLink );\n\n\t\t\t\t\t\t// set name and id here - otherwise in cases where \"subBone\" is created it will not have a name / id\n\n\t\t\t\t\t\tbone.name = name ? PropertyBinding.sanitizeNodeName( name ) : '';\n\t\t\t\t\t\tbone.userData.originalName = name;\n\t\t\t\t\t\tbone.ID = id;\n\n\t\t\t\t\t\tskeleton.bones[ i ] = bone;\n\n\t\t\t\t\t\t// In cases where a bone is shared between multiple meshes\n\t\t\t\t\t\t// duplicate the bone here and and it as a child of the first bone\n\t\t\t\t\t\tif ( subBone !== null ) {\n\n\t\t\t\t\t\t\tbone.add( subBone );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t} );\n\n\t\treturn bone;\n\n\t}\n\n\t// create a PerspectiveCamera or OrthographicCamera\n\tcreateCamera( relationships ) {\n\n\t\tlet model;\n\t\tlet cameraAttribute;\n\n\t\trelationships.children.forEach( function ( child ) {\n\n\t\t\tconst attr = fbxTree.Objects.NodeAttribute[ child.ID ];\n\n\t\t\tif ( attr !== undefined ) {\n\n\t\t\t\tcameraAttribute = attr;\n\n\t\t\t}\n\n\t\t} );\n\n\t\tif ( cameraAttribute === undefined ) {\n\n\t\t\tmodel = new Object3D();\n\n\t\t} else {\n\n\t\t\tlet type = 0;\n\t\t\tif ( cameraAttribute.CameraProjectionType !== undefined && cameraAttribute.CameraProjectionType.value === 1 ) {\n\n\t\t\t\ttype = 1;\n\n\t\t\t}\n\n\t\t\tlet nearClippingPlane = 1;\n\t\t\tif ( cameraAttribute.NearPlane !== undefined ) {\n\n\t\t\t\tnearClippingPlane = cameraAttribute.NearPlane.value / 1000;\n\n\t\t\t}\n\n\t\t\tlet farClippingPlane = 1000;\n\t\t\tif ( cameraAttribute.FarPlane !== undefined ) {\n\n\t\t\t\tfarClippingPlane = cameraAttribute.FarPlane.value / 1000;\n\n\t\t\t}\n\n\n\t\t\tlet width = window.innerWidth;\n\t\t\tlet height = window.innerHeight;\n\n\t\t\tif ( cameraAttribute.AspectWidth !== undefined && cameraAttribute.AspectHeight !== undefined ) {\n\n\t\t\t\twidth = cameraAttribute.AspectWidth.value;\n\t\t\t\theight = cameraAttribute.AspectHeight.value;\n\n\t\t\t}\n\n\t\t\tconst aspect = width / height;\n\n\t\t\tlet fov = 45;\n\t\t\tif ( cameraAttribute.FieldOfView !== undefined ) {\n\n\t\t\t\tfov = cameraAttribute.FieldOfView.value;\n\n\t\t\t}\n\n\t\t\tconst focalLength = cameraAttribute.FocalLength ? cameraAttribute.FocalLength.value : null;\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 0: // Perspective\n\t\t\t\t\tmodel = new PerspectiveCamera( fov, aspect, nearClippingPlane, farClippingPlane );\n\t\t\t\t\tif ( focalLength !== null ) model.setFocalLength( focalLength );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 1: // Orthographic\n\t\t\t\t\tmodel = new OrthographicCamera( - width / 2, width / 2, height / 2, - height / 2, nearClippingPlane, farClippingPlane );\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.warn( 'THREE.FBXLoader: Unknown camera type ' + type + '.' );\n\t\t\t\t\tmodel = new Object3D();\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn model;\n\n\t}\n\n\t// Create a DirectionalLight, PointLight or SpotLight\n\tcreateLight( relationships ) {\n\n\t\tlet model;\n\t\tlet lightAttribute;\n\n\t\trelationships.children.forEach( function ( child ) {\n\n\t\t\tconst attr = fbxTree.Objects.NodeAttribute[ child.ID ];\n\n\t\t\tif ( attr !== undefined ) {\n\n\t\t\t\tlightAttribute = attr;\n\n\t\t\t}\n\n\t\t} );\n\n\t\tif ( lightAttribute === undefined ) {\n\n\t\t\tmodel = new Object3D();\n\n\t\t} else {\n\n\t\t\tlet type;\n\n\t\t\t// LightType can be undefined for Point lights\n\t\t\tif ( lightAttribute.LightType === undefined ) {\n\n\t\t\t\ttype = 0;\n\n\t\t\t} else {\n\n\t\t\t\ttype = lightAttribute.LightType.value;\n\n\t\t\t}\n\n\t\t\tlet color = 0xffffff;\n\n\t\t\tif ( lightAttribute.Color !== undefined ) {\n\n\t\t\t\tcolor = new Color().fromArray( lightAttribute.Color.value ).convertSRGBToLinear();\n\n\t\t\t}\n\n\t\t\tlet intensity = ( lightAttribute.Intensity === undefined ) ? 1 : lightAttribute.Intensity.value / 100;\n\n\t\t\t// light disabled\n\t\t\tif ( lightAttribute.CastLightOnObject !== undefined && lightAttribute.CastLightOnObject.value === 0 ) {\n\n\t\t\t\tintensity = 0;\n\n\t\t\t}\n\n\t\t\tlet distance = 0;\n\t\t\tif ( lightAttribute.FarAttenuationEnd !== undefined ) {\n\n\t\t\t\tif ( lightAttribute.EnableFarAttenuation !== undefined && lightAttribute.EnableFarAttenuation.value === 0 ) {\n\n\t\t\t\t\tdistance = 0;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tdistance = lightAttribute.FarAttenuationEnd.value;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// TODO: could this be calculated linearly from FarAttenuationStart to FarAttenuationEnd?\n\t\t\tconst decay = 1;\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 0: // Point\n\t\t\t\t\tmodel = new PointLight( color, intensity, distance, decay );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 1: // Directional\n\t\t\t\t\tmodel = new DirectionalLight( color, intensity );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 2: // Spot\n\t\t\t\t\tlet angle = Math.PI / 3;\n\n\t\t\t\t\tif ( lightAttribute.InnerAngle !== undefined ) {\n\n\t\t\t\t\t\tangle = MathUtils.degToRad( lightAttribute.InnerAngle.value );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet penumbra = 0;\n\t\t\t\t\tif ( lightAttribute.OuterAngle !== undefined ) {\n\n\t\t\t\t\t\t// TODO: this is not correct - FBX calculates outer and inner angle in degrees\n\t\t\t\t\t\t// with OuterAngle > InnerAngle && OuterAngle <= Math.PI\n\t\t\t\t\t\t// while three.js uses a penumbra between (0, 1) to attenuate the inner angle\n\t\t\t\t\t\tpenumbra = MathUtils.degToRad( lightAttribute.OuterAngle.value );\n\t\t\t\t\t\tpenumbra = Math.max( penumbra, 1 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tmodel = new SpotLight( color, intensity, distance, angle, penumbra, decay );\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.warn( 'THREE.FBXLoader: Unknown light type ' + lightAttribute.LightType.value + ', defaulting to a PointLight.' );\n\t\t\t\t\tmodel = new PointLight( color, intensity );\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( lightAttribute.CastShadows !== undefined && lightAttribute.CastShadows.value === 1 ) {\n\n\t\t\t\tmodel.castShadow = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn model;\n\n\t}\n\n\tcreateMesh( relationships, geometryMap, materialMap ) {\n\n\t\tlet model;\n\t\tlet geometry = null;\n\t\tlet material = null;\n\t\tconst materials = [];\n\n\t\t// get geometry and materials(s) from connections\n\t\trelationships.children.forEach( function ( child ) {\n\n\t\t\tif ( geometryMap.has( child.ID ) ) {\n\n\t\t\t\tgeometry = geometryMap.get( child.ID );\n\n\t\t\t}\n\n\t\t\tif ( materialMap.has( child.ID ) ) {\n\n\t\t\t\tmaterials.push( materialMap.get( child.ID ) );\n\n\t\t\t}\n\n\t\t} );\n\n\t\tif ( materials.length > 1 ) {\n\n\t\t\tmaterial = materials;\n\n\t\t} else if ( materials.length > 0 ) {\n\n\t\t\tmaterial = materials[ 0 ];\n\n\t\t} else {\n\n\t\t\tmaterial = new MeshPhongMaterial( {\n\t\t\t\tname: Loader.DEFAULT_MATERIAL_NAME,\n\t\t\t\tcolor: 0xcccccc\n\t\t\t} );\n\t\t\tmaterials.push( material );\n\n\t\t}\n\n\t\tif ( 'color' in geometry.attributes ) {\n\n\t\t\tmaterials.forEach( function ( material ) {\n\n\t\t\t\tmaterial.vertexColors = true;\n\n\t\t\t} );\n\n\t\t}\n\n\t\tif ( geometry.FBX_Deformer ) {\n\n\t\t\tmodel = new SkinnedMesh( geometry, material );\n\t\t\tmodel.normalizeSkinWeights();\n\n\t\t} else {\n\n\t\t\tmodel = new Mesh( geometry, material );\n\n\t\t}\n\n\t\treturn model;\n\n\t}\n\n\tcreateCurve( relationships, geometryMap ) {\n\n\t\tconst geometry = relationships.children.reduce( function ( geo, child ) {\n\n\t\t\tif ( geometryMap.has( child.ID ) ) geo = geometryMap.get( child.ID );\n\n\t\t\treturn geo;\n\n\t\t}, null );\n\n\t\t// FBX does not list materials for Nurbs lines, so we'll just put our own in here.\n\t\tconst material = new LineBasicMaterial( {\n\t\t\tname: Loader.DEFAULT_MATERIAL_NAME,\n\t\t\tcolor: 0x3300ff,\n\t\t\tlinewidth: 1\n\t\t} );\n\t\treturn new Line( geometry, material );\n\n\t}\n\n\t// parse the model node for transform data\n\tgetTransformData( model, modelNode ) {\n\n\t\tconst transformData = {};\n\n\t\tif ( 'InheritType' in modelNode ) transformData.inheritType = parseInt( modelNode.InheritType.value );\n\n\t\tif ( 'RotationOrder' in modelNode ) transformData.eulerOrder = getEulerOrder( modelNode.RotationOrder.value );\n\t\telse transformData.eulerOrder = 'ZYX';\n\n\t\tif ( 'Lcl_Translation' in modelNode ) transformData.translation = modelNode.Lcl_Translation.value;\n\n\t\tif ( 'PreRotation' in modelNode ) transformData.preRotation = modelNode.PreRotation.value;\n\t\tif ( 'Lcl_Rotation' in modelNode ) transformData.rotation = modelNode.Lcl_Rotation.value;\n\t\tif ( 'PostRotation' in modelNode ) transformData.postRotation = modelNode.PostRotation.value;\n\n\t\tif ( 'Lcl_Scaling' in modelNode ) transformData.scale = modelNode.Lcl_Scaling.value;\n\n\t\tif ( 'ScalingOffset' in modelNode ) transformData.scalingOffset = modelNode.ScalingOffset.value;\n\t\tif ( 'ScalingPivot' in modelNode ) transformData.scalingPivot = modelNode.ScalingPivot.value;\n\n\t\tif ( 'RotationOffset' in modelNode ) transformData.rotationOffset = modelNode.RotationOffset.value;\n\t\tif ( 'RotationPivot' in modelNode ) transformData.rotationPivot = modelNode.RotationPivot.value;\n\n\t\tmodel.userData.transformData = transformData;\n\n\t}\n\n\tsetLookAtProperties( model, modelNode ) {\n\n\t\tif ( 'LookAtProperty' in modelNode ) {\n\n\t\t\tconst children = connections.get( model.ID ).children;\n\n\t\t\tchildren.forEach( function ( child ) {\n\n\t\t\t\tif ( child.relationship === 'LookAtProperty' ) {\n\n\t\t\t\t\tconst lookAtTarget = fbxTree.Objects.Model[ child.ID ];\n\n\t\t\t\t\tif ( 'Lcl_Translation' in lookAtTarget ) {\n\n\t\t\t\t\t\tconst pos = lookAtTarget.Lcl_Translation.value;\n\n\t\t\t\t\t\t// DirectionalLight, SpotLight\n\t\t\t\t\t\tif ( model.target !== undefined ) {\n\n\t\t\t\t\t\t\tmodel.target.position.fromArray( pos );\n\t\t\t\t\t\t\tsceneGraph.add( model.target );\n\n\t\t\t\t\t\t} else { // Cameras and other Object3Ds\n\n\t\t\t\t\t\t\tmodel.lookAt( new Vector3().fromArray( pos ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t}\n\n\t}\n\n\tbindSkeleton( skeletons, geometryMap, modelMap ) {\n\n\t\tconst bindMatrices = this.parsePoseNodes();\n\n\t\tfor ( const ID in skeletons ) {\n\n\t\t\tconst skeleton = skeletons[ ID ];\n\n\t\t\tconst parents = connections.get( parseInt( skeleton.ID ) ).parents;\n\n\t\t\tparents.forEach( function ( parent ) {\n\n\t\t\t\tif ( geometryMap.has( parent.ID ) ) {\n\n\t\t\t\t\tconst geoID = parent.ID;\n\t\t\t\t\tconst geoRelationships = connections.get( geoID );\n\n\t\t\t\t\tgeoRelationships.parents.forEach( function ( geoConnParent ) {\n\n\t\t\t\t\t\tif ( modelMap.has( geoConnParent.ID ) ) {\n\n\t\t\t\t\t\t\tconst model = modelMap.get( geoConnParent.ID );\n\n\t\t\t\t\t\t\tmodel.bind( new Skeleton( skeleton.bones ), bindMatrices[ geoConnParent.ID ] );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t}\n\n\t}\n\n\tparsePoseNodes() {\n\n\t\tconst bindMatrices = {};\n\n\t\tif ( 'Pose' in fbxTree.Objects ) {\n\n\t\t\tconst BindPoseNode = fbxTree.Objects.Pose;\n\n\t\t\tfor ( const nodeID in BindPoseNode ) {\n\n\t\t\t\tif ( BindPoseNode[ nodeID ].attrType === 'BindPose' && BindPoseNode[ nodeID ].NbPoseNodes > 0 ) {\n\n\t\t\t\t\tconst poseNodes = BindPoseNode[ nodeID ].PoseNode;\n\n\t\t\t\t\tif ( Array.isArray( poseNodes ) ) {\n\n\t\t\t\t\t\tposeNodes.forEach( function ( poseNode ) {\n\n\t\t\t\t\t\t\tbindMatrices[ poseNode.Node ] = new Matrix4().fromArray( poseNode.Matrix.a );\n\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tbindMatrices[ poseNodes.Node ] = new Matrix4().fromArray( poseNodes.Matrix.a );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bindMatrices;\n\n\t}\n\n\taddGlobalSceneSettings() {\n\n\t\tif ( 'GlobalSettings' in fbxTree ) {\n\n\t\t\tif ( 'AmbientColor' in fbxTree.GlobalSettings ) {\n\n\t\t\t\t// Parse ambient color - if it's not set to black (default), create an ambient light\n\n\t\t\t\tconst ambientColor = fbxTree.GlobalSettings.AmbientColor.value;\n\t\t\t\tconst r = ambientColor[ 0 ];\n\t\t\t\tconst g = ambientColor[ 1 ];\n\t\t\t\tconst b = ambientColor[ 2 ];\n\n\t\t\t\tif ( r !== 0 || g !== 0 || b !== 0 ) {\n\n\t\t\t\t\tconst color = new Color( r, g, b ).convertSRGBToLinear();\n\t\t\t\t\tsceneGraph.add( new AmbientLight( color, 1 ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( 'UnitScaleFactor' in fbxTree.GlobalSettings ) {\n\n\t\t\t\tsceneGraph.userData.unitScaleFactor = fbxTree.GlobalSettings.UnitScaleFactor.value;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n// parse Geometry data from FBXTree and return map of BufferGeometries\nclass GeometryParser {\n\n\tconstructor() {\n\n\t\tthis.negativeMaterialIndices = false;\n\n\t}\n\n\t// Parse nodes in FBXTree.Objects.Geometry\n\tparse( deformers ) {\n\n\t\tconst geometryMap = new Map();\n\n\t\tif ( 'Geometry' in fbxTree.Objects ) {\n\n\t\t\tconst geoNodes = fbxTree.Objects.Geometry;\n\n\t\t\tfor ( const nodeID in geoNodes ) {\n\n\t\t\t\tconst relationships = connections.get( parseInt( nodeID ) );\n\t\t\t\tconst geo = this.parseGeometry( relationships, geoNodes[ nodeID ], deformers );\n\n\t\t\t\tgeometryMap.set( parseInt( nodeID ), geo );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// report warnings\n\n\t\tif ( this.negativeMaterialIndices === true ) {\n\n\t\t\tconsole.warn( 'THREE.FBXLoader: The FBX file contains invalid (negative) material indices. The asset might not render as expected.' );\n\n\t\t}\n\n\t\treturn geometryMap;\n\n\t}\n\n\t// Parse single node in FBXTree.Objects.Geometry\n\tparseGeometry( relationships, geoNode, deformers ) {\n\n\t\tswitch ( geoNode.attrType ) {\n\n\t\t\tcase 'Mesh':\n\t\t\t\treturn this.parseMeshGeometry( relationships, geoNode, deformers );\n\t\t\t\tbreak;\n\n\t\t\tcase 'NurbsCurve':\n\t\t\t\treturn this.parseNurbsGeometry( geoNode );\n\t\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// Parse single node mesh geometry in FBXTree.Objects.Geometry\n\tparseMeshGeometry( relationships, geoNode, deformers ) {\n\n\t\tconst skeletons = deformers.skeletons;\n\t\tconst morphTargets = [];\n\n\t\tconst modelNodes = relationships.parents.map( function ( parent ) {\n\n\t\t\treturn fbxTree.Objects.Model[ parent.ID ];\n\n\t\t} );\n\n\t\t// don't create geometry if it is not associated with any models\n\t\tif ( modelNodes.length === 0 ) return;\n\n\t\tconst skeleton = relationships.children.reduce( function ( skeleton, child ) {\n\n\t\t\tif ( skeletons[ child.ID ] !== undefined ) skeleton = skeletons[ child.ID ];\n\n\t\t\treturn skeleton;\n\n\t\t}, null );\n\n\t\trelationships.children.forEach( function ( child ) {\n\n\t\t\tif ( deformers.morphTargets[ child.ID ] !== undefined ) {\n\n\t\t\t\tmorphTargets.push( deformers.morphTargets[ child.ID ] );\n\n\t\t\t}\n\n\t\t} );\n\n\t\t// Assume one model and get the preRotation from that\n\t\t// if there is more than one model associated with the geometry this may cause problems\n\t\tconst modelNode = modelNodes[ 0 ];\n\n\t\tconst transformData = {};\n\n\t\tif ( 'RotationOrder' in modelNode ) transformData.eulerOrder = getEulerOrder( modelNode.RotationOrder.value );\n\t\tif ( 'InheritType' in modelNode ) transformData.inheritType = parseInt( modelNode.InheritType.value );\n\n\t\tif ( 'GeometricTranslation' in modelNode ) transformData.translation = modelNode.GeometricTranslation.value;\n\t\tif ( 'GeometricRotation' in modelNode ) transformData.rotation = modelNode.GeometricRotation.value;\n\t\tif ( 'GeometricScaling' in modelNode ) transformData.scale = modelNode.GeometricScaling.value;\n\n\t\tconst transform = generateTransform( transformData );\n\n\t\treturn this.genGeometry( geoNode, skeleton, morphTargets, transform );\n\n\t}\n\n\t// Generate a BufferGeometry from a node in FBXTree.Objects.Geometry\n\tgenGeometry( geoNode, skeleton, morphTargets, preTransform ) {\n\n\t\tconst geo = new BufferGeometry();\n\t\tif ( geoNode.attrName ) geo.name = geoNode.attrName;\n\n\t\tconst geoInfo = this.parseGeoNode( geoNode, skeleton );\n\t\tconst buffers = this.genBuffers( geoInfo );\n\n\t\tconst positionAttribute = new Float32BufferAttribute( buffers.vertex, 3 );\n\n\t\tpositionAttribute.applyMatrix4( preTransform );\n\n\t\tgeo.setAttribute( 'position', positionAttribute );\n\n\t\tif ( buffers.colors.length > 0 ) {\n\n\t\t\tgeo.setAttribute( 'color', new Float32BufferAttribute( buffers.colors, 3 ) );\n\n\t\t}\n\n\t\tif ( skeleton ) {\n\n\t\t\tgeo.setAttribute( 'skinIndex', new Uint16BufferAttribute( buffers.weightsIndices, 4 ) );\n\n\t\t\tgeo.setAttribute( 'skinWeight', new Float32BufferAttribute( buffers.vertexWeights, 4 ) );\n\n\t\t\t// used later to bind the skeleton to the model\n\t\t\tgeo.FBX_Deformer = skeleton;\n\n\t\t}\n\n\t\tif ( buffers.normal.length > 0 ) {\n\n\t\t\tconst normalMatrix = new Matrix3().getNormalMatrix( preTransform );\n\n\t\t\tconst normalAttribute = new Float32BufferAttribute( buffers.normal, 3 );\n\t\t\tnormalAttribute.applyNormalMatrix( normalMatrix );\n\n\t\t\tgeo.setAttribute( 'normal', normalAttribute );\n\n\t\t}\n\n\t\tbuffers.uvs.forEach( function ( uvBuffer, i ) {\n\n\t\t\tconst name = i === 0 ? 'uv' : `uv${ i }`;\n\n\t\t\tgeo.setAttribute( name, new Float32BufferAttribute( buffers.uvs[ i ], 2 ) );\n\n\t\t} );\n\n\t\tif ( geoInfo.material && geoInfo.material.mappingType !== 'AllSame' ) {\n\n\t\t\t// Convert the material indices of each vertex into rendering groups on the geometry.\n\t\t\tlet prevMaterialIndex = buffers.materialIndex[ 0 ];\n\t\t\tlet startIndex = 0;\n\n\t\t\tbuffers.materialIndex.forEach( function ( currentIndex, i ) {\n\n\t\t\t\tif ( currentIndex !== prevMaterialIndex ) {\n\n\t\t\t\t\tgeo.addGroup( startIndex, i - startIndex, prevMaterialIndex );\n\n\t\t\t\t\tprevMaterialIndex = currentIndex;\n\t\t\t\t\tstartIndex = i;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\t// the loop above doesn't add the last group, do that here.\n\t\t\tif ( geo.groups.length > 0 ) {\n\n\t\t\t\tconst lastGroup = geo.groups[ geo.groups.length - 1 ];\n\t\t\t\tconst lastIndex = lastGroup.start + lastGroup.count;\n\n\t\t\t\tif ( lastIndex !== buffers.materialIndex.length ) {\n\n\t\t\t\t\tgeo.addGroup( lastIndex, buffers.materialIndex.length - lastIndex, prevMaterialIndex );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// case where there are multiple materials but the whole geometry is only\n\t\t\t// using one of them\n\t\t\tif ( geo.groups.length === 0 ) {\n\n\t\t\t\tgeo.addGroup( 0, buffers.materialIndex.length, buffers.materialIndex[ 0 ] );\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.addMorphTargets( geo, geoNode, morphTargets, preTransform );\n\n\t\treturn geo;\n\n\t}\n\n\tparseGeoNode( geoNode, skeleton ) {\n\n\t\tconst geoInfo = {};\n\n\t\tgeoInfo.vertexPositions = ( geoNode.Vertices !== undefined ) ? geoNode.Vertices.a : [];\n\t\tgeoInfo.vertexIndices = ( geoNode.PolygonVertexIndex !== undefined ) ? geoNode.PolygonVertexIndex.a : [];\n\n\t\tif ( geoNode.LayerElementColor ) {\n\n\t\t\tgeoInfo.color = this.parseVertexColors( geoNode.LayerElementColor[ 0 ] );\n\n\t\t}\n\n\t\tif ( geoNode.LayerElementMaterial ) {\n\n\t\t\tgeoInfo.material = this.parseMaterialIndices( geoNode.LayerElementMaterial[ 0 ] );\n\n\t\t}\n\n\t\tif ( geoNode.LayerElementNormal ) {\n\n\t\t\tgeoInfo.normal = this.parseNormals( geoNode.LayerElementNormal[ 0 ] );\n\n\t\t}\n\n\t\tif ( geoNode.LayerElementUV ) {\n\n\t\t\tgeoInfo.uv = [];\n\n\t\t\tlet i = 0;\n\t\t\twhile ( geoNode.LayerElementUV[ i ] ) {\n\n\t\t\t\tif ( geoNode.LayerElementUV[ i ].UV ) {\n\n\t\t\t\t\tgeoInfo.uv.push( this.parseUVs( geoNode.LayerElementUV[ i ] ) );\n\n\t\t\t\t}\n\n\t\t\t\ti ++;\n\n\t\t\t}\n\n\t\t}\n\n\t\tgeoInfo.weightTable = {};\n\n\t\tif ( skeleton !== null ) {\n\n\t\t\tgeoInfo.skeleton = skeleton;\n\n\t\t\tskeleton.rawBones.forEach( function ( rawBone, i ) {\n\n\t\t\t\t// loop over the bone's vertex indices and weights\n\t\t\t\trawBone.indices.forEach( function ( index, j ) {\n\n\t\t\t\t\tif ( geoInfo.weightTable[ index ] === undefined ) geoInfo.weightTable[ index ] = [];\n\n\t\t\t\t\tgeoInfo.weightTable[ index ].push( {\n\n\t\t\t\t\t\tid: i,\n\t\t\t\t\t\tweight: rawBone.weights[ j ],\n\n\t\t\t\t\t} );\n\n\t\t\t\t} );\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn geoInfo;\n\n\t}\n\n\tgenBuffers( geoInfo ) {\n\n\t\tconst buffers = {\n\t\t\tvertex: [],\n\t\t\tnormal: [],\n\t\t\tcolors: [],\n\t\t\tuvs: [],\n\t\t\tmaterialIndex: [],\n\t\t\tvertexWeights: [],\n\t\t\tweightsIndices: [],\n\t\t};\n\n\t\tlet polygonIndex = 0;\n\t\tlet faceLength = 0;\n\t\tlet displayedWeightsWarning = false;\n\n\t\t// these will hold data for a single face\n\t\tlet facePositionIndexes = [];\n\t\tlet faceNormals = [];\n\t\tlet faceColors = [];\n\t\tlet faceUVs = [];\n\t\tlet faceWeights = [];\n\t\tlet faceWeightIndices = [];\n\n\t\tconst scope = this;\n\t\tgeoInfo.vertexIndices.forEach( function ( vertexIndex, polygonVertexIndex ) {\n\n\t\t\tlet materialIndex;\n\t\t\tlet endOfFace = false;\n\n\t\t\t// Face index and vertex index arrays are combined in a single array\n\t\t\t// A cube with quad faces looks like this:\n\t\t\t// PolygonVertexIndex: *24 {\n\t\t\t//  a: 0, 1, 3, -3, 2, 3, 5, -5, 4, 5, 7, -7, 6, 7, 1, -1, 1, 7, 5, -4, 6, 0, 2, -5\n\t\t\t//  }\n\t\t\t// Negative numbers mark the end of a face - first face here is 0, 1, 3, -3\n\t\t\t// to find index of last vertex bit shift the index: ^ - 1\n\t\t\tif ( vertexIndex < 0 ) {\n\n\t\t\t\tvertexIndex = vertexIndex ^ - 1; // equivalent to ( x * -1 ) - 1\n\t\t\t\tendOfFace = true;\n\n\t\t\t}\n\n\t\t\tlet weightIndices = [];\n\t\t\tlet weights = [];\n\n\t\t\tfacePositionIndexes.push( vertexIndex * 3, vertexIndex * 3 + 1, vertexIndex * 3 + 2 );\n\n\t\t\tif ( geoInfo.color ) {\n\n\t\t\t\tconst data = getData( polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.color );\n\n\t\t\t\tfaceColors.push( data[ 0 ], data[ 1 ], data[ 2 ] );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.skeleton ) {\n\n\t\t\t\tif ( geoInfo.weightTable[ vertexIndex ] !== undefined ) {\n\n\t\t\t\t\tgeoInfo.weightTable[ vertexIndex ].forEach( function ( wt ) {\n\n\t\t\t\t\t\tweights.push( wt.weight );\n\t\t\t\t\t\tweightIndices.push( wt.id );\n\n\t\t\t\t\t} );\n\n\n\t\t\t\t}\n\n\t\t\t\tif ( weights.length > 4 ) {\n\n\t\t\t\t\tif ( ! displayedWeightsWarning ) {\n\n\t\t\t\t\t\tconsole.warn( 'THREE.FBXLoader: Vertex has more than 4 skinning weights assigned to vertex. Deleting additional weights.' );\n\t\t\t\t\t\tdisplayedWeightsWarning = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst wIndex = [ 0, 0, 0, 0 ];\n\t\t\t\t\tconst Weight = [ 0, 0, 0, 0 ];\n\n\t\t\t\t\tweights.forEach( function ( weight, weightIndex ) {\n\n\t\t\t\t\t\tlet currentWeight = weight;\n\t\t\t\t\t\tlet currentIndex = weightIndices[ weightIndex ];\n\n\t\t\t\t\t\tWeight.forEach( function ( comparedWeight, comparedWeightIndex, comparedWeightArray ) {\n\n\t\t\t\t\t\t\tif ( currentWeight > comparedWeight ) {\n\n\t\t\t\t\t\t\t\tcomparedWeightArray[ comparedWeightIndex ] = currentWeight;\n\t\t\t\t\t\t\t\tcurrentWeight = comparedWeight;\n\n\t\t\t\t\t\t\t\tconst tmp = wIndex[ comparedWeightIndex ];\n\t\t\t\t\t\t\t\twIndex[ comparedWeightIndex ] = currentIndex;\n\t\t\t\t\t\t\t\tcurrentIndex = tmp;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t} );\n\n\t\t\t\t\tweightIndices = wIndex;\n\t\t\t\t\tweights = Weight;\n\n\t\t\t\t}\n\n\t\t\t\t// if the weight array is shorter than 4 pad with 0s\n\t\t\t\twhile ( weights.length < 4 ) {\n\n\t\t\t\t\tweights.push( 0 );\n\t\t\t\t\tweightIndices.push( 0 );\n\n\t\t\t\t}\n\n\t\t\t\tfor ( let i = 0; i < 4; ++ i ) {\n\n\t\t\t\t\tfaceWeights.push( weights[ i ] );\n\t\t\t\t\tfaceWeightIndices.push( weightIndices[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.normal ) {\n\n\t\t\t\tconst data = getData( polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.normal );\n\n\t\t\t\tfaceNormals.push( data[ 0 ], data[ 1 ], data[ 2 ] );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.material && geoInfo.material.mappingType !== 'AllSame' ) {\n\n\t\t\t\tmaterialIndex = getData( polygonVertexIndex, polygonIndex, vertexIndex, geoInfo.material )[ 0 ];\n\n\t\t\t\tif ( materialIndex < 0 ) {\n\n\t\t\t\t\tscope.negativeMaterialIndices = true;\n\t\t\t\t\tmaterialIndex = 0; // fallback\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.uv ) {\n\n\t\t\t\tgeoInfo.uv.forEach( function ( uv, i ) {\n\n\t\t\t\t\tconst data = getData( polygonVertexIndex, polygonIndex, vertexIndex, uv );\n\n\t\t\t\t\tif ( faceUVs[ i ] === undefined ) {\n\n\t\t\t\t\t\tfaceUVs[ i ] = [];\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfaceUVs[ i ].push( data[ 0 ] );\n\t\t\t\t\tfaceUVs[ i ].push( data[ 1 ] );\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tfaceLength ++;\n\n\t\t\tif ( endOfFace ) {\n\n\t\t\t\tscope.genFace( buffers, geoInfo, facePositionIndexes, materialIndex, faceNormals, faceColors, faceUVs, faceWeights, faceWeightIndices, faceLength );\n\n\t\t\t\tpolygonIndex ++;\n\t\t\t\tfaceLength = 0;\n\n\t\t\t\t// reset arrays for the next face\n\t\t\t\tfacePositionIndexes = [];\n\t\t\t\tfaceNormals = [];\n\t\t\t\tfaceColors = [];\n\t\t\t\tfaceUVs = [];\n\t\t\t\tfaceWeights = [];\n\t\t\t\tfaceWeightIndices = [];\n\n\t\t\t}\n\n\t\t} );\n\n\t\treturn buffers;\n\n\t}\n\n\t// See https://www.khronos.org/opengl/wiki/Calculating_a_Surface_Normal\n\tgetNormalNewell( vertices ) {\n\n\t\tconst normal = new Vector3( 0.0, 0.0, 0.0 );\n\n\t\tfor ( let i = 0; i < vertices.length; i ++ ) {\n\n\t\t\tconst current = vertices[ i ];\n\t\t\tconst next = vertices[ ( i + 1 ) % vertices.length ];\n\n\t\t\tnormal.x += ( current.y - next.y ) * ( current.z + next.z );\n\t\t\tnormal.y += ( current.z - next.z ) * ( current.x + next.x );\n\t\t\tnormal.z += ( current.x - next.x ) * ( current.y + next.y );\n\n\t\t}\n\n\t\tnormal.normalize();\n\n\t\treturn normal;\n\n\t}\n\n\tgetNormalTangentAndBitangent( vertices ) {\n\n\t\tconst normalVector = this.getNormalNewell( vertices );\n\t\t// Avoid up being equal or almost equal to normalVector\n\t\tconst up = Math.abs( normalVector.z ) > 0.5 ? new Vector3( 0.0, 1.0, 0.0 ) : new Vector3( 0.0, 0.0, 1.0 );\n\t\tconst tangent = up.cross( normalVector ).normalize();\n\t\tconst bitangent = normalVector.clone().cross( tangent ).normalize();\n\n\t\treturn {\n\t\t\tnormal: normalVector,\n\t\t\ttangent: tangent,\n\t\t\tbitangent: bitangent\n\t\t};\n\n\t}\n\n\tflattenVertex( vertex, normalTangent, normalBitangent ) {\n\n\t\treturn new Vector2(\n\t\t\tvertex.dot( normalTangent ),\n\t\t\tvertex.dot( normalBitangent )\n\t\t);\n\n\t}\n\n\t// Generate data for a single face in a geometry. If the face is a quad then split it into 2 tris\n\tgenFace( buffers, geoInfo, facePositionIndexes, materialIndex, faceNormals, faceColors, faceUVs, faceWeights, faceWeightIndices, faceLength ) {\n\n\t\tlet triangles;\n\n\t\tif ( faceLength > 3 ) {\n\n\t\t\t// Triangulate n-gon using earcut\n\n\t\t\tconst vertices = [];\n\n\t\t\tfor ( let i = 0; i < facePositionIndexes.length; i += 3 ) {\n\n\t\t\t\tvertices.push( new Vector3(\n\t\t\t\t\tgeoInfo.vertexPositions[ facePositionIndexes[ i ] ],\n\t\t\t\t\tgeoInfo.vertexPositions[ facePositionIndexes[ i + 1 ] ],\n\t\t\t\t\tgeoInfo.vertexPositions[ facePositionIndexes[ i + 2 ] ]\n\t\t\t\t) );\n\n\t\t\t}\n\n\t\t\tconst { tangent, bitangent } = this.getNormalTangentAndBitangent( vertices );\n\t\t\tconst triangulationInput = [];\n\n\t\t\tfor ( const vertex of vertices ) {\n\n\t\t\t\ttriangulationInput.push( this.flattenVertex( vertex, tangent, bitangent ) );\n\n\t\t\t}\n\n\t\t\ttriangles = ShapeUtils.triangulateShape( triangulationInput, [] );\n\n\t\t} else {\n\n\t\t\t// Regular triangle, skip earcut triangulation step\n\t\t\ttriangles = [[ 0, 1, 2 ]];\n\n\t\t}\n\n\t\tfor ( const [ i0, i1, i2 ] of triangles ) {\n\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i0 * 3 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i0 * 3 + 1 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i0 * 3 + 2 ] ] );\n\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i1 * 3 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i1 * 3 + 1 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i1 * 3 + 2 ] ] );\n\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i2 * 3 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i2 * 3 + 1 ] ] );\n\t\t\tbuffers.vertex.push( geoInfo.vertexPositions[ facePositionIndexes[ i2 * 3 + 2 ] ] );\n\n\t\t\tif ( geoInfo.skeleton ) {\n\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i0 * 4 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i0 * 4 + 1 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i0 * 4 + 2 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i0 * 4 + 3 ] );\n\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i1 * 4 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i1 * 4 + 1 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i1 * 4 + 2 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i1 * 4 + 3 ] );\n\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i2 * 4 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i2 * 4 + 1 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i2 * 4 + 2 ] );\n\t\t\t\tbuffers.vertexWeights.push( faceWeights[ i2 * 4 + 3 ] );\n\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i0 * 4 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i0 * 4 + 1 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i0 * 4 + 2 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i0 * 4 + 3 ] );\n\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i1 * 4 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i1 * 4 + 1 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i1 * 4 + 2 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i1 * 4 + 3 ] );\n\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i2 * 4 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i2 * 4 + 1 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i2 * 4 + 2 ] );\n\t\t\t\tbuffers.weightsIndices.push( faceWeightIndices[ i2 * 4 + 3 ] );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.color ) {\n\n\t\t\t\tbuffers.colors.push( faceColors[ i0 * 3 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i0 * 3 + 1 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i0 * 3 + 2 ] );\n\n\t\t\t\tbuffers.colors.push( faceColors[ i1 * 3 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i1 * 3 + 1 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i1 * 3 + 2 ] );\n\n\t\t\t\tbuffers.colors.push( faceColors[ i2 * 3 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i2 * 3 + 1 ] );\n\t\t\t\tbuffers.colors.push( faceColors[ i2 * 3 + 2 ] );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.material && geoInfo.material.mappingType !== 'AllSame' ) {\n\n\t\t\t\tbuffers.materialIndex.push( materialIndex );\n\t\t\t\tbuffers.materialIndex.push( materialIndex );\n\t\t\t\tbuffers.materialIndex.push( materialIndex );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.normal ) {\n\n\t\t\t\tbuffers.normal.push( faceNormals[ i0 * 3 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i0 * 3 + 1 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i0 * 3 + 2 ] );\n\n\t\t\t\tbuffers.normal.push( faceNormals[ i1 * 3 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i1 * 3 + 1 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i1 * 3 + 2 ] );\n\n\t\t\t\tbuffers.normal.push( faceNormals[ i2 * 3 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i2 * 3 + 1 ] );\n\t\t\t\tbuffers.normal.push( faceNormals[ i2 * 3 + 2 ] );\n\n\t\t\t}\n\n\t\t\tif ( geoInfo.uv ) {\n\n\t\t\t\tgeoInfo.uv.forEach( function ( uv, j ) {\n\n\t\t\t\t\tif ( buffers.uvs[ j ] === undefined ) buffers.uvs[ j ] = [];\n\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i0 * 2 ] );\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i0 * 2 + 1 ] );\n\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i1 * 2 ] );\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i1 * 2 + 1 ] );\n\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i2 * 2 ] );\n\t\t\t\t\tbuffers.uvs[ j ].push( faceUVs[ j ][ i2 * 2 + 1 ] );\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\taddMorphTargets( parentGeo, parentGeoNode, morphTargets, preTransform ) {\n\n\t\tif ( morphTargets.length === 0 ) return;\n\n\t\tparentGeo.morphTargetsRelative = true;\n\n\t\tparentGeo.morphAttributes.position = [];\n\t\t// parentGeo.morphAttributes.normal = []; // not implemented\n\n\t\tconst scope = this;\n\t\tmorphTargets.forEach( function ( morphTarget ) {\n\n\t\t\tmorphTarget.rawTargets.forEach( function ( rawTarget ) {\n\n\t\t\t\tconst morphGeoNode = fbxTree.Objects.Geometry[ rawTarget.geoID ];\n\n\t\t\t\tif ( morphGeoNode !== undefined ) {\n\n\t\t\t\t\tscope.genMorphGeometry( parentGeo, parentGeoNode, morphGeoNode, preTransform, rawTarget.name );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\t// a morph geometry node is similar to a standard  node, and the node is also contained\n\t// in FBXTree.Objects.Geometry, however it can only have attributes for position, normal\n\t// and a special attribute Index defining which vertices of the original geometry are affected\n\t// Normal and position attributes only have data for the vertices that are affected by the morph\n\tgenMorphGeometry( parentGeo, parentGeoNode, morphGeoNode, preTransform, name ) {\n\n\t\tconst vertexIndices = ( parentGeoNode.PolygonVertexIndex !== undefined ) ? parentGeoNode.PolygonVertexIndex.a : [];\n\n\t\tconst morphPositionsSparse = ( morphGeoNode.Vertices !== undefined ) ? morphGeoNode.Vertices.a : [];\n\t\tconst indices = ( morphGeoNode.Indexes !== undefined ) ? morphGeoNode.Indexes.a : [];\n\n\t\tconst length = parentGeo.attributes.position.count * 3;\n\t\tconst morphPositions = new Float32Array( length );\n\n\t\tfor ( let i = 0; i < indices.length; i ++ ) {\n\n\t\t\tconst morphIndex = indices[ i ] * 3;\n\n\t\t\tmorphPositions[ morphIndex ] = morphPositionsSparse[ i * 3 ];\n\t\t\tmorphPositions[ morphIndex + 1 ] = morphPositionsSparse[ i * 3 + 1 ];\n\t\t\tmorphPositions[ morphIndex + 2 ] = morphPositionsSparse[ i * 3 + 2 ];\n\n\t\t}\n\n\t\t// TODO: add morph normal support\n\t\tconst morphGeoInfo = {\n\t\t\tvertexIndices: vertexIndices,\n\t\t\tvertexPositions: morphPositions,\n\n\t\t};\n\n\t\tconst morphBuffers = this.genBuffers( morphGeoInfo );\n\n\t\tconst positionAttribute = new Float32BufferAttribute( morphBuffers.vertex, 3 );\n\t\tpositionAttribute.name = name || morphGeoNode.attrName;\n\n\t\tpositionAttribute.applyMatrix4( preTransform );\n\n\t\tparentGeo.morphAttributes.position.push( positionAttribute );\n\n\t}\n\n\t// Parse normal from FBXTree.Objects.Geometry.LayerElementNormal if it exists\n\tparseNormals( NormalNode ) {\n\n\t\tconst mappingType = NormalNode.MappingInformationType;\n\t\tconst referenceType = NormalNode.ReferenceInformationType;\n\t\tconst buffer = NormalNode.Normals.a;\n\t\tlet indexBuffer = [];\n\t\tif ( referenceType === 'IndexToDirect' ) {\n\n\t\t\tif ( 'NormalIndex' in NormalNode ) {\n\n\t\t\t\tindexBuffer = NormalNode.NormalIndex.a;\n\n\t\t\t} else if ( 'NormalsIndex' in NormalNode ) {\n\n\t\t\t\tindexBuffer = NormalNode.NormalsIndex.a;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn {\n\t\t\tdataSize: 3,\n\t\t\tbuffer: buffer,\n\t\t\tindices: indexBuffer,\n\t\t\tmappingType: mappingType,\n\t\t\treferenceType: referenceType\n\t\t};\n\n\t}\n\n\t// Parse UVs from FBXTree.Objects.Geometry.LayerElementUV if it exists\n\tparseUVs( UVNode ) {\n\n\t\tconst mappingType = UVNode.MappingInformationType;\n\t\tconst referenceType = UVNode.ReferenceInformationType;\n\t\tconst buffer = UVNode.UV.a;\n\t\tlet indexBuffer = [];\n\t\tif ( referenceType === 'IndexToDirect' ) {\n\n\t\t\tindexBuffer = UVNode.UVIndex.a;\n\n\t\t}\n\n\t\treturn {\n\t\t\tdataSize: 2,\n\t\t\tbuffer: buffer,\n\t\t\tindices: indexBuffer,\n\t\t\tmappingType: mappingType,\n\t\t\treferenceType: referenceType\n\t\t};\n\n\t}\n\n\t// Parse Vertex Colors from FBXTree.Objects.Geometry.LayerElementColor if it exists\n\tparseVertexColors( ColorNode ) {\n\n\t\tconst mappingType = ColorNode.MappingInformationType;\n\t\tconst referenceType = ColorNode.ReferenceInformationType;\n\t\tconst buffer = ColorNode.Colors.a;\n\t\tlet indexBuffer = [];\n\t\tif ( referenceType === 'IndexToDirect' ) {\n\n\t\t\tindexBuffer = ColorNode.ColorIndex.a;\n\n\t\t}\n\n\t\tfor ( let i = 0, c = new Color(); i < buffer.length; i += 4 ) {\n\n\t\t\tc.fromArray( buffer, i ).convertSRGBToLinear().toArray( buffer, i );\n\n\t\t}\n\n\t\treturn {\n\t\t\tdataSize: 4,\n\t\t\tbuffer: buffer,\n\t\t\tindices: indexBuffer,\n\t\t\tmappingType: mappingType,\n\t\t\treferenceType: referenceType\n\t\t};\n\n\t}\n\n\t// Parse mapping and material data in FBXTree.Objects.Geometry.LayerElementMaterial if it exists\n\tparseMaterialIndices( MaterialNode ) {\n\n\t\tconst mappingType = MaterialNode.MappingInformationType;\n\t\tconst referenceType = MaterialNode.ReferenceInformationType;\n\n\t\tif ( mappingType === 'NoMappingInformation' ) {\n\n\t\t\treturn {\n\t\t\t\tdataSize: 1,\n\t\t\t\tbuffer: [ 0 ],\n\t\t\t\tindices: [ 0 ],\n\t\t\t\tmappingType: 'AllSame',\n\t\t\t\treferenceType: referenceType\n\t\t\t};\n\n\t\t}\n\n\t\tconst materialIndexBuffer = MaterialNode.Materials.a;\n\n\t\t// Since materials are stored as indices, there's a bit of a mismatch between FBX and what\n\t\t// we expect.So we create an intermediate buffer that points to the index in the buffer,\n\t\t// for conforming with the other functions we've written for other data.\n\t\tconst materialIndices = [];\n\n\t\tfor ( let i = 0; i < materialIndexBuffer.length; ++ i ) {\n\n\t\t\tmaterialIndices.push( i );\n\n\t\t}\n\n\t\treturn {\n\t\t\tdataSize: 1,\n\t\t\tbuffer: materialIndexBuffer,\n\t\t\tindices: materialIndices,\n\t\t\tmappingType: mappingType,\n\t\t\treferenceType: referenceType\n\t\t};\n\n\t}\n\n\t// Generate a NurbGeometry from a node in FBXTree.Objects.Geometry\n\tparseNurbsGeometry( geoNode ) {\n\n\t\tconst order = parseInt( geoNode.Order );\n\n\t\tif ( isNaN( order ) ) {\n\n\t\t\tconsole.error( 'THREE.FBXLoader: Invalid Order %s given for geometry ID: %s', geoNode.Order, geoNode.id );\n\t\t\treturn new BufferGeometry();\n\n\t\t}\n\n\t\tconst degree = order - 1;\n\n\t\tconst knots = geoNode.KnotVector.a;\n\t\tconst controlPoints = [];\n\t\tconst pointsValues = geoNode.Points.a;\n\n\t\tfor ( let i = 0, l = pointsValues.length; i < l; i += 4 ) {\n\n\t\t\tcontrolPoints.push( new Vector4().fromArray( pointsValues, i ) );\n\n\t\t}\n\n\t\tlet startKnot, endKnot;\n\n\t\tif ( geoNode.Form === 'Closed' ) {\n\n\t\t\tcontrolPoints.push( controlPoints[ 0 ] );\n\n\t\t} else if ( geoNode.Form === 'Periodic' ) {\n\n\t\t\tstartKnot = degree;\n\t\t\tendKnot = knots.length - 1 - startKnot;\n\n\t\t\tfor ( let i = 0; i < degree; ++ i ) {\n\n\t\t\t\tcontrolPoints.push( controlPoints[ i ] );\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst curve = new NURBSCurve( degree, knots, controlPoints, startKnot, endKnot );\n\t\tconst points = curve.getPoints( controlPoints.length * 12 );\n\n\t\treturn new BufferGeometry().setFromPoints( points );\n\n\t}\n\n}\n\n// parse animation data from FBXTree\nclass AnimationParser {\n\n\t// take raw animation clips and turn them into three.js animation clips\n\tparse() {\n\n\t\tconst animationClips = [];\n\n\t\tconst rawClips = this.parseClips();\n\n\t\tif ( rawClips !== undefined ) {\n\n\t\t\tfor ( const key in rawClips ) {\n\n\t\t\t\tconst rawClip = rawClips[ key ];\n\n\t\t\t\tconst clip = this.addClip( rawClip );\n\n\t\t\t\tanimationClips.push( clip );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn animationClips;\n\n\t}\n\n\tparseClips() {\n\n\t\t// since the actual transformation data is stored in FBXTree.Objects.AnimationCurve,\n\t\t// if this is undefined we can safely assume there are no animations\n\t\tif ( fbxTree.Objects.AnimationCurve === undefined ) return undefined;\n\n\t\tconst curveNodesMap = this.parseAnimationCurveNodes();\n\n\t\tthis.parseAnimationCurves( curveNodesMap );\n\n\t\tconst layersMap = this.parseAnimationLayers( curveNodesMap );\n\t\tconst rawClips = this.parseAnimStacks( layersMap );\n\n\t\treturn rawClips;\n\n\t}\n\n\t// parse nodes in FBXTree.Objects.AnimationCurveNode\n\t// each AnimationCurveNode holds data for an animation transform for a model (e.g. left arm rotation )\n\t// and is referenced by an AnimationLayer\n\tparseAnimationCurveNodes() {\n\n\t\tconst rawCurveNodes = fbxTree.Objects.AnimationCurveNode;\n\n\t\tconst curveNodesMap = new Map();\n\n\t\tfor ( const nodeID in rawCurveNodes ) {\n\n\t\t\tconst rawCurveNode = rawCurveNodes[ nodeID ];\n\n\t\t\tif ( rawCurveNode.attrName.match( /S|R|T|DeformPercent/ ) !== null ) {\n\n\t\t\t\tconst curveNode = {\n\n\t\t\t\t\tid: rawCurveNode.id,\n\t\t\t\t\tattr: rawCurveNode.attrName,\n\t\t\t\t\tcurves: {},\n\n\t\t\t\t};\n\n\t\t\t\tcurveNodesMap.set( curveNode.id, curveNode );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn curveNodesMap;\n\n\t}\n\n\t// parse nodes in FBXTree.Objects.AnimationCurve and connect them up to\n\t// previously parsed AnimationCurveNodes. Each AnimationCurve holds data for a single animated\n\t// axis ( e.g. times and values of x rotation)\n\tparseAnimationCurves( curveNodesMap ) {\n\n\t\tconst rawCurves = fbxTree.Objects.AnimationCurve;\n\n\t\t// TODO: Many values are identical up to roundoff error, but won't be optimised\n\t\t// e.g. position times: [0, 0.4, 0. 8]\n\t\t// position values: [7.23538335023477e-7, 93.67518615722656, -0.9982695579528809, 7.23538335023477e-7, 93.67518615722656, -0.9982695579528809, 7.235384487103147e-7, 93.67520904541016, -0.9982695579528809]\n\t\t// clearly, this should be optimised to\n\t\t// times: [0], positions [7.23538335023477e-7, 93.67518615722656, -0.9982695579528809]\n\t\t// this shows up in nearly every FBX file, and generally time array is length > 100\n\n\t\tfor ( const nodeID in rawCurves ) {\n\n\t\t\tconst animationCurve = {\n\n\t\t\t\tid: rawCurves[ nodeID ].id,\n\t\t\t\ttimes: rawCurves[ nodeID ].KeyTime.a.map( convertFBXTimeToSeconds ),\n\t\t\t\tvalues: rawCurves[ nodeID ].KeyValueFloat.a,\n\n\t\t\t};\n\n\t\t\tconst relationships = connections.get( animationCurve.id );\n\n\t\t\tif ( relationships !== undefined ) {\n\n\t\t\t\tconst animationCurveID = relationships.parents[ 0 ].ID;\n\t\t\t\tconst animationCurveRelationship = relationships.parents[ 0 ].relationship;\n\n\t\t\t\tif ( animationCurveRelationship.match( /X/ ) ) {\n\n\t\t\t\t\tcurveNodesMap.get( animationCurveID ).curves[ 'x' ] = animationCurve;\n\n\t\t\t\t} else if ( animationCurveRelationship.match( /Y/ ) ) {\n\n\t\t\t\t\tcurveNodesMap.get( animationCurveID ).curves[ 'y' ] = animationCurve;\n\n\t\t\t\t} else if ( animationCurveRelationship.match( /Z/ ) ) {\n\n\t\t\t\t\tcurveNodesMap.get( animationCurveID ).curves[ 'z' ] = animationCurve;\n\n\t\t\t\t} else if ( animationCurveRelationship.match( /DeformPercent/ ) && curveNodesMap.has( animationCurveID ) ) {\n\n\t\t\t\t\tcurveNodesMap.get( animationCurveID ).curves[ 'morph' ] = animationCurve;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// parse nodes in FBXTree.Objects.AnimationLayer. Each layers holds references\n\t// to various AnimationCurveNodes and is referenced by an AnimationStack node\n\t// note: theoretically a stack can have multiple layers, however in practice there always seems to be one per stack\n\tparseAnimationLayers( curveNodesMap ) {\n\n\t\tconst rawLayers = fbxTree.Objects.AnimationLayer;\n\n\t\tconst layersMap = new Map();\n\n\t\tfor ( const nodeID in rawLayers ) {\n\n\t\t\tconst layerCurveNodes = [];\n\n\t\t\tconst connection = connections.get( parseInt( nodeID ) );\n\n\t\t\tif ( connection !== undefined ) {\n\n\t\t\t\t// all the animationCurveNodes used in the layer\n\t\t\t\tconst children = connection.children;\n\n\t\t\t\tchildren.forEach( function ( child, i ) {\n\n\t\t\t\t\tif ( curveNodesMap.has( child.ID ) ) {\n\n\t\t\t\t\t\tconst curveNode = curveNodesMap.get( child.ID );\n\n\t\t\t\t\t\t// check that the curves are defined for at least one axis, otherwise ignore the curveNode\n\t\t\t\t\t\tif ( curveNode.curves.x !== undefined || curveNode.curves.y !== undefined || curveNode.curves.z !== undefined ) {\n\n\t\t\t\t\t\t\tif ( layerCurveNodes[ i ] === undefined ) {\n\n\t\t\t\t\t\t\t\tconst modelID = connections.get( child.ID ).parents.filter( function ( parent ) {\n\n\t\t\t\t\t\t\t\t\treturn parent.relationship !== undefined;\n\n\t\t\t\t\t\t\t\t} )[ 0 ].ID;\n\n\t\t\t\t\t\t\t\tif ( modelID !== undefined ) {\n\n\t\t\t\t\t\t\t\t\tconst rawModel = fbxTree.Objects.Model[ modelID.toString() ];\n\n\t\t\t\t\t\t\t\t\tif ( rawModel === undefined ) {\n\n\t\t\t\t\t\t\t\t\t\tconsole.warn( 'THREE.FBXLoader: Encountered a unused curve.', child );\n\t\t\t\t\t\t\t\t\t\treturn;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tconst node = {\n\n\t\t\t\t\t\t\t\t\t\tmodelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName( rawModel.attrName ) : '',\n\t\t\t\t\t\t\t\t\t\tID: rawModel.id,\n\t\t\t\t\t\t\t\t\t\tinitialPosition: [ 0, 0, 0 ],\n\t\t\t\t\t\t\t\t\t\tinitialRotation: [ 0, 0, 0 ],\n\t\t\t\t\t\t\t\t\t\tinitialScale: [ 1, 1, 1 ],\n\n\t\t\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t\t\tsceneGraph.traverse( function ( child ) {\n\n\t\t\t\t\t\t\t\t\t\tif ( child.ID === rawModel.id ) {\n\n\t\t\t\t\t\t\t\t\t\t\tnode.transform = child.matrix;\n\n\t\t\t\t\t\t\t\t\t\t\tif ( child.userData.transformData ) node.eulerOrder = child.userData.transformData.eulerOrder;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t} );\n\n\t\t\t\t\t\t\t\t\tif ( ! node.transform ) node.transform = new Matrix4();\n\n\t\t\t\t\t\t\t\t\t// if the animated model is pre rotated, we'll have to apply the pre rotations to every\n\t\t\t\t\t\t\t\t\t// animation value as well\n\t\t\t\t\t\t\t\t\tif ( 'PreRotation' in rawModel ) node.preRotation = rawModel.PreRotation.value;\n\t\t\t\t\t\t\t\t\tif ( 'PostRotation' in rawModel ) node.postRotation = rawModel.PostRotation.value;\n\n\t\t\t\t\t\t\t\t\tlayerCurveNodes[ i ] = node;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif ( layerCurveNodes[ i ] ) layerCurveNodes[ i ][ curveNode.attr ] = curveNode;\n\n\t\t\t\t\t\t} else if ( curveNode.curves.morph !== undefined ) {\n\n\t\t\t\t\t\t\tif ( layerCurveNodes[ i ] === undefined ) {\n\n\t\t\t\t\t\t\t\tconst deformerID = connections.get( child.ID ).parents.filter( function ( parent ) {\n\n\t\t\t\t\t\t\t\t\treturn parent.relationship !== undefined;\n\n\t\t\t\t\t\t\t\t} )[ 0 ].ID;\n\n\t\t\t\t\t\t\t\tconst morpherID = connections.get( deformerID ).parents[ 0 ].ID;\n\t\t\t\t\t\t\t\tconst geoID = connections.get( morpherID ).parents[ 0 ].ID;\n\n\t\t\t\t\t\t\t\t// assuming geometry is not used in more than one model\n\t\t\t\t\t\t\t\tconst modelID = connections.get( geoID ).parents[ 0 ].ID;\n\n\t\t\t\t\t\t\t\tconst rawModel = fbxTree.Objects.Model[ modelID ];\n\n\t\t\t\t\t\t\t\tconst node = {\n\n\t\t\t\t\t\t\t\t\tmodelName: rawModel.attrName ? PropertyBinding.sanitizeNodeName( rawModel.attrName ) : '',\n\t\t\t\t\t\t\t\t\tmorphName: fbxTree.Objects.Deformer[ deformerID ].attrName,\n\n\t\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t\tlayerCurveNodes[ i ] = node;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tlayerCurveNodes[ i ][ curveNode.attr ] = curveNode;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\tlayersMap.set( parseInt( nodeID ), layerCurveNodes );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn layersMap;\n\n\t}\n\n\t// parse nodes in FBXTree.Objects.AnimationStack. These are the top level node in the animation\n\t// hierarchy. Each Stack node will be used to create a AnimationClip\n\tparseAnimStacks( layersMap ) {\n\n\t\tconst rawStacks = fbxTree.Objects.AnimationStack;\n\n\t\t// connect the stacks (clips) up to the layers\n\t\tconst rawClips = {};\n\n\t\tfor ( const nodeID in rawStacks ) {\n\n\t\t\tconst children = connections.get( parseInt( nodeID ) ).children;\n\n\t\t\tif ( children.length > 1 ) {\n\n\t\t\t\t// it seems like stacks will always be associated with a single layer. But just in case there are files\n\t\t\t\t// where there are multiple layers per stack, we'll display a warning\n\t\t\t\tconsole.warn( 'THREE.FBXLoader: Encountered an animation stack with multiple layers, this is currently not supported. Ignoring subsequent layers.' );\n\n\t\t\t}\n\n\t\t\tconst layer = layersMap.get( children[ 0 ].ID );\n\n\t\t\trawClips[ nodeID ] = {\n\n\t\t\t\tname: rawStacks[ nodeID ].attrName,\n\t\t\t\tlayer: layer,\n\n\t\t\t};\n\n\t\t}\n\n\t\treturn rawClips;\n\n\t}\n\n\taddClip( rawClip ) {\n\n\t\tlet tracks = [];\n\n\t\tconst scope = this;\n\t\trawClip.layer.forEach( function ( rawTracks ) {\n\n\t\t\ttracks = tracks.concat( scope.generateTracks( rawTracks ) );\n\n\t\t} );\n\n\t\treturn new AnimationClip( rawClip.name, - 1, tracks );\n\n\t}\n\n\tgenerateTracks( rawTracks ) {\n\n\t\tconst tracks = [];\n\n\t\tlet initialPosition = new Vector3();\n\t\tlet initialScale = new Vector3();\n\n\t\tif ( rawTracks.transform ) rawTracks.transform.decompose( initialPosition, new Quaternion(), initialScale );\n\n\t\tinitialPosition = initialPosition.toArray();\n\t\tinitialScale = initialScale.toArray();\n\n\t\tif ( rawTracks.T !== undefined && Object.keys( rawTracks.T.curves ).length > 0 ) {\n\n\t\t\tconst positionTrack = this.generateVectorTrack( rawTracks.modelName, rawTracks.T.curves, initialPosition, 'position' );\n\t\t\tif ( positionTrack !== undefined ) tracks.push( positionTrack );\n\n\t\t}\n\n\t\tif ( rawTracks.R !== undefined && Object.keys( rawTracks.R.curves ).length > 0 ) {\n\n\t\t\tconst rotationTrack = this.generateRotationTrack( rawTracks.modelName, rawTracks.R.curves, rawTracks.preRotation, rawTracks.postRotation, rawTracks.eulerOrder );\n\t\t\tif ( rotationTrack !== undefined ) tracks.push( rotationTrack );\n\n\t\t}\n\n\t\tif ( rawTracks.S !== undefined && Object.keys( rawTracks.S.curves ).length > 0 ) {\n\n\t\t\tconst scaleTrack = this.generateVectorTrack( rawTracks.modelName, rawTracks.S.curves, initialScale, 'scale' );\n\t\t\tif ( scaleTrack !== undefined ) tracks.push( scaleTrack );\n\n\t\t}\n\n\t\tif ( rawTracks.DeformPercent !== undefined ) {\n\n\t\t\tconst morphTrack = this.generateMorphTrack( rawTracks );\n\t\t\tif ( morphTrack !== undefined ) tracks.push( morphTrack );\n\n\t\t}\n\n\t\treturn tracks;\n\n\t}\n\n\tgenerateVectorTrack( modelName, curves, initialValue, type ) {\n\n\t\tconst times = this.getTimesForAllAxes( curves );\n\t\tconst values = this.getKeyframeTrackValues( times, curves, initialValue );\n\n\t\treturn new VectorKeyframeTrack( modelName + '.' + type, times, values );\n\n\t}\n\n\tgenerateRotationTrack( modelName, curves, preRotation, postRotation, eulerOrder ) {\n\n\t\tlet times;\n\t\tlet values;\n\n\t\tif ( curves.x !== undefined && curves.y !== undefined && curves.z !== undefined ) {\n\n\t\t\tconst result = this.interpolateRotations( curves.x, curves.y, curves.z, eulerOrder );\n\n\t\t\ttimes = result[ 0 ];\n\t\t\tvalues = result[ 1 ];\n\n\t\t}\n\n\t\tif ( preRotation !== undefined ) {\n\n\t\t\tpreRotation = preRotation.map( MathUtils.degToRad );\n\t\t\tpreRotation.push( eulerOrder );\n\n\t\t\tpreRotation = new Euler().fromArray( preRotation );\n\t\t\tpreRotation = new Quaternion().setFromEuler( preRotation );\n\n\t\t}\n\n\t\tif ( postRotation !== undefined ) {\n\n\t\t\tpostRotation = postRotation.map( MathUtils.degToRad );\n\t\t\tpostRotation.push( eulerOrder );\n\n\t\t\tpostRotation = new Euler().fromArray( postRotation );\n\t\t\tpostRotation = new Quaternion().setFromEuler( postRotation ).invert();\n\n\t\t}\n\n\t\tconst quaternion = new Quaternion();\n\t\tconst euler = new Euler();\n\n\t\tconst quaternionValues = [];\n\n\t\tif ( ! values || ! times ) return new QuaternionKeyframeTrack( modelName + '.quaternion', [], [] );\n\n\t\tfor ( let i = 0; i < values.length; i += 3 ) {\n\n\t\t\teuler.set( values[ i ], values[ i + 1 ], values[ i + 2 ], eulerOrder );\n\t\t\tquaternion.setFromEuler( euler );\n\n\t\t\tif ( preRotation !== undefined ) quaternion.premultiply( preRotation );\n\t\t\tif ( postRotation !== undefined ) quaternion.multiply( postRotation );\n\n\t\t\t// Check unroll\n\t\t\tif ( i > 2 ) {\n\n\t\t\t\tconst prevQuat = new Quaternion().fromArray(\n\t\t\t\t\tquaternionValues,\n\t\t\t\t\t( ( i - 3 ) / 3 ) * 4\n\t\t\t\t);\n\n\t\t\t\tif ( prevQuat.dot( quaternion ) < 0 ) {\n\n\t\t\t\t\tquaternion.set( - quaternion.x, - quaternion.y, - quaternion.z, - quaternion.w );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tquaternion.toArray( quaternionValues, ( i / 3 ) * 4 );\n\n\t\t}\n\n\t\treturn new QuaternionKeyframeTrack( modelName + '.quaternion', times, quaternionValues );\n\n\t}\n\n\tgenerateMorphTrack( rawTracks ) {\n\n\t\tconst curves = rawTracks.DeformPercent.curves.morph;\n\t\tconst values = curves.values.map( function ( val ) {\n\n\t\t\treturn val / 100;\n\n\t\t} );\n\n\t\tconst morphNum = sceneGraph.getObjectByName( rawTracks.modelName ).morphTargetDictionary[ rawTracks.morphName ];\n\n\t\treturn new NumberKeyframeTrack( rawTracks.modelName + '.morphTargetInfluences[' + morphNum + ']', curves.times, values );\n\n\t}\n\n\t// For all animated objects, times are defined separately for each axis\n\t// Here we'll combine the times into one sorted array without duplicates\n\tgetTimesForAllAxes( curves ) {\n\n\t\tlet times = [];\n\n\t\t// first join together the times for each axis, if defined\n\t\tif ( curves.x !== undefined ) times = times.concat( curves.x.times );\n\t\tif ( curves.y !== undefined ) times = times.concat( curves.y.times );\n\t\tif ( curves.z !== undefined ) times = times.concat( curves.z.times );\n\n\t\t// then sort them\n\t\ttimes = times.sort( function ( a, b ) {\n\n\t\t\treturn a - b;\n\n\t\t} );\n\n\t\t// and remove duplicates\n\t\tif ( times.length > 1 ) {\n\n\t\t\tlet targetIndex = 1;\n\t\t\tlet lastValue = times[ 0 ];\n\t\t\tfor ( let i = 1; i < times.length; i ++ ) {\n\n\t\t\t\tconst currentValue = times[ i ];\n\t\t\t\tif ( currentValue !== lastValue ) {\n\n\t\t\t\t\ttimes[ targetIndex ] = currentValue;\n\t\t\t\t\tlastValue = currentValue;\n\t\t\t\t\ttargetIndex ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ttimes = times.slice( 0, targetIndex );\n\n\t\t}\n\n\t\treturn times;\n\n\t}\n\n\tgetKeyframeTrackValues( times, curves, initialValue ) {\n\n\t\tconst prevValue = initialValue;\n\n\t\tconst values = [];\n\n\t\tlet xIndex = - 1;\n\t\tlet yIndex = - 1;\n\t\tlet zIndex = - 1;\n\n\t\ttimes.forEach( function ( time ) {\n\n\t\t\tif ( curves.x ) xIndex = curves.x.times.indexOf( time );\n\t\t\tif ( curves.y ) yIndex = curves.y.times.indexOf( time );\n\t\t\tif ( curves.z ) zIndex = curves.z.times.indexOf( time );\n\n\t\t\t// if there is an x value defined for this frame, use that\n\t\t\tif ( xIndex !== - 1 ) {\n\n\t\t\t\tconst xValue = curves.x.values[ xIndex ];\n\t\t\t\tvalues.push( xValue );\n\t\t\t\tprevValue[ 0 ] = xValue;\n\n\t\t\t} else {\n\n\t\t\t\t// otherwise use the x value from the previous frame\n\t\t\t\tvalues.push( prevValue[ 0 ] );\n\n\t\t\t}\n\n\t\t\tif ( yIndex !== - 1 ) {\n\n\t\t\t\tconst yValue = curves.y.values[ yIndex ];\n\t\t\t\tvalues.push( yValue );\n\t\t\t\tprevValue[ 1 ] = yValue;\n\n\t\t\t} else {\n\n\t\t\t\tvalues.push( prevValue[ 1 ] );\n\n\t\t\t}\n\n\t\t\tif ( zIndex !== - 1 ) {\n\n\t\t\t\tconst zValue = curves.z.values[ zIndex ];\n\t\t\t\tvalues.push( zValue );\n\t\t\t\tprevValue[ 2 ] = zValue;\n\n\t\t\t} else {\n\n\t\t\t\tvalues.push( prevValue[ 2 ] );\n\n\t\t\t}\n\n\t\t} );\n\n\t\treturn values;\n\n\t}\n\n\t// Rotations are defined as Euler angles which can have values  of any size\n\t// These will be converted to quaternions which don't support values greater than\n\t// PI, so we'll interpolate large rotations\n\tinterpolateRotations( curvex, curvey, curvez, eulerOrder ) {\n\n\t\tconst times = [];\n\t\tconst values = [];\n\n\t\t// Add first frame\n\t\ttimes.push( curvex.times[ 0 ] );\n\t\tvalues.push( MathUtils.degToRad( curvex.values[ 0 ] ) );\n\t\tvalues.push( MathUtils.degToRad( curvey.values[ 0 ] ) );\n\t\tvalues.push( MathUtils.degToRad( curvez.values[ 0 ] ) );\n\n\t\tfor ( let i = 1; i < curvex.values.length; i ++ ) {\n\n\t\t\tconst initialValue = [\n\t\t\t\tcurvex.values[ i - 1 ],\n\t\t\t\tcurvey.values[ i - 1 ],\n\t\t\t\tcurvez.values[ i - 1 ],\n\t\t\t];\n\n\t\t\tif ( isNaN( initialValue[ 0 ] ) || isNaN( initialValue[ 1 ] ) || isNaN( initialValue[ 2 ] ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tconst initialValueRad = initialValue.map( MathUtils.degToRad );\n\n\t\t\tconst currentValue = [\n\t\t\t\tcurvex.values[ i ],\n\t\t\t\tcurvey.values[ i ],\n\t\t\t\tcurvez.values[ i ],\n\t\t\t];\n\n\t\t\tif ( isNaN( currentValue[ 0 ] ) || isNaN( currentValue[ 1 ] ) || isNaN( currentValue[ 2 ] ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tconst currentValueRad = currentValue.map( MathUtils.degToRad );\n\n\t\t\tconst valuesSpan = [\n\t\t\t\tcurrentValue[ 0 ] - initialValue[ 0 ],\n\t\t\t\tcurrentValue[ 1 ] - initialValue[ 1 ],\n\t\t\t\tcurrentValue[ 2 ] - initialValue[ 2 ],\n\t\t\t];\n\n\t\t\tconst absoluteSpan = [\n\t\t\t\tMath.abs( valuesSpan[ 0 ] ),\n\t\t\t\tMath.abs( valuesSpan[ 1 ] ),\n\t\t\t\tMath.abs( valuesSpan[ 2 ] ),\n\t\t\t];\n\n\t\t\tif ( absoluteSpan[ 0 ] >= 180 || absoluteSpan[ 1 ] >= 180 || absoluteSpan[ 2 ] >= 180 ) {\n\n\t\t\t\tconst maxAbsSpan = Math.max( ...absoluteSpan );\n\n\t\t\t\tconst numSubIntervals = maxAbsSpan / 180;\n\n\t\t\t\tconst E1 = new Euler( ...initialValueRad, eulerOrder );\n\t\t\t\tconst E2 = new Euler( ...currentValueRad, eulerOrder );\n\n\t\t\t\tconst Q1 = new Quaternion().setFromEuler( E1 );\n\t\t\t\tconst Q2 = new Quaternion().setFromEuler( E2 );\n\n\t\t\t\t// Check unroll\n\t\t\t\tif ( Q1.dot( Q2 ) ) {\n\n\t\t\t\t\tQ2.set( - Q2.x, - Q2.y, - Q2.z, - Q2.w );\n\n\t\t\t\t}\n\n\t\t\t\t// Interpolate\n\t\t\t\tconst initialTime = curvex.times[ i - 1 ];\n\t\t\t\tconst timeSpan = curvex.times[ i ] - initialTime;\n\n\t\t\t\tconst Q = new Quaternion();\n\t\t\t\tconst E = new Euler();\n\t\t\t\tfor ( let t = 0; t < 1; t += 1 / numSubIntervals ) {\n\n\t\t\t\t\tQ.copy( Q1.clone().slerp( Q2.clone(), t ) );\n\n\t\t\t\t\ttimes.push( initialTime + t * timeSpan );\n\t\t\t\t\tE.setFromQuaternion( Q, eulerOrder );\n\n\t\t\t\t\tvalues.push( E.x );\n\t\t\t\t\tvalues.push( E.y );\n\t\t\t\t\tvalues.push( E.z );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\ttimes.push( curvex.times[ i ] );\n\t\t\t\tvalues.push( MathUtils.degToRad( curvex.values[ i ] ) );\n\t\t\t\tvalues.push( MathUtils.degToRad( curvey.values[ i ] ) );\n\t\t\t\tvalues.push( MathUtils.degToRad( curvez.values[ i ] ) );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn [ times, values ];\n\n\t}\n\n}\n\n// parse an FBX file in ASCII format\nclass TextParser {\n\n\tgetPrevNode() {\n\n\t\treturn this.nodeStack[ this.currentIndent - 2 ];\n\n\t}\n\n\tgetCurrentNode() {\n\n\t\treturn this.nodeStack[ this.currentIndent - 1 ];\n\n\t}\n\n\tgetCurrentProp() {\n\n\t\treturn this.currentProp;\n\n\t}\n\n\tpushStack( node ) {\n\n\t\tthis.nodeStack.push( node );\n\t\tthis.currentIndent += 1;\n\n\t}\n\n\tpopStack() {\n\n\t\tthis.nodeStack.pop();\n\t\tthis.currentIndent -= 1;\n\n\t}\n\n\tsetCurrentProp( val, name ) {\n\n\t\tthis.currentProp = val;\n\t\tthis.currentPropName = name;\n\n\t}\n\n\tparse( text ) {\n\n\t\tthis.currentIndent = 0;\n\n\t\tthis.allNodes = new FBXTree();\n\t\tthis.nodeStack = [];\n\t\tthis.currentProp = [];\n\t\tthis.currentPropName = '';\n\n\t\tconst scope = this;\n\n\t\tconst split = text.split( /[\\r\\n]+/ );\n\n\t\tsplit.forEach( function ( line, i ) {\n\n\t\t\tconst matchComment = line.match( /^[\\s\\t]*;/ );\n\t\t\tconst matchEmpty = line.match( /^[\\s\\t]*$/ );\n\n\t\t\tif ( matchComment || matchEmpty ) return;\n\n\t\t\tconst matchBeginning = line.match( '^\\\\t{' + scope.currentIndent + '}(\\\\w+):(.*){', '' );\n\t\t\tconst matchProperty = line.match( '^\\\\t{' + ( scope.currentIndent ) + '}(\\\\w+):[\\\\s\\\\t\\\\r\\\\n](.*)' );\n\t\t\tconst matchEnd = line.match( '^\\\\t{' + ( scope.currentIndent - 1 ) + '}}' );\n\n\t\t\tif ( matchBeginning ) {\n\n\t\t\t\tscope.parseNodeBegin( line, matchBeginning );\n\n\t\t\t} else if ( matchProperty ) {\n\n\t\t\t\tscope.parseNodeProperty( line, matchProperty, split[ ++ i ] );\n\n\t\t\t} else if ( matchEnd ) {\n\n\t\t\t\tscope.popStack();\n\n\t\t\t} else if ( line.match( /^[^\\s\\t}]/ ) ) {\n\n\t\t\t\t// large arrays are split over multiple lines terminated with a ',' character\n\t\t\t\t// if this is encountered the line needs to be joined to the previous line\n\t\t\t\tscope.parseNodePropertyContinued( line );\n\n\t\t\t}\n\n\t\t} );\n\n\t\treturn this.allNodes;\n\n\t}\n\n\tparseNodeBegin( line, property ) {\n\n\t\tconst nodeName = property[ 1 ].trim().replace( /^\"/, '' ).replace( /\"$/, '' );\n\n\t\tconst nodeAttrs = property[ 2 ].split( ',' ).map( function ( attr ) {\n\n\t\t\treturn attr.trim().replace( /^\"/, '' ).replace( /\"$/, '' );\n\n\t\t} );\n\n\t\tconst node = { name: nodeName };\n\t\tconst attrs = this.parseNodeAttr( nodeAttrs );\n\n\t\tconst currentNode = this.getCurrentNode();\n\n\t\t// a top node\n\t\tif ( this.currentIndent === 0 ) {\n\n\t\t\tthis.allNodes.add( nodeName, node );\n\n\t\t} else { // a subnode\n\n\t\t\t// if the subnode already exists, append it\n\t\t\tif ( nodeName in currentNode ) {\n\n\t\t\t\t// special case Pose needs PoseNodes as an array\n\t\t\t\tif ( nodeName === 'PoseNode' ) {\n\n\t\t\t\t\tcurrentNode.PoseNode.push( node );\n\n\t\t\t\t} else if ( currentNode[ nodeName ].id !== undefined ) {\n\n\t\t\t\t\tcurrentNode[ nodeName ] = {};\n\t\t\t\t\tcurrentNode[ nodeName ][ currentNode[ nodeName ].id ] = currentNode[ nodeName ];\n\n\t\t\t\t}\n\n\t\t\t\tif ( attrs.id !== '' ) currentNode[ nodeName ][ attrs.id ] = node;\n\n\t\t\t} else if ( typeof attrs.id === 'number' ) {\n\n\t\t\t\tcurrentNode[ nodeName ] = {};\n\t\t\t\tcurrentNode[ nodeName ][ attrs.id ] = node;\n\n\t\t\t} else if ( nodeName !== 'Properties70' ) {\n\n\t\t\t\tif ( nodeName === 'PoseNode' )\tcurrentNode[ nodeName ] = [ node ];\n\t\t\t\telse currentNode[ nodeName ] = node;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( typeof attrs.id === 'number' ) node.id = attrs.id;\n\t\tif ( attrs.name !== '' ) node.attrName = attrs.name;\n\t\tif ( attrs.type !== '' ) node.attrType = attrs.type;\n\n\t\tthis.pushStack( node );\n\n\t}\n\n\tparseNodeAttr( attrs ) {\n\n\t\tlet id = attrs[ 0 ];\n\n\t\tif ( attrs[ 0 ] !== '' ) {\n\n\t\t\tid = parseInt( attrs[ 0 ] );\n\n\t\t\tif ( isNaN( id ) ) {\n\n\t\t\t\tid = attrs[ 0 ];\n\n\t\t\t}\n\n\t\t}\n\n\t\tlet name = '', type = '';\n\n\t\tif ( attrs.length > 1 ) {\n\n\t\t\tname = attrs[ 1 ].replace( /^(\\w+)::/, '' );\n\t\t\ttype = attrs[ 2 ];\n\n\t\t}\n\n\t\treturn { id: id, name: name, type: type };\n\n\t}\n\n\tparseNodeProperty( line, property, contentLine ) {\n\n\t\tlet propName = property[ 1 ].replace( /^\"/, '' ).replace( /\"$/, '' ).trim();\n\t\tlet propValue = property[ 2 ].replace( /^\"/, '' ).replace( /\"$/, '' ).trim();\n\n\t\t// for special case: base64 image data follows \"Content: ,\" line\n\t\t//\tContent: ,\n\t\t//\t \"/9j/4RDaRXhpZgAATU0A...\"\n\t\tif ( propName === 'Content' && propValue === ',' ) {\n\n\t\t\tpropValue = contentLine.replace( /\"/g, '' ).replace( /,$/, '' ).trim();\n\n\t\t}\n\n\t\tconst currentNode = this.getCurrentNode();\n\t\tconst parentName = currentNode.name;\n\n\t\tif ( parentName === 'Properties70' ) {\n\n\t\t\tthis.parseNodeSpecialProperty( line, propName, propValue );\n\t\t\treturn;\n\n\t\t}\n\n\t\t// Connections\n\t\tif ( propName === 'C' ) {\n\n\t\t\tconst connProps = propValue.split( ',' ).slice( 1 );\n\t\t\tconst from = parseInt( connProps[ 0 ] );\n\t\t\tconst to = parseInt( connProps[ 1 ] );\n\n\t\t\tlet rest = propValue.split( ',' ).slice( 3 );\n\n\t\t\trest = rest.map( function ( elem ) {\n\n\t\t\t\treturn elem.trim().replace( /^\"/, '' );\n\n\t\t\t} );\n\n\t\t\tpropName = 'connections';\n\t\t\tpropValue = [ from, to ];\n\t\t\tappend( propValue, rest );\n\n\t\t\tif ( currentNode[ propName ] === undefined ) {\n\n\t\t\t\tcurrentNode[ propName ] = [];\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Node\n\t\tif ( propName === 'Node' ) currentNode.id = propValue;\n\n\t\t// connections\n\t\tif ( propName in currentNode && Array.isArray( currentNode[ propName ] ) ) {\n\n\t\t\tcurrentNode[ propName ].push( propValue );\n\n\t\t} else {\n\n\t\t\tif ( propName !== 'a' ) currentNode[ propName ] = propValue;\n\t\t\telse currentNode.a = propValue;\n\n\t\t}\n\n\t\tthis.setCurrentProp( currentNode, propName );\n\n\t\t// convert string to array, unless it ends in ',' in which case more will be added to it\n\t\tif ( propName === 'a' && propValue.slice( - 1 ) !== ',' ) {\n\n\t\t\tcurrentNode.a = parseNumberArray( propValue );\n\n\t\t}\n\n\t}\n\n\tparseNodePropertyContinued( line ) {\n\n\t\tconst currentNode = this.getCurrentNode();\n\n\t\tcurrentNode.a += line;\n\n\t\t// if the line doesn't end in ',' we have reached the end of the property value\n\t\t// so convert the string to an array\n\t\tif ( line.slice( - 1 ) !== ',' ) {\n\n\t\t\tcurrentNode.a = parseNumberArray( currentNode.a );\n\n\t\t}\n\n\t}\n\n\t// parse \"Property70\"\n\tparseNodeSpecialProperty( line, propName, propValue ) {\n\n\t\t// split this\n\t\t// P: \"Lcl Scaling\", \"Lcl Scaling\", \"\", \"A\",1,1,1\n\t\t// into array like below\n\t\t// [\"Lcl Scaling\", \"Lcl Scaling\", \"\", \"A\", \"1,1,1\" ]\n\t\tconst props = propValue.split( '\",' ).map( function ( prop ) {\n\n\t\t\treturn prop.trim().replace( /^\\\"/, '' ).replace( /\\s/, '_' );\n\n\t\t} );\n\n\t\tconst innerPropName = props[ 0 ];\n\t\tconst innerPropType1 = props[ 1 ];\n\t\tconst innerPropType2 = props[ 2 ];\n\t\tconst innerPropFlag = props[ 3 ];\n\t\tlet innerPropValue = props[ 4 ];\n\n\t\t// cast values where needed, otherwise leave as strings\n\t\tswitch ( innerPropType1 ) {\n\n\t\t\tcase 'int':\n\t\t\tcase 'enum':\n\t\t\tcase 'bool':\n\t\t\tcase 'ULongLong':\n\t\t\tcase 'double':\n\t\t\tcase 'Number':\n\t\t\tcase 'FieldOfView':\n\t\t\t\tinnerPropValue = parseFloat( innerPropValue );\n\t\t\t\tbreak;\n\n\t\t\tcase 'Color':\n\t\t\tcase 'ColorRGB':\n\t\t\tcase 'Vector3D':\n\t\t\tcase 'Lcl_Translation':\n\t\t\tcase 'Lcl_Rotation':\n\t\t\tcase 'Lcl_Scaling':\n\t\t\t\tinnerPropValue = parseNumberArray( innerPropValue );\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// CAUTION: these props must append to parent's parent\n\t\tthis.getPrevNode()[ innerPropName ] = {\n\n\t\t\t'type': innerPropType1,\n\t\t\t'type2': innerPropType2,\n\t\t\t'flag': innerPropFlag,\n\t\t\t'value': innerPropValue\n\n\t\t};\n\n\t\tthis.setCurrentProp( this.getPrevNode(), innerPropName );\n\n\t}\n\n}\n\n// Parse an FBX file in Binary format\nclass BinaryParser {\n\n\tparse( buffer ) {\n\n\t\tconst reader = new BinaryReader( buffer );\n\t\treader.skip( 23 ); // skip magic 23 bytes\n\n\t\tconst version = reader.getUint32();\n\n\t\tif ( version < 6400 ) {\n\n\t\t\tthrow new Error( 'THREE.FBXLoader: FBX version not supported, FileVersion: ' + version );\n\n\t\t}\n\n\t\tconst allNodes = new FBXTree();\n\n\t\twhile ( ! this.endOfContent( reader ) ) {\n\n\t\t\tconst node = this.parseNode( reader, version );\n\t\t\tif ( node !== null ) allNodes.add( node.name, node );\n\n\t\t}\n\n\t\treturn allNodes;\n\n\t}\n\n\t// Check if reader has reached the end of content.\n\tendOfContent( reader ) {\n\n\t\t// footer size: 160bytes + 16-byte alignment padding\n\t\t// - 16bytes: magic\n\t\t// - padding til 16-byte alignment (at least 1byte?)\n\t\t//\t(seems like some exporters embed fixed 15 or 16bytes?)\n\t\t// - 4bytes: magic\n\t\t// - 4bytes: version\n\t\t// - 120bytes: zero\n\t\t// - 16bytes: magic\n\t\tif ( reader.size() % 16 === 0 ) {\n\n\t\t\treturn ( ( reader.getOffset() + 160 + 16 ) & ~ 0xf ) >= reader.size();\n\n\t\t} else {\n\n\t\t\treturn reader.getOffset() + 160 + 16 >= reader.size();\n\n\t\t}\n\n\t}\n\n\t// recursively parse nodes until the end of the file is reached\n\tparseNode( reader, version ) {\n\n\t\tconst node = {};\n\n\t\t// The first three data sizes depends on version.\n\t\tconst endOffset = ( version >= 7500 ) ? reader.getUint64() : reader.getUint32();\n\t\tconst numProperties = ( version >= 7500 ) ? reader.getUint64() : reader.getUint32();\n\n\t\t( version >= 7500 ) ? reader.getUint64() : reader.getUint32(); // the returned propertyListLen is not used\n\n\t\tconst nameLen = reader.getUint8();\n\t\tconst name = reader.getString( nameLen );\n\n\t\t// Regards this node as NULL-record if endOffset is zero\n\t\tif ( endOffset === 0 ) return null;\n\n\t\tconst propertyList = [];\n\n\t\tfor ( let i = 0; i < numProperties; i ++ ) {\n\n\t\t\tpropertyList.push( this.parseProperty( reader ) );\n\n\t\t}\n\n\t\t// Regards the first three elements in propertyList as id, attrName, and attrType\n\t\tconst id = propertyList.length > 0 ? propertyList[ 0 ] : '';\n\t\tconst attrName = propertyList.length > 1 ? propertyList[ 1 ] : '';\n\t\tconst attrType = propertyList.length > 2 ? propertyList[ 2 ] : '';\n\n\t\t// check if this node represents just a single property\n\t\t// like (name, 0) set or (name2, [0, 1, 2]) set of {name: 0, name2: [0, 1, 2]}\n\t\tnode.singleProperty = ( numProperties === 1 && reader.getOffset() === endOffset ) ? true : false;\n\n\t\twhile ( endOffset > reader.getOffset() ) {\n\n\t\t\tconst subNode = this.parseNode( reader, version );\n\n\t\t\tif ( subNode !== null ) this.parseSubNode( name, node, subNode );\n\n\t\t}\n\n\t\tnode.propertyList = propertyList; // raw property list used by parent\n\n\t\tif ( typeof id === 'number' ) node.id = id;\n\t\tif ( attrName !== '' ) node.attrName = attrName;\n\t\tif ( attrType !== '' ) node.attrType = attrType;\n\t\tif ( name !== '' ) node.name = name;\n\n\t\treturn node;\n\n\t}\n\n\tparseSubNode( name, node, subNode ) {\n\n\t\t// special case: child node is single property\n\t\tif ( subNode.singleProperty === true ) {\n\n\t\t\tconst value = subNode.propertyList[ 0 ];\n\n\t\t\tif ( Array.isArray( value ) ) {\n\n\t\t\t\tnode[ subNode.name ] = subNode;\n\n\t\t\t\tsubNode.a = value;\n\n\t\t\t} else {\n\n\t\t\t\tnode[ subNode.name ] = value;\n\n\t\t\t}\n\n\t\t} else if ( name === 'Connections' && subNode.name === 'C' ) {\n\n\t\t\tconst array = [];\n\n\t\t\tsubNode.propertyList.forEach( function ( property, i ) {\n\n\t\t\t\t// first Connection is FBX type (OO, OP, etc.). We'll discard these\n\t\t\t\tif ( i !== 0 ) array.push( property );\n\n\t\t\t} );\n\n\t\t\tif ( node.connections === undefined ) {\n\n\t\t\t\tnode.connections = [];\n\n\t\t\t}\n\n\t\t\tnode.connections.push( array );\n\n\t\t} else if ( subNode.name === 'Properties70' ) {\n\n\t\t\tconst keys = Object.keys( subNode );\n\n\t\t\tkeys.forEach( function ( key ) {\n\n\t\t\t\tnode[ key ] = subNode[ key ];\n\n\t\t\t} );\n\n\t\t} else if ( name === 'Properties70' && subNode.name === 'P' ) {\n\n\t\t\tlet innerPropName = subNode.propertyList[ 0 ];\n\t\t\tlet innerPropType1 = subNode.propertyList[ 1 ];\n\t\t\tconst innerPropType2 = subNode.propertyList[ 2 ];\n\t\t\tconst innerPropFlag = subNode.propertyList[ 3 ];\n\t\t\tlet innerPropValue;\n\n\t\t\tif ( innerPropName.indexOf( 'Lcl ' ) === 0 ) innerPropName = innerPropName.replace( 'Lcl ', 'Lcl_' );\n\t\t\tif ( innerPropType1.indexOf( 'Lcl ' ) === 0 ) innerPropType1 = innerPropType1.replace( 'Lcl ', 'Lcl_' );\n\n\t\t\tif ( innerPropType1 === 'Color' || innerPropType1 === 'ColorRGB' || innerPropType1 === 'Vector' || innerPropType1 === 'Vector3D' || innerPropType1.indexOf( 'Lcl_' ) === 0 ) {\n\n\t\t\t\tinnerPropValue = [\n\t\t\t\t\tsubNode.propertyList[ 4 ],\n\t\t\t\t\tsubNode.propertyList[ 5 ],\n\t\t\t\t\tsubNode.propertyList[ 6 ]\n\t\t\t\t];\n\n\t\t\t} else {\n\n\t\t\t\tinnerPropValue = subNode.propertyList[ 4 ];\n\n\t\t\t}\n\n\t\t\t// this will be copied to parent, see above\n\t\t\tnode[ innerPropName ] = {\n\n\t\t\t\t'type': innerPropType1,\n\t\t\t\t'type2': innerPropType2,\n\t\t\t\t'flag': innerPropFlag,\n\t\t\t\t'value': innerPropValue\n\n\t\t\t};\n\n\t\t} else if ( node[ subNode.name ] === undefined ) {\n\n\t\t\tif ( typeof subNode.id === 'number' ) {\n\n\t\t\t\tnode[ subNode.name ] = {};\n\t\t\t\tnode[ subNode.name ][ subNode.id ] = subNode;\n\n\t\t\t} else {\n\n\t\t\t\tnode[ subNode.name ] = subNode;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tif ( subNode.name === 'PoseNode' ) {\n\n\t\t\t\tif ( ! Array.isArray( node[ subNode.name ] ) ) {\n\n\t\t\t\t\tnode[ subNode.name ] = [ node[ subNode.name ] ];\n\n\t\t\t\t}\n\n\t\t\t\tnode[ subNode.name ].push( subNode );\n\n\t\t\t} else if ( node[ subNode.name ][ subNode.id ] === undefined ) {\n\n\t\t\t\tnode[ subNode.name ][ subNode.id ] = subNode;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tparseProperty( reader ) {\n\n\t\tconst type = reader.getString( 1 );\n\t\tlet length;\n\n\t\tswitch ( type ) {\n\n\t\t\tcase 'C':\n\t\t\t\treturn reader.getBoolean();\n\n\t\t\tcase 'D':\n\t\t\t\treturn reader.getFloat64();\n\n\t\t\tcase 'F':\n\t\t\t\treturn reader.getFloat32();\n\n\t\t\tcase 'I':\n\t\t\t\treturn reader.getInt32();\n\n\t\t\tcase 'L':\n\t\t\t\treturn reader.getInt64();\n\n\t\t\tcase 'R':\n\t\t\t\tlength = reader.getUint32();\n\t\t\t\treturn reader.getArrayBuffer( length );\n\n\t\t\tcase 'S':\n\t\t\t\tlength = reader.getUint32();\n\t\t\t\treturn reader.getString( length );\n\n\t\t\tcase 'Y':\n\t\t\t\treturn reader.getInt16();\n\n\t\t\tcase 'b':\n\t\t\tcase 'c':\n\t\t\tcase 'd':\n\t\t\tcase 'f':\n\t\t\tcase 'i':\n\t\t\tcase 'l':\n\n\t\t\t\tconst arrayLength = reader.getUint32();\n\t\t\t\tconst encoding = reader.getUint32(); // 0: non-compressed, 1: compressed\n\t\t\t\tconst compressedLength = reader.getUint32();\n\n\t\t\t\tif ( encoding === 0 ) {\n\n\t\t\t\t\tswitch ( type ) {\n\n\t\t\t\t\t\tcase 'b':\n\t\t\t\t\t\tcase 'c':\n\t\t\t\t\t\t\treturn reader.getBooleanArray( arrayLength );\n\n\t\t\t\t\t\tcase 'd':\n\t\t\t\t\t\t\treturn reader.getFloat64Array( arrayLength );\n\n\t\t\t\t\t\tcase 'f':\n\t\t\t\t\t\t\treturn reader.getFloat32Array( arrayLength );\n\n\t\t\t\t\t\tcase 'i':\n\t\t\t\t\t\t\treturn reader.getInt32Array( arrayLength );\n\n\t\t\t\t\t\tcase 'l':\n\t\t\t\t\t\t\treturn reader.getInt64Array( arrayLength );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tconst data = fflate.unzlibSync( new Uint8Array( reader.getArrayBuffer( compressedLength ) ) );\n\t\t\t\tconst reader2 = new BinaryReader( data.buffer );\n\n\t\t\t\tswitch ( type ) {\n\n\t\t\t\t\tcase 'b':\n\t\t\t\t\tcase 'c':\n\t\t\t\t\t\treturn reader2.getBooleanArray( arrayLength );\n\n\t\t\t\t\tcase 'd':\n\t\t\t\t\t\treturn reader2.getFloat64Array( arrayLength );\n\n\t\t\t\t\tcase 'f':\n\t\t\t\t\t\treturn reader2.getFloat32Array( arrayLength );\n\n\t\t\t\t\tcase 'i':\n\t\t\t\t\t\treturn reader2.getInt32Array( arrayLength );\n\n\t\t\t\t\tcase 'l':\n\t\t\t\t\t\treturn reader2.getInt64Array( arrayLength );\n\n\t\t\t\t}\n\n\t\t\t\tbreak; // cannot happen but is required by the DeepScan\n\n\t\t\tdefault:\n\t\t\t\tthrow new Error( 'THREE.FBXLoader: Unknown property type ' + type );\n\n\t\t}\n\n\t}\n\n}\n\nclass BinaryReader {\n\n\tconstructor( buffer, littleEndian ) {\n\n\t\tthis.dv = new DataView( buffer );\n\t\tthis.offset = 0;\n\t\tthis.littleEndian = ( littleEndian !== undefined ) ? littleEndian : true;\n\t\tthis._textDecoder = new TextDecoder();\n\n\t}\n\n\tgetOffset() {\n\n\t\treturn this.offset;\n\n\t}\n\n\tsize() {\n\n\t\treturn this.dv.buffer.byteLength;\n\n\t}\n\n\tskip( length ) {\n\n\t\tthis.offset += length;\n\n\t}\n\n\t// seems like true/false representation depends on exporter.\n\t// true: 1 or 'Y'(=0x59), false: 0 or 'T'(=0x54)\n\t// then sees LSB.\n\tgetBoolean() {\n\n\t\treturn ( this.getUint8() & 1 ) === 1;\n\n\t}\n\n\tgetBooleanArray( size ) {\n\n\t\tconst a = [];\n\n\t\tfor ( let i = 0; i < size; i ++ ) {\n\n\t\t\ta.push( this.getBoolean() );\n\n\t\t}\n\n\t\treturn a;\n\n\t}\n\n\tgetUint8() {\n\n\t\tconst value = this.dv.getUint8( this.offset );\n\t\tthis.offset += 1;\n\t\treturn value;\n\n\t}\n\n\tgetInt16() {\n\n\t\tconst value = this.dv.getInt16( this.offset, this.littleEndian );\n\t\tthis.offset += 2;\n\t\treturn value;\n\n\t}\n\n\tgetInt32() {\n\n\t\tconst value = this.dv.getInt32( this.offset, this.littleEndian );\n\t\tthis.offset += 4;\n\t\treturn value;\n\n\t}\n\n\tgetInt32Array( size ) {\n\n\t\tconst a = [];\n\n\t\tfor ( let i = 0; i < size; i ++ ) {\n\n\t\t\ta.push( this.getInt32() );\n\n\t\t}\n\n\t\treturn a;\n\n\t}\n\n\tgetUint32() {\n\n\t\tconst value = this.dv.getUint32( this.offset, this.littleEndian );\n\t\tthis.offset += 4;\n\t\treturn value;\n\n\t}\n\n\t// JavaScript doesn't support 64-bit integer so calculate this here\n\t// 1 << 32 will return 1 so using multiply operation instead here.\n\t// There's a possibility that this method returns wrong value if the value\n\t// is out of the range between Number.MAX_SAFE_INTEGER and Number.MIN_SAFE_INTEGER.\n\t// TODO: safely handle 64-bit integer\n\tgetInt64() {\n\n\t\tlet low, high;\n\n\t\tif ( this.littleEndian ) {\n\n\t\t\tlow = this.getUint32();\n\t\t\thigh = this.getUint32();\n\n\t\t} else {\n\n\t\t\thigh = this.getUint32();\n\t\t\tlow = this.getUint32();\n\n\t\t}\n\n\t\t// calculate negative value\n\t\tif ( high & 0x80000000 ) {\n\n\t\t\thigh = ~ high & 0xFFFFFFFF;\n\t\t\tlow = ~ low & 0xFFFFFFFF;\n\n\t\t\tif ( low === 0xFFFFFFFF ) high = ( high + 1 ) & 0xFFFFFFFF;\n\n\t\t\tlow = ( low + 1 ) & 0xFFFFFFFF;\n\n\t\t\treturn - ( high * 0x100000000 + low );\n\n\t\t}\n\n\t\treturn high * 0x100000000 + low;\n\n\t}\n\n\tgetInt64Array( size ) {\n\n\t\tconst a = [];\n\n\t\tfor ( let i = 0; i < size; i ++ ) {\n\n\t\t\ta.push( this.getInt64() );\n\n\t\t}\n\n\t\treturn a;\n\n\t}\n\n\t// Note: see getInt64() comment\n\tgetUint64() {\n\n\t\tlet low, high;\n\n\t\tif ( this.littleEndian ) {\n\n\t\t\tlow = this.getUint32();\n\t\t\thigh = this.getUint32();\n\n\t\t} else {\n\n\t\t\thigh = this.getUint32();\n\t\t\tlow = this.getUint32();\n\n\t\t}\n\n\t\treturn high * 0x100000000 + low;\n\n\t}\n\n\tgetFloat32() {\n\n\t\tconst value = this.dv.getFloat32( this.offset, this.littleEndian );\n\t\tthis.offset += 4;\n\t\treturn value;\n\n\t}\n\n\tgetFloat32Array( size ) {\n\n\t\tconst a = [];\n\n\t\tfor ( let i = 0; i < size; i ++ ) {\n\n\t\t\ta.push( this.getFloat32() );\n\n\t\t}\n\n\t\treturn a;\n\n\t}\n\n\tgetFloat64() {\n\n\t\tconst value = this.dv.getFloat64( this.offset, this.littleEndian );\n\t\tthis.offset += 8;\n\t\treturn value;\n\n\t}\n\n\tgetFloat64Array( size ) {\n\n\t\tconst a = [];\n\n\t\tfor ( let i = 0; i < size; i ++ ) {\n\n\t\t\ta.push( this.getFloat64() );\n\n\t\t}\n\n\t\treturn a;\n\n\t}\n\n\tgetArrayBuffer( size ) {\n\n\t\tconst value = this.dv.buffer.slice( this.offset, this.offset + size );\n\t\tthis.offset += size;\n\t\treturn value;\n\n\t}\n\n\tgetString( size ) {\n\n\t\tconst start = this.offset;\n\t\tlet a = new Uint8Array( this.dv.buffer, start, size );\n\n\t\tthis.skip( size );\n\n\t\tconst nullByte = a.indexOf( 0 );\n\t\tif ( nullByte >= 0 ) a = new Uint8Array( this.dv.buffer, start, nullByte );\n\n\t\treturn this._textDecoder.decode( a );\n\n\t}\n\n}\n\n// FBXTree holds a representation of the FBX data, returned by the TextParser ( FBX ASCII format)\n// and BinaryParser( FBX Binary format)\nclass FBXTree {\n\n\tadd( key, val ) {\n\n\t\tthis[ key ] = val;\n\n\t}\n\n}\n\n// ************** UTILITY FUNCTIONS **************\n\nfunction isFbxFormatBinary( buffer ) {\n\n\tconst CORRECT = 'Kaydara\\u0020FBX\\u0020Binary\\u0020\\u0020\\0';\n\n\treturn buffer.byteLength >= CORRECT.length && CORRECT === convertArrayBufferToString( buffer, 0, CORRECT.length );\n\n}\n\nfunction isFbxFormatASCII( text ) {\n\n\tconst CORRECT = [ 'K', 'a', 'y', 'd', 'a', 'r', 'a', '\\\\', 'F', 'B', 'X', '\\\\', 'B', 'i', 'n', 'a', 'r', 'y', '\\\\', '\\\\' ];\n\n\tlet cursor = 0;\n\n\tfunction read( offset ) {\n\n\t\tconst result = text[ offset - 1 ];\n\t\ttext = text.slice( cursor + offset );\n\t\tcursor ++;\n\t\treturn result;\n\n\t}\n\n\tfor ( let i = 0; i < CORRECT.length; ++ i ) {\n\n\t\tconst num = read( 1 );\n\t\tif ( num === CORRECT[ i ] ) {\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\treturn true;\n\n}\n\nfunction getFbxVersion( text ) {\n\n\tconst versionRegExp = /FBXVersion: (\\d+)/;\n\tconst match = text.match( versionRegExp );\n\n\tif ( match ) {\n\n\t\tconst version = parseInt( match[ 1 ] );\n\t\treturn version;\n\n\t}\n\n\tthrow new Error( 'THREE.FBXLoader: Cannot find the version number for the file given.' );\n\n}\n\n// Converts FBX ticks into real time seconds.\nfunction convertFBXTimeToSeconds( time ) {\n\n\treturn time / 46186158000;\n\n}\n\nconst dataArray = [];\n\n// extracts the data from the correct position in the FBX array based on indexing type\nfunction getData( polygonVertexIndex, polygonIndex, vertexIndex, infoObject ) {\n\n\tlet index;\n\n\tswitch ( infoObject.mappingType ) {\n\n\t\tcase 'ByPolygonVertex' :\n\t\t\tindex = polygonVertexIndex;\n\t\t\tbreak;\n\t\tcase 'ByPolygon' :\n\t\t\tindex = polygonIndex;\n\t\t\tbreak;\n\t\tcase 'ByVertice' :\n\t\t\tindex = vertexIndex;\n\t\t\tbreak;\n\t\tcase 'AllSame' :\n\t\t\tindex = infoObject.indices[ 0 ];\n\t\t\tbreak;\n\t\tdefault :\n\t\t\tconsole.warn( 'THREE.FBXLoader: unknown attribute mapping type ' + infoObject.mappingType );\n\n\t}\n\n\tif ( infoObject.referenceType === 'IndexToDirect' ) index = infoObject.indices[ index ];\n\n\tconst from = index * infoObject.dataSize;\n\tconst to = from + infoObject.dataSize;\n\n\treturn slice( dataArray, infoObject.buffer, from, to );\n\n}\n\nconst tempEuler = new Euler();\nconst tempVec = new Vector3();\n\n// generate transformation from FBX transform data\n// ref: https://help.autodesk.com/view/FBX/2017/ENU/?guid=__files_GUID_10CDD63C_79C1_4F2D_BB28_AD2BE65A02ED_htm\n// ref: http://docs.autodesk.com/FBX/2014/ENU/FBX-SDK-Documentation/index.html?url=cpp_ref/_transformations_2main_8cxx-example.html,topicNumber=cpp_ref__transformations_2main_8cxx_example_htmlfc10a1e1-b18d-4e72-9dc0-70d0f1959f5e\nfunction generateTransform( transformData ) {\n\n\tconst lTranslationM = new Matrix4();\n\tconst lPreRotationM = new Matrix4();\n\tconst lRotationM = new Matrix4();\n\tconst lPostRotationM = new Matrix4();\n\n\tconst lScalingM = new Matrix4();\n\tconst lScalingPivotM = new Matrix4();\n\tconst lScalingOffsetM = new Matrix4();\n\tconst lRotationOffsetM = new Matrix4();\n\tconst lRotationPivotM = new Matrix4();\n\n\tconst lParentGX = new Matrix4();\n\tconst lParentLX = new Matrix4();\n\tconst lGlobalT = new Matrix4();\n\n\tconst inheritType = ( transformData.inheritType ) ? transformData.inheritType : 0;\n\n\tif ( transformData.translation ) lTranslationM.setPosition( tempVec.fromArray( transformData.translation ) );\n\n\tif ( transformData.preRotation ) {\n\n\t\tconst array = transformData.preRotation.map( MathUtils.degToRad );\n\t\tarray.push( transformData.eulerOrder || Euler.DEFAULT_ORDER );\n\t\tlPreRotationM.makeRotationFromEuler( tempEuler.fromArray( array ) );\n\n\t}\n\n\tif ( transformData.rotation ) {\n\n\t\tconst array = transformData.rotation.map( MathUtils.degToRad );\n\t\tarray.push( transformData.eulerOrder || Euler.DEFAULT_ORDER );\n\t\tlRotationM.makeRotationFromEuler( tempEuler.fromArray( array ) );\n\n\t}\n\n\tif ( transformData.postRotation ) {\n\n\t\tconst array = transformData.postRotation.map( MathUtils.degToRad );\n\t\tarray.push( transformData.eulerOrder || Euler.DEFAULT_ORDER );\n\t\tlPostRotationM.makeRotationFromEuler( tempEuler.fromArray( array ) );\n\t\tlPostRotationM.invert();\n\n\t}\n\n\tif ( transformData.scale ) lScalingM.scale( tempVec.fromArray( transformData.scale ) );\n\n\t// Pivots and offsets\n\tif ( transformData.scalingOffset ) lScalingOffsetM.setPosition( tempVec.fromArray( transformData.scalingOffset ) );\n\tif ( transformData.scalingPivot ) lScalingPivotM.setPosition( tempVec.fromArray( transformData.scalingPivot ) );\n\tif ( transformData.rotationOffset ) lRotationOffsetM.setPosition( tempVec.fromArray( transformData.rotationOffset ) );\n\tif ( transformData.rotationPivot ) lRotationPivotM.setPosition( tempVec.fromArray( transformData.rotationPivot ) );\n\n\t// parent transform\n\tif ( transformData.parentMatrixWorld ) {\n\n\t\tlParentLX.copy( transformData.parentMatrix );\n\t\tlParentGX.copy( transformData.parentMatrixWorld );\n\n\t}\n\n\tconst lLRM = lPreRotationM.clone().multiply( lRotationM ).multiply( lPostRotationM );\n\t// Global Rotation\n\tconst lParentGRM = new Matrix4();\n\tlParentGRM.extractRotation( lParentGX );\n\n\t// Global Shear*Scaling\n\tconst lParentTM = new Matrix4();\n\tlParentTM.copyPosition( lParentGX );\n\n\tconst lParentGRSM = lParentTM.clone().invert().multiply( lParentGX );\n\tconst lParentGSM = lParentGRM.clone().invert().multiply( lParentGRSM );\n\tconst lLSM = lScalingM;\n\n\tconst lGlobalRS = new Matrix4();\n\n\tif ( inheritType === 0 ) {\n\n\t\tlGlobalRS.copy( lParentGRM ).multiply( lLRM ).multiply( lParentGSM ).multiply( lLSM );\n\n\t} else if ( inheritType === 1 ) {\n\n\t\tlGlobalRS.copy( lParentGRM ).multiply( lParentGSM ).multiply( lLRM ).multiply( lLSM );\n\n\t} else {\n\n\t\tconst lParentLSM = new Matrix4().scale( new Vector3().setFromMatrixScale( lParentLX ) );\n\t\tconst lParentLSM_inv = lParentLSM.clone().invert();\n\t\tconst lParentGSM_noLocal = lParentGSM.clone().multiply( lParentLSM_inv );\n\n\t\tlGlobalRS.copy( lParentGRM ).multiply( lLRM ).multiply( lParentGSM_noLocal ).multiply( lLSM );\n\n\t}\n\n\tconst lRotationPivotM_inv = lRotationPivotM.clone().invert();\n\tconst lScalingPivotM_inv = lScalingPivotM.clone().invert();\n\t// Calculate the local transform matrix\n\tlet lTransform = lTranslationM.clone().multiply( lRotationOffsetM ).multiply( lRotationPivotM ).multiply( lPreRotationM ).multiply( lRotationM ).multiply( lPostRotationM ).multiply( lRotationPivotM_inv ).multiply( lScalingOffsetM ).multiply( lScalingPivotM ).multiply( lScalingM ).multiply( lScalingPivotM_inv );\n\n\tconst lLocalTWithAllPivotAndOffsetInfo = new Matrix4().copyPosition( lTransform );\n\n\tconst lGlobalTranslation = lParentGX.clone().multiply( lLocalTWithAllPivotAndOffsetInfo );\n\tlGlobalT.copyPosition( lGlobalTranslation );\n\n\tlTransform = lGlobalT.clone().multiply( lGlobalRS );\n\n\t// from global to local\n\tlTransform.premultiply( lParentGX.invert() );\n\n\treturn lTransform;\n\n}\n\n// Returns the three.js intrinsic Euler order corresponding to FBX extrinsic Euler order\n// ref: http://help.autodesk.com/view/FBX/2017/ENU/?guid=__cpp_ref_class_fbx_euler_html\nfunction getEulerOrder( order ) {\n\n\torder = order || 0;\n\n\tconst enums = [\n\t\t'ZYX', // -> XYZ extrinsic\n\t\t'YZX', // -> XZY extrinsic\n\t\t'XZY', // -> YZX extrinsic\n\t\t'ZXY', // -> YXZ extrinsic\n\t\t'YXZ', // -> ZXY extrinsic\n\t\t'XYZ', // -> ZYX extrinsic\n\t\t//'SphericXYZ', // not possible to support\n\t];\n\n\tif ( order === 6 ) {\n\n\t\tconsole.warn( 'THREE.FBXLoader: unsupported Euler Order: Spherical XYZ. Animations and rotations may be incorrect.' );\n\t\treturn enums[ 0 ];\n\n\t}\n\n\treturn enums[ order ];\n\n}\n\n// Parses comma separated list of numbers and returns them an array.\n// Used internally by the TextParser\nfunction parseNumberArray( value ) {\n\n\tconst array = value.split( ',' ).map( function ( val ) {\n\n\t\treturn parseFloat( val );\n\n\t} );\n\n\treturn array;\n\n}\n\nfunction convertArrayBufferToString( buffer, from, to ) {\n\n\tif ( from === undefined ) from = 0;\n\tif ( to === undefined ) to = buffer.byteLength;\n\n\treturn new TextDecoder().decode( new Uint8Array( buffer, from, to ) );\n\n}\n\nfunction append( a, b ) {\n\n\tfor ( let i = 0, j = a.length, l = b.length; i < l; i ++, j ++ ) {\n\n\t\ta[ j ] = b[ i ];\n\n\t}\n\n}\n\nfunction slice( a, b, from, to ) {\n\n\tfor ( let i = from, j = 0; i < to; i ++, j ++ ) {\n\n\t\ta[ j ] = b[ i ];\n\n\t}\n\n\treturn a;\n\n}\n\n\nexport { FBXLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,SAAU,GAAG;AAAE,SAAO,IAAI,gBAAgB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,MAAM,kBAAkB,CAAC,CAAC;AAAG;AAClG,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,IAAI,OAAO,CAAC;AAAG;AAC/C,IAAI;AACA,MAAI,gBAAgB,KAAK,EAAE,CAAC;AAChC,SACO,GAAG;AAEN,SAAO,SAAU,GAAG;AAAE,WAAO,+CAA+C,UAAU,CAAC;AAAA,EAAG;AAE1F,QAAM,SAAU,GAAG;AAAE,WAAO,IAAI,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC;AAAA,EAAG;AACnE;AACA,IAAI,KAAM,SAAU,GAAG,IAAI,KAAK,UAAU,IAAI;AAC1C,MAAI,IAAI,IAAI,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC,EAAE;AAC1C,IAAE,UAAU,SAAU,GAAG;AAAE,WAAO,GAAG,EAAE,OAAO,IAAI;AAAA,EAAG;AACrD,IAAE,YAAY,SAAU,GAAG;AAAE,WAAO,GAAG,MAAM,EAAE,IAAI;AAAA,EAAG;AACtD,IAAE,YAAY,KAAK,QAAQ;AAC3B,SAAO;AACX;AAGA,IAAI,KAAK;AAAT,IAAqB,MAAM;AAA3B,IAAwC,MAAM;AAE9C,IAAI,OAAO,IAAI,GAAG;AAAA,EAAC;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA;AAAA,EAAgB;AAAA,EAAG;AAAA;AAAA,EAAoB;AAAC,CAAC;AAGhJ,IAAI,OAAO,IAAI,GAAG;AAAA,EAAC;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA;AAAA,EAAiB;AAAA,EAAG;AAAC,CAAC;AAEvI,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpF,IAAI,OAAO,SAAU,IAAI,OAAO;AAC5B,MAAI,IAAI,IAAI,IAAI,EAAE;AAClB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,MAAE,CAAC,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC;AAAA,EACjC;AAEA,MAAI,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,aAAS,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;AAClC,QAAE,CAAC,IAAM,IAAI,EAAE,CAAC,KAAM,IAAK;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,CAAC;AAChB;AACA,IAAI,KAAK,KAAK,MAAM,CAAC;AAArB,IAAwB,KAAK,GAAG,CAAC;AAAjC,IAAoC,QAAQ,GAAG,CAAC;AAEhD,GAAG,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;AAC3B,IAAI,KAAK,KAAK,MAAM,CAAC;AAArB,IAAwB,KAAK,GAAG,CAAC;AAAjC,IAAoC,QAAQ,GAAG,CAAC;AAEhD,IAAI,MAAM,IAAI,IAAI,KAAK;AACvB,KAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAExB,OAAM,IAAI,WAAY,KAAO,IAAI,UAAW;AAChD,OAAM,IAAI,WAAY,KAAO,IAAI,UAAW;AAC5C,OAAM,IAAI,WAAY,KAAO,IAAI,SAAW;AAC5C,MAAI,CAAC,MAAO,IAAI,WAAY,KAAO,IAAI,QAAW,OAAQ;AAC9D;AAJQ;AAFC;AAUT,IAAI,OAAQ,SAAU,IAAI,IAAI,GAAG;AAC7B,MAAI,IAAI,GAAG;AAEX,MAAI,IAAI;AAER,MAAI,IAAI,IAAI,IAAI,EAAE;AAElB,SAAO,IAAI,GAAG,EAAE;AACZ,MAAE,EAAE,GAAG,CAAC,IAAI,CAAC;AAEjB,MAAI,KAAK,IAAI,IAAI,EAAE;AACnB,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,OAAG,CAAC,IAAK,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAM;AAAA,EACtC;AACA,MAAI;AACJ,MAAI,GAAG;AAEH,SAAK,IAAI,IAAI,KAAK,EAAE;AAEpB,QAAI,MAAM,KAAK;AACf,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAEpB,UAAI,GAAG,CAAC,GAAG;AAEP,YAAI,KAAM,KAAK,IAAK,GAAG,CAAC;AAExB,YAAI,MAAM,KAAK,GAAG,CAAC;AAEnB,YAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO;AAE3B,iBAAS,IAAI,KAAM,KAAK,OAAO,GAAI,KAAK,GAAG,EAAE,GAAG;AAE5C,aAAG,IAAI,CAAC,MAAM,GAAG,IAAI;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,OACK;AACD,SAAK,IAAI,IAAI,CAAC;AACd,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpB,UAAI,GAAG,CAAC,GAAG;AACP,WAAG,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,MAAO,KAAK,GAAG,CAAC;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAI,MAAM,IAAI,GAAG,GAAG;AACpB,KAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACvB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAGT,IAAI,MAAM,IAAI,GAAG,EAAE;AACnB,KAAS,IAAI,GAAG,IAAI,IAAI,EAAE;AACtB,MAAI,CAAC,IAAI;AADJ;AAGT,IAAI,MAAoB,KAAK,KAAK,GAAG,CAAC;AAAtC,IAAyC,OAAqB,KAAK,KAAK,GAAG,CAAC;AAE5E,IAAI,MAAoB,KAAK,KAAK,GAAG,CAAC;AAAtC,IAAyC,OAAqB,KAAK,KAAK,GAAG,CAAC;AAE5E,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,EAAE,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC/B,QAAI,EAAE,CAAC,IAAI;AACP,UAAI,EAAE,CAAC;AAAA,EACf;AACA,SAAO;AACX;AAEA,IAAI,OAAO,SAAU,GAAG,GAAG,GAAG;AAC1B,MAAI,IAAK,IAAI,IAAK;AAClB,UAAS,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,OAAQ,IAAI,KAAM;AACnD;AAEA,IAAI,SAAS,SAAU,GAAG,GAAG;AACzB,MAAI,IAAK,IAAI,IAAK;AAClB,UAAS,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,IAAM,EAAE,IAAI,CAAC,KAAK,QAAS,IAAI;AAChE;AAEA,IAAI,OAAO,SAAU,GAAG;AAAE,UAAS,IAAI,IAAK,MAAM,IAAI,KAAK;AAAI;AAG/D,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG;AACzB,MAAI,KAAK,QAAQ,IAAI;AACjB,QAAI;AACR,MAAI,KAAK,QAAQ,IAAI,EAAE;AACnB,QAAI,EAAE;AAEV,MAAI,IAAI,KAAK,aAAa,MAAM,MAAM,aAAa,MAAM,MAAM,IAAI,IAAI,CAAC;AACxE,IAAE,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC;AACtB,SAAO;AACX;AAEA,IAAI,QAAQ,SAAU,KAAK,KAAK,IAAI;AAEhC,MAAI,KAAK,IAAI;AACb,MAAI,CAAC,MAAO,MAAM,CAAC,GAAG,KAAK,KAAK;AAC5B,WAAO,OAAO,IAAI,GAAG,CAAC;AAE1B,MAAI,QAAQ,CAAC,OAAO;AAEpB,MAAI,OAAO,CAAC,MAAM,GAAG;AACrB,MAAI,CAAC;AACD,SAAK,CAAC;AAEV,MAAI,CAAC;AACD,UAAM,IAAI,GAAG,KAAK,CAAC;AAEvB,MAAI,OAAO,SAAUA,IAAG;AACpB,QAAI,KAAK,IAAI;AAEb,QAAIA,KAAI,IAAI;AAER,UAAI,OAAO,IAAI,GAAG,KAAK,IAAI,KAAK,GAAGA,EAAC,CAAC;AACrC,WAAK,IAAI,GAAG;AACZ,YAAM;AAAA,IACV;AAAA,EACJ;AAEA,MAAI,QAAQ,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;AAEnG,MAAI,OAAO,KAAK;AAChB,KAAG;AACC,QAAI,CAAC,IAAI;AAEL,SAAG,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC;AAE/B,UAAI,OAAO,KAAK,KAAK,MAAM,GAAG,CAAC;AAC/B,aAAO;AACP,UAAI,CAAC,MAAM;AAEP,YAAI,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAK,IAAI,IAAI,CAAC,KAAK,GAAI,IAAI,IAAI;AACnE,YAAI,IAAI,IAAI;AACR,cAAI;AACA,kBAAM;AACV;AAAA,QACJ;AAEA,YAAI;AACA,eAAK,KAAK,CAAC;AAEf,YAAI,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE;AAE9B,WAAG,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,IAAI;AACjC;AAAA,MACJ,WACS,QAAQ;AACb,aAAK,MAAM,KAAK,MAAM,MAAM,GAAG,MAAM;AAAA,eAChC,QAAQ,GAAG;AAEhB,YAAI,OAAO,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,EAAE,IAAI;AACvE,YAAI,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI;AACzC,eAAO;AAEP,YAAI,MAAM,IAAI,GAAG,EAAE;AAEnB,YAAI,MAAM,IAAI,GAAG,EAAE;AACnB,iBAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAE5B,cAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,MAAM,IAAI,GAAG,CAAC;AAAA,QAC3C;AACA,eAAO,QAAQ;AAEf,YAAI,MAAM,IAAI,GAAG,GAAG,UAAU,KAAK,OAAO;AAE1C,YAAI,MAAM,KAAK,KAAK,KAAK,CAAC;AAC1B,iBAAS,IAAI,GAAG,IAAI,MAAK;AACrB,cAAI,IAAI,IAAI,KAAK,KAAK,KAAK,MAAM,CAAC;AAElC,iBAAO,IAAI;AAEX,cAAI,IAAI,MAAM;AAEd,cAAI,IAAI,IAAI;AACR,gBAAI,GAAG,IAAI;AAAA,UACf,OACK;AAED,gBAAI,IAAI,GAAG,IAAI;AACf,gBAAI,KAAK;AACL,kBAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,qBAC7C,KAAK;AACV,kBAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO;AAAA,qBAC7B,KAAK;AACV,kBAAI,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,OAAO;AACzC,mBAAO;AACH,kBAAI,GAAG,IAAI;AAAA,UACnB;AAAA,QACJ;AAEA,YAAI,KAAK,IAAI,SAAS,GAAG,IAAI,GAAG,KAAK,IAAI,SAAS,IAAI;AAEtD,cAAM,IAAI,EAAE;AAEZ,cAAM,IAAI,EAAE;AACZ,aAAK,KAAK,IAAI,KAAK,CAAC;AACpB,aAAK,KAAK,IAAI,KAAK,CAAC;AAAA,MACxB;AAEI,cAAM;AACV,UAAI,MAAM,MAAM;AACZ,YAAI;AACA,gBAAM;AACV;AAAA,MACJ;AAAA,IACJ;AAGA,QAAI;AACA,WAAK,KAAK,MAAM;AACpB,QAAI,OAAO,KAAK,OAAO,GAAG,OAAO,KAAK,OAAO;AAC7C,QAAI,OAAO;AACX,aAAQ,OAAO,KAAK;AAEhB,UAAI,IAAI,GAAG,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,MAAM,MAAM;AAChD,aAAO,IAAI;AACX,UAAI,MAAM,MAAM;AACZ,YAAI;AACA,gBAAM;AACV;AAAA,MACJ;AACA,UAAI,CAAC;AACD,cAAM;AACV,UAAI,MAAM;AACN,YAAI,IAAI,IAAI;AAAA,eACP,OAAO,KAAK;AACjB,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ,OACK;AACD,YAAI,MAAM,MAAM;AAEhB,YAAI,MAAM,KAAK;AAEX,cAAI,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC;AAC7B,gBAAM,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC;AACzC,iBAAO;AAAA,QACX;AAEA,YAAI,IAAI,GAAG,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,OAAO,MAAM;AACjD,YAAI,CAAC;AACD,gBAAM;AACV,eAAO,IAAI;AACX,YAAI,KAAK,GAAG,IAAI;AAChB,YAAI,OAAO,GAAG;AACV,cAAI,IAAI,KAAK,IAAI;AACjB,gBAAM,OAAO,KAAK,GAAG,KAAM,KAAK,KAAK,GAAI,OAAO;AAAA,QACpD;AACA,YAAI,MAAM,MAAM;AACZ,cAAI;AACA,kBAAM;AACV;AAAA,QACJ;AACA,YAAI;AACA,eAAK,KAAK,MAAM;AACpB,YAAI,MAAM,KAAK;AACf,eAAO,KAAK,KAAK,MAAM,GAAG;AACtB,cAAI,EAAE,IAAI,IAAI,KAAK,EAAE;AACrB,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAC7B,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAC7B,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,QACjC;AACA,aAAK;AAAA,MACT;AAAA,IACJ;AACA,OAAG,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI;AAC/B,QAAI;AACA,cAAQ,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,IAAI;AAAA,EACjD,SAAS,CAAC;AACV,SAAO,MAAM,IAAI,SAAS,MAAM,IAAI,KAAK,GAAG,EAAE;AAClD;AAEA,IAAI,QAAQ,SAAU,GAAG,GAAG,GAAG;AAC3B,QAAM,IAAI;AACV,MAAI,IAAK,IAAI,IAAK;AAClB,IAAE,CAAC,KAAK;AACR,IAAE,IAAI,CAAC,KAAK,MAAM;AACtB;AAEA,IAAI,UAAU,SAAU,GAAG,GAAG,GAAG;AAC7B,QAAM,IAAI;AACV,MAAI,IAAK,IAAI,IAAK;AAClB,IAAE,CAAC,KAAK;AACR,IAAE,IAAI,CAAC,KAAK,MAAM;AAClB,IAAE,IAAI,CAAC,KAAK,MAAM;AACtB;AAEA,IAAI,QAAQ,SAAU,GAAG,IAAI;AAEzB,MAAI,IAAI,CAAC;AACT,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC/B,QAAI,EAAE,CAAC;AACH,QAAE,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AAAA,EAChC;AACA,MAAI,IAAI,EAAE;AACV,MAAI,KAAK,EAAE,MAAM;AACjB,MAAI,CAAC;AACD,WAAO,CAAC,IAAI,CAAC;AACjB,MAAI,KAAK,GAAG;AACR,QAAI,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;AACzB,MAAE,EAAE,CAAC,EAAE,CAAC,IAAI;AACZ,WAAO,CAAC,GAAG,CAAC;AAAA,EAChB;AACA,IAAE,KAAK,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,IAAI,EAAE;AAAA,EAAG,CAAC;AAG5C,IAAE,KAAK,EAAE,GAAG,IAAI,GAAG,MAAM,CAAC;AAC1B,MAAI,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AAC7C,IAAE,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG,GAAM,EAAK;AAMzC,SAAO,MAAM,IAAI,GAAG;AAChB,QAAI,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAO,IAAI;AACrC,QAAI,EAAE,MAAM,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAO,IAAI;AACjD,MAAE,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG,GAAM,EAAK;AAAA,EAChD;AACA,MAAI,SAAS,GAAG,CAAC,EAAE;AACnB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,GAAG,CAAC,EAAE,IAAI;AACV,eAAS,GAAG,CAAC,EAAE;AAAA,EACvB;AAEA,MAAI,KAAK,IAAI,IAAI,SAAS,CAAC;AAE3B,MAAI,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7B,MAAI,MAAM,IAAI;AAIV,QAAI,IAAI,GAAG,KAAK;AAEhB,QAAI,MAAM,MAAM,IAAI,MAAM,KAAK;AAC/B,OAAG,KAAK,SAAU,GAAG,GAAG;AAAE,aAAO,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE;AAAA,IAAG,CAAC;AAClE,WAAO,IAAI,GAAG,EAAE,GAAG;AACf,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,IAAI,IAAI;AACf,cAAM,OAAO,KAAM,MAAM,GAAG,IAAI;AAChC,WAAG,IAAI,IAAI;AAAA,MACf;AAEI;AAAA,IACR;AACA,YAAQ;AACR,WAAO,KAAK,GAAG;AACX,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,IAAI;AACX,cAAM,KAAM,KAAK,GAAG,IAAI,MAAM;AAAA;AAE9B,UAAE;AAAA,IACV;AACA,WAAO,KAAK,KAAK,IAAI,EAAE,GAAG;AACtB,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,KAAK,IAAI;AAChB,UAAE,GAAG,IAAI;AACT,UAAE;AAAA,MACN;AAAA,IACJ;AACA,UAAM;AAAA,EACV;AACA,SAAO,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG;AAC3B;AAEA,IAAI,KAAK,SAAU,GAAG,GAAG,GAAG;AACxB,SAAO,EAAE,KAAK,KACR,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,IAC5C,EAAE,EAAE,CAAC,IAAI;AACpB;AAEA,IAAI,KAAK,SAAU,GAAG;AAClB,MAAI,IAAI,EAAE;AAEV,SAAO,KAAK,CAAC,EAAE,EAAE,CAAC;AACd;AACJ,MAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAEpB,MAAI,MAAM,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM;AAC/B,MAAI,IAAI,SAAU,GAAG;AAAE,OAAG,KAAK,IAAI;AAAA,EAAG;AACtC,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACzB,QAAI,EAAE,CAAC,KAAK,OAAO,KAAK;AACpB,QAAE;AAAA,SACD;AACD,UAAI,CAAC,OAAO,MAAM,GAAG;AACjB,eAAO,MAAM,KAAK,OAAO;AACrB,YAAE,KAAK;AACX,YAAI,MAAM,GAAG;AACT,YAAE,MAAM,KAAO,MAAM,MAAO,IAAK,QAAU,MAAM,KAAM,IAAK,KAAK;AACjE,gBAAM;AAAA,QACV;AAAA,MACJ,WACS,MAAM,GAAG;AACd,UAAE,GAAG,GAAG,EAAE;AACV,eAAO,MAAM,GAAG,OAAO;AACnB,YAAE,IAAI;AACV,YAAI,MAAM;AACN,YAAI,MAAM,KAAM,IAAK,IAAI,GAAG,MAAM;AAAA,MAC1C;AACA,aAAO;AACH,UAAE,GAAG;AACT,YAAM;AACN,YAAM,EAAE,CAAC;AAAA,IACb;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAClC;AAEA,IAAI,OAAO,SAAU,IAAI,IAAI;AACzB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,EAAE;AAC7B,SAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AAGA,IAAI,QAAQ,SAAU,KAAK,KAAK,KAAK;AAEjC,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,KAAK,MAAM,CAAC;AACpB,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,IAAI,CAAC,IAAI,MAAM;AACnB,MAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AACtB,MAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACrB,QAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAC1B,UAAQ,IAAI,IAAI,KAAK;AACzB;AAEA,IAAI,OAAO,SAAU,KAAK,KAAK,OAAO,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AACnE,QAAM,KAAK,KAAK,KAAK;AACrB,IAAE,GAAG,GAAG;AACR,MAAIC,MAAK,MAAM,IAAI,EAAE,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC/C,MAAIC,MAAK,MAAM,IAAI,EAAE,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC/C,MAAI,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAC1C,MAAI,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAC1C,MAAI,SAAS,IAAI,IAAI,EAAE;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAC/B,WAAO,KAAK,CAAC,IAAI,EAAE;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAC/B,WAAO,KAAK,CAAC,IAAI,EAAE;AACvB,MAAI,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC;AACnD,MAAI,OAAO;AACX,SAAO,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,GAAG,EAAE;AACvC;AACJ,MAAI,OAAQ,KAAK,KAAM;AACvB,MAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI;AAC5C,MAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,GAAG,KAAK,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE;AACrI,MAAI,QAAQ,SAAS,QAAQ;AACzB,WAAO,MAAM,KAAK,GAAG,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;AAClD,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,KAAK,GAAG,KAAK,QAAQ,MAAM,GAAG,KAAK;AACzC,MAAI,QAAQ,OAAO;AACf,SAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK;AAC/D,QAAI,MAAM,KAAK,KAAK,MAAM,CAAC;AAC3B,UAAM,KAAK,GAAG,MAAM,GAAG;AACvB,UAAM,KAAK,IAAI,GAAG,MAAM,CAAC;AACzB,UAAM,KAAK,IAAI,IAAI,OAAO,CAAC;AAC3B,SAAK;AACL,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE;AACxB,YAAM,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;AACtC,SAAK,IAAI;AACT,QAAI,OAAO,CAAC,MAAM,IAAI;AACtB,aAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC3B,UAAI,OAAO,KAAK,EAAE;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,MAAM,KAAK,CAAC,IAAI;AACpB,cAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;AACrC,YAAI,MAAM;AACN,gBAAM,KAAK,GAAI,KAAK,CAAC,MAAM,IAAK,GAAG,GAAG,KAAK,KAAK,CAAC,MAAM;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ,OACK;AACD,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,EACvC;AACA,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,QAAI,KAAK,CAAC,IAAI,KAAK;AACf,UAAI,MAAO,KAAK,CAAC,MAAM,KAAM;AAC7B,cAAQ,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG;AACjD,UAAI,MAAM;AACN,cAAM,KAAK,GAAI,KAAK,CAAC,MAAM,KAAM,EAAE,GAAG,KAAK,KAAK,GAAG;AACvD,UAAI,MAAM,KAAK,CAAC,IAAI;AACpB,cAAQ,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG;AACrC,UAAI,MAAM;AACN,gBAAQ,KAAK,GAAI,KAAK,CAAC,MAAM,IAAK,IAAI,GAAG,KAAK,KAAK,GAAG;AAAA,IAC9D,OACK;AACD,cAAQ,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAAA,IACjD;AAAA,EACJ;AACA,UAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AACvB,SAAO,IAAI,GAAG,GAAG;AACrB;AAEA,IAAI,MAAoB,IAAI,IAAI,CAAC,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,CAAC;AAE3G,IAAI,KAAmB,IAAI,GAAG,CAAC;AAE/B,IAAI,OAAO,SAAU,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK;AACjD,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAI,KAAK,IAAI;AAE7D,MAAI,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI;AACvC,MAAI,MAAM;AACV,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO;AAEhC,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,GAAG;AAEP,cAAM,MAAM,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,MAC1C,OACK;AAED,UAAE,CAAC,IAAI;AACP,cAAM,MAAM,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,MAAM,IAAI,MAAM,CAAC;AACrB,QAAI,IAAI,QAAQ,IAAI,IAAI,MAAM;AAC9B,QAAI,SAAS,KAAK,QAAQ;AAE1B,QAAI,OAAO,IAAI,IAAI,KAAK,GAAG,OAAO,IAAI,IAAI,QAAQ,CAAC;AACnD,QAAI,QAAQ,KAAK,KAAK,OAAO,CAAC,GAAG,QAAQ,IAAI;AAC7C,QAAI,MAAM,SAAUC,IAAG;AAAE,cAAQ,IAAIA,EAAC,IAAK,IAAIA,KAAI,CAAC,KAAK,QAAU,IAAIA,KAAI,CAAC,KAAK,SAAU;AAAA,IAAO;AAGlG,QAAI,OAAO,IAAI,IAAI,IAAK;AAExB,QAAI,KAAK,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,IAAI,EAAE;AAEtC,QAAI,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AAClD,WAAO,IAAI,GAAG,EAAE,GAAG;AAGf,UAAI,KAAK,IAAI,CAAC;AAEd,UAAI,OAAO,IAAI,OAAO,QAAQ,KAAK,EAAE;AACrC,WAAK,IAAI,IAAI;AACb,WAAK,EAAE,IAAI;AAGX,UAAI,MAAM,GAAG;AAET,YAAI,MAAM,IAAI;AACd,aAAK,OAAO,OAAQ,KAAK,UAAU,MAAM,KAAK;AAC1C,gBAAM,KAAK,KAAK,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAC3D,eAAK,OAAO,KAAK,GAAG,KAAK;AACzB,mBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACvB,eAAG,CAAC,IAAI;AACZ,mBAAS,IAAI,GAAG,IAAI,IAAI,EAAE;AACtB,eAAG,CAAC,IAAI;AAAA,QAChB;AAEA,YAAI,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAO,OAAO,QAAS;AACnD,YAAI,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,GAAG;AAC/B,cAAI,OAAO,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9B,cAAI,OAAO,KAAK,IAAI,OAAO,CAAC;AAG5B,cAAI,KAAK,KAAK,IAAI,KAAK,GAAG;AAC1B,iBAAO,OAAO,QAAQ,EAAE,QAAQ,QAAQ,OAAO;AAC3C,gBAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,GAAG,GAAG;AAChC,kBAAI,KAAK;AACT,qBAAO,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,EAAE;AAClD;AACJ,kBAAI,KAAK,GAAG;AACR,oBAAI,IAAI,IAAI;AAEZ,oBAAI,KAAK;AACL;AAIJ,oBAAI,MAAM,KAAK,IAAI,KAAK,KAAK,CAAC;AAC9B,oBAAI,KAAK;AACT,yBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,sBAAI,KAAM,IAAI,MAAM,IAAI,QAAS;AACjC,sBAAI,MAAM,KAAK,EAAE;AACjB,sBAAI,KAAM,KAAK,MAAM,QAAS;AAC9B,sBAAI,KAAK;AACL,yBAAK,IAAI,QAAQ;AAAA,gBACzB;AAAA,cACJ;AAAA,YACJ;AAEA,mBAAO,OAAO,QAAQ,KAAK,IAAI;AAC/B,mBAAQ,OAAO,QAAQ,QAAS;AAAA,UACpC;AAAA,QACJ;AAEA,YAAI,GAAG;AAGH,eAAK,IAAI,IAAI,YAAa,MAAM,CAAC,KAAK,KAAM,MAAM,CAAC;AACnD,cAAI,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,MAAM,CAAC,IAAI;AAC1C,gBAAM,KAAK,GAAG,IAAI,KAAK,GAAG;AAC1B,YAAE,GAAG,MAAM,GAAG;AACd,YAAE,GAAG,GAAG;AACR,eAAK,IAAI;AACT,YAAE;AAAA,QACN,OACK;AACD,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,YAAE,GAAG,IAAI,CAAC,CAAC;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAE7D,QAAI,CAAC,OAAO,MAAM;AACd,YAAM,MAAM,GAAG,MAAM,GAAG,EAAE;AAAA,EAClC;AACA,SAAO,IAAI,GAAG,GAAG,MAAM,KAAK,GAAG,IAAI,IAAI;AAC3C;AAEA,IAAI,OAAsB,WAAY;AAClC,MAAI,IAAI,IAAI,IAAI,GAAG;AACnB,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,QAAI,IAAI,GAAG,IAAI;AACf,WAAO,EAAE;AACL,WAAM,IAAI,KAAM,cAAe,MAAM;AACzC,MAAE,CAAC,IAAI;AAAA,EACX;AACA,SAAO;AACX,EAAG;AAEH,IAAI,MAAM,WAAY;AAClB,MAAI,IAAI;AACR,SAAO;AAAA,IACH,GAAG,SAAU,GAAG;AAEZ,UAAI,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE;AAC5B,aAAK,KAAM,KAAK,MAAO,EAAE,CAAC,CAAC,IAAK,OAAO;AAC3C,UAAI;AAAA,IACR;AAAA,IACA,GAAG,WAAY;AAAE,aAAO,CAAC;AAAA,IAAG;AAAA,EAChC;AACJ;AAEA,IAAI,QAAQ,WAAY;AACpB,MAAI,IAAI,GAAG,IAAI;AACf,SAAO;AAAA,IACH,GAAG,SAAU,GAAG;AAEZ,UAAI,IAAI,GAAG,IAAI;AACf,UAAI,IAAI,EAAE;AACV,eAAS,IAAI,GAAG,KAAK,KAAI;AACrB,YAAI,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC;AAC5B,eAAO,IAAI,GAAG,EAAE;AACZ,eAAK,KAAK,EAAE,CAAC;AACjB,aAAK,IAAI,SAAS,MAAM,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,KAAK;AAAA,MACnE;AACA,UAAI,GAAG,IAAI;AAAA,IACf;AAAA,IACA,GAAG,WAAY;AACX,WAAK,OAAO,KAAK;AACjB,cAAQ,IAAI,QAAQ,KAAM,MAAM,KAAM,MAAM,IAAI,QAAQ,IAAK,MAAM;AAAA,IACvE;AAAA,EACJ;AACJ;AAGA,IAAI,OAAO,SAAU,KAAK,KAAK,KAAK,MAAM,IAAI;AAC1C,SAAO,KAAK,KAAK,IAAI,SAAS,OAAO,IAAI,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,IAAI,GAAG,IAAK,KAAK,IAAI,KAAM,KAAK,MAAM,CAAC,EAAE;AAC3K;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,WAAS,KAAK;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,SAAO;AACX;AAQA,IAAI,OAAO,SAAU,IAAI,OAAOC,KAAI;AAChC,MAAI,KAAK,GAAG;AACZ,MAAI,KAAK,GAAG,SAAS;AACrB,MAAI,KAAK,GAAG,MAAM,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC,EAAE,QAAQ,MAAM,EAAE,EAAE,MAAM,GAAG;AACvF,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,EAAE,GAAG;AAChC,QAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AACvB,QAAI,OAAO,KAAK,YAAY;AACxB,eAAS,MAAM,IAAI;AACnB,UAAI,OAAO,EAAE,SAAS;AACtB,UAAI,EAAE,WAAW;AAEb,YAAI,KAAK,QAAQ,eAAe,KAAK,IAAI;AACrC,cAAI,QAAQ,KAAK,QAAQ,KAAK,CAAC,IAAI;AACnC,mBAAS,KAAK,MAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,CAAC;AAAA,QACvD,OACK;AACD,mBAAS;AACT,mBAAS,KAAK,EAAE;AACZ,qBAAS,MAAM,IAAI,gBAAgB,IAAI,MAAM,EAAE,UAAU,CAAC,EAAE,SAAS;AAAA,QAC7E;AAAA,MACJ;AAEI,iBAAS;AAAA,IACjB;AAEI,MAAAA,IAAG,CAAC,IAAI;AAAA,EAChB;AACA,SAAO,CAAC,OAAOA,GAAE;AACrB;AACA,IAAI,KAAK,CAAC;AAEV,IAAI,OAAO,SAAU,GAAG;AACpB,MAAI,KAAK,CAAC;AACV,WAAS,KAAK,GAAG;AACb,QAAI,EAAE,CAAC,aAAa,MAAM,EAAE,CAAC,aAAa,OAAO,EAAE,CAAC,aAAa;AAC7D,SAAG,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,EAC1D;AACA,SAAO;AACX;AAEA,IAAI,OAAO,SAAU,KAAK,MAAM,IAAI,IAAI;AACpC,MAAIC;AACJ,MAAI,CAAC,GAAG,EAAE,GAAG;AACT,QAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,SAAS;AAC5C,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACrB,MAAAA,MAAK,KAAK,IAAI,CAAC,GAAG,OAAO,IAAI,GAAG,QAAQA,IAAG,CAAC,GAAG,OAAOA,IAAG,CAAC;AAC9D,OAAG,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,OAAO,IAAI;AAAA,EACrC;AACA,MAAID,MAAK,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;AAC1B,SAAO,GAAG,GAAG,EAAE,EAAE,CAAC,IAAI,4EAA4E,KAAK,SAAS,IAAI,KAAK,IAAIA,KAAI,KAAKA,GAAE,GAAG,EAAE;AACjJ;AAEA,IAAI,SAAS,WAAY;AAAE,SAAO,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,aAAa,KAAK,GAAG;AAAG;AAC/J,IAAI,QAAQ,WAAY;AAAE,SAAO,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,OAAO,SAAS,OAAO,IAAI,IAAI,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,MAAM,aAAa,GAAG;AAAG;AAIpN,IAAI,OAAO,WAAY;AAAE,SAAO,CAAC,KAAK,GAAG;AAAG;AAI5C,IAAI,OAAO,WAAY;AAAE,SAAO,CAAC,GAAG;AAAG;AAEvC,IAAI,MAAM,SAAU,KAAK;AAAE,SAAO,YAAY,KAAK,CAAC,IAAI,MAAM,CAAC;AAAG;AAElE,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,KAAK,EAAE,QAAQ,IAAI,GAAG,EAAE,IAAI;AAAG;AAW/D,IAAI,QAAQ,SAAU,MAAM;AACxB,OAAK,SAAS,SAAU,KAAK,OAAO;AAAE,WAAO,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC;AAAA,EAAG;AACtF,SAAO,SAAU,IAAI;AAAE,WAAO,KAAK,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,EAAG;AACrE;AAEA,IAAI,WAAW,SAAU,KAAK,MAAM,MAAM,MAAM,IAAI;AAChD,MAAI;AACJ,MAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,KAAK,KAAK;AAC5C,QAAI;AACA,QAAE,UAAU,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG;AAAA,SACxC;AACD,UAAI,IAAI,CAAC;AACL,UAAE,UAAU;AAChB,WAAK,OAAO,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,IAAE,YAAY,IAAI;AAClB,OAAK,OAAO,SAAU,GAAG,GAAG;AACxB,QAAI;AACA,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,MAAE,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;AAAA,EACxC;AACA,OAAK,YAAY,WAAY;AAAE,MAAE,UAAU;AAAA,EAAG;AAClD;AAEA,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK;AAAI;AAE1D,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,UAAQ,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,IAAM,EAAE,IAAI,CAAC,KAAK,KAAO,EAAE,IAAI,CAAC,KAAK,QAAS;AAAG;AACxG,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,SAAO,GAAG,GAAG,CAAC,IAAK,GAAG,GAAG,IAAI,CAAC,IAAI;AAAa;AAE1E,IAAI,SAAS,SAAU,GAAG,GAAG,GAAG;AAC5B,SAAO,GAAG,EAAE;AACR,MAAE,CAAC,IAAI,GAAG,OAAO;AACzB;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,KAAK,EAAE;AACX,IAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI;AACvF,MAAI,EAAE,SAAS;AACX,WAAO,GAAG,GAAG,KAAK,MAAM,IAAI,KAAK,EAAE,SAAS,KAAK,IAAI,CAAC,IAAI,GAAI,CAAC;AACnE,MAAI,IAAI;AACJ,MAAE,CAAC,IAAI;AACP,aAAS,IAAI,GAAG,KAAK,GAAG,QAAQ,EAAE;AAC9B,QAAE,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC;AAAA,EACnC;AACJ;AAGA,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,KAAK;AACrC,UAAM;AACV,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK;AACT,MAAI,MAAM;AACN,UAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK;AACjC,WAAS,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI;AAChE;AACJ,SAAO,MAAM,MAAM;AACvB;AAEA,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,EAAE;AACV,UAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,KAAO,EAAE,IAAI,CAAC,KAAK,QAAS;AAChF;AAEA,IAAI,OAAO,SAAU,GAAG;AAAE,SAAO,MAAO,EAAE,YAAa,EAAE,SAAS,SAAS,KAAO;AAAI;AAEtF,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,KAAK,EAAE,OAAOE,MAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AAChE,IAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAKA,OAAM,KAAMA,MAAM,KAAK,IAAIA,MAAM;AACzD;AAEA,IAAI,MAAM,SAAU,GAAG;AACnB,OAAK,EAAE,CAAC,IAAI,OAAO,KAAM,EAAE,CAAC,MAAM,IAAK,MAAO,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK;AAC9D,UAAM;AACV,MAAI,EAAE,CAAC,IAAI;AACP,UAAM;AACd;AACA,SAAS,aAAa,MAAM,IAAI;AAC5B,MAAI,CAAC,MAAM,OAAO,QAAQ;AACtB,SAAK,MAAM,OAAO,CAAC;AACvB,OAAK,SAAS;AACd,SAAO;AACX;AAKA,IAAI,UAAyB,WAAY;AACrC,WAASC,SAAQ,MAAM,IAAI;AACvB,QAAI,CAAC,MAAM,OAAO,QAAQ;AACtB,WAAK,MAAM,OAAO,CAAC;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,QAAQ,CAAC;AAAA,EACtB;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,GAAG,GAAG;AAClC,SAAK,OAAO,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;AAAA,EAC5C;AAMA,EAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO,OAAO;AAC7C,QAAI,KAAK;AACL,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,SAAK,IAAI;AACT,SAAK,EAAE,OAAO,SAAS,KAAK;AAAA,EAChC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,eAA8B,2BAAY;AAC1C,WAASC,cAAa,MAAM,IAAI;AAC5B,aAAS;AAAA,MACL;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,OAAO;AAAA,MAAG;AAAA,IAC3C,GAAG,MAAM,aAAa,KAAK,MAAM,MAAM,EAAE,GAAG,SAAU,IAAI;AACtD,UAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;AAC9B,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAiBK,SAAS,YAAY,MAAM,MAAM;AACpC,SAAO,KAAK,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC;AACtC;AAIA,IAAI,UAAyB,WAAY;AAKrC,WAASC,SAAQ,IAAI;AACjB,SAAK,IAAI,CAAC;AACV,SAAK,IAAI,IAAI,GAAG,CAAC;AACjB,SAAK,SAAS;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,GAAG;AAC/B,QAAI,KAAK;AACL,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,IAAI,KAAK,EAAE;AACf,QAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM;AAC3B,MAAE,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI;AAAA,EACzC;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,OAAO;AACnC,SAAK,IAAI,KAAK,EAAE,IAAI,SAAS;AAC7B,QAAI,MAAM,KAAK,EAAE;AACjB,QAAI,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACrC,SAAK,OAAO,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;AAC1C,SAAK,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,EAAE;AACtD,SAAK,IAAI,IAAI,KAAK,GAAI,KAAK,EAAE,IAAI,IAAK,CAAC,GAAG,KAAK,EAAE,KAAK;AAAA,EAC1D;AAMA,EAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO,OAAO;AAC7C,SAAK,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK;AAAA,EAC/B;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,eAA8B,2BAAY;AAK1C,WAASC,cAAa,IAAI;AACtB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,OAAO;AAAA,MAAG;AAAA,IAC3C,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,QAAQ;AACvB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAiBK,SAAS,YAAY,MAAM,KAAK;AACnC,SAAO,MAAM,MAAM,GAAG;AAC1B;AAKA,IAAI,OAAsB,WAAY;AAClC,WAASC,MAAK,MAAM,IAAI;AACpB,SAAK,IAAI,IAAI;AACb,SAAK,IAAI;AACT,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,MAAM,EAAE;AAAA,EAC/B;AAMA,EAAAA,MAAK,UAAU,OAAO,SAAU,OAAO,OAAO;AAC1C,YAAQ,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EAClD;AACA,EAAAA,MAAK,UAAU,IAAI,SAAU,GAAG,GAAG;AAC/B,SAAK,EAAE,EAAE,CAAC;AACV,SAAK,KAAK,EAAE;AACZ,QAAI,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAC5D,QAAI,KAAK;AACL,UAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI;AAC/B,QAAI;AACA,aAAO,KAAK,IAAI,SAAS,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,OAAO,KAAK,IAAI,SAAS,GAAG,KAAK,CAAC;AAC/E,SAAK,OAAO,KAAK,CAAC;AAAA,EACtB;AACA,SAAOA;AACX,EAAE;AA+CF,IAAI,SAAwB,WAAY;AAKpC,WAASC,QAAO,IAAI;AAChB,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,EAAE;AAAA,EACzB;AAMA,EAAAA,QAAO,UAAU,OAAO,SAAU,OAAO,OAAO;AAC5C,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AACpC,QAAI,KAAK,GAAG;AACR,UAAI,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,CAAC,IAAI;AAC1C,UAAI,KAAK,KAAK,EAAE,UAAU,CAAC;AACvB;AACJ,WAAK,IAAI,KAAK,EAAE,SAAS,CAAC,GAAG,KAAK,IAAI;AAAA,IAC1C;AACA,QAAI,OAAO;AACP,UAAI,KAAK,EAAE,SAAS;AAChB,cAAM;AACV,WAAK,IAAI,KAAK,EAAE,SAAS,GAAG,EAAE;AAAA,IAClC;AAGA,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AAAA,EACxC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,cAA6B,2BAAY;AAKzC,WAASC,aAAY,IAAI;AACrB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,SAAS,MAAM;AAAA,MAAG;AAAA,IACnD,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,OAAO;AACtB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAyBF,IAAI,OAAsB,WAAY;AAClC,WAASC,MAAK,MAAM,IAAI;AACpB,SAAK,IAAI,MAAM;AACf,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,MAAM,EAAE;AAAA,EAC/B;AAMA,EAAAA,MAAK,UAAU,OAAO,SAAU,OAAO,OAAO;AAC1C,YAAQ,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EAClD;AACA,EAAAA,MAAK,UAAU,IAAI,SAAU,GAAG,GAAG;AAC/B,SAAK,EAAE,EAAE,CAAC;AACV,QAAI,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACjD,QAAI,KAAK;AACL,UAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI;AAC/B,QAAI;AACA,aAAO,KAAK,IAAI,SAAS,GAAG,KAAK,EAAE,EAAE,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACtB;AACA,SAAOA;AACX,EAAE;AA+CF,IAAI,SAAwB,WAAY;AAKpC,WAASC,QAAO,IAAI;AAChB,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,EAAE;AAAA,EACzB;AAMA,EAAAA,QAAO,UAAU,OAAO,SAAU,OAAO,OAAO;AAC5C,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AACpC,QAAI,KAAK,GAAG;AACR,UAAI,KAAK,EAAE,SAAS,KAAK,CAAC;AACtB;AACJ,WAAK,IAAI,KAAK,EAAE,SAAS,CAAC,GAAG,KAAK,IAAI;AAAA,IAC1C;AACA,QAAI,OAAO;AACP,UAAI,KAAK,EAAE,SAAS;AAChB,cAAM;AACV,WAAK,IAAI,KAAK,EAAE,SAAS,GAAG,EAAE;AAAA,IAClC;AAGA,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AAAA,EACxC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,cAA6B,2BAAY;AAKzC,WAASC,aAAY,IAAI;AACrB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,SAAS,MAAM;AAAA,MAAG;AAAA,IACnD,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,OAAO;AACtB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,EAAE;AAAA,EACT;AACA,SAAOA;AACX,EAAE;AAmBK,SAAS,WAAW,MAAM,KAAK;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,KAAK,SAAS,GAAG,EAAE,IAAI,GAAG;AACvD;AAQA,IAAI,aAA4B,WAAY;AAKxC,WAASC,YAAW,IAAI;AACpB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,CAAC,KAAK,GAAG;AACT,UAAI,KAAK,KAAK,KAAK,EAAE,QAAQ;AACzB,YAAI,IAAI,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AAC3C,UAAE,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,OAAO,KAAK,EAAE,MAAM;AAAA,MAC7C;AAEI,aAAK,IAAI;AACb,UAAI,KAAK,EAAE,SAAS,GAAG;AACnB,YAAI,UAAU;AACd,YAAI,KAAK,WAAY;AAAE,kBAAQ,OAAO,MAAM,SAAS,SAAS;AAAA,QAAG;AACjE,aAAK,IAAK,KAAK,EAAE,CAAC,KAAK,MAAM,KAAK,EAAE,CAAC,KAAK,OAAO,KAAK,EAAE,CAAC,KAAK,IACxD,IAAI,KAAK,EAAE,EAAE,KACX,KAAK,EAAE,CAAC,IAAI,OAAO,KAAM,KAAK,EAAE,CAAC,KAAK,IAAK,MAAO,KAAK,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,KAAK,KAC9E,IAAI,KAAK,EAAE,EAAE,IACb,IAAI,KAAK,EAAE,EAAE;AACvB,aAAK,EAAE,KAAK,KAAK,GAAG,KAAK;AACzB,aAAK,IAAI;AAAA,MACb;AAAA,IACJ;AAEI,WAAK,EAAE,KAAK,OAAO,KAAK;AAAA,EAChC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,kBAAiC,WAAY;AAK7C,WAASC,iBAAgB,IAAI;AACzB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,iBAAgB,UAAU,OAAO,SAAU,OAAO,OAAO;AACrD,eAAW,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACrD;AACA,SAAOA;AACX,EAAE;AAuCF,IAAI,KAAK,OAAO,eAAe,eAA6B,IAAI,YAAY;AAE5E,IAAI,KAAK,OAAO,eAAe,eAA6B,IAAI,YAAY;AAE5E,IAAI,MAAM;AACV,IAAI;AACA,KAAG,OAAO,IAAI,EAAE,QAAQ,KAAK,CAAC;AAC9B,QAAM;AACV,SACO,GAAG;AAAE;AAEZ,IAAI,QAAQ,SAAU,GAAG;AACrB,WAAS,IAAI,IAAI,IAAI,OAAK;AACtB,QAAI,IAAI,EAAE,GAAG;AACb,QAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI;AACtC,QAAI,IAAI,KAAK,EAAE;AACX,aAAO,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;AAC5B,QAAI,CAAC;AACD,WAAK,OAAO,aAAa,CAAC;AAAA,aACrB,MAAM,GAAG;AACd,YAAM,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,MAAO,OAC9E,KAAK,OAAO,aAAa,QAAS,KAAK,IAAK,QAAS,IAAI,IAAK;AAAA,IACtE,WACS,KAAK;AACV,WAAK,OAAO,cAAc,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,EAAG;AAAA;AAEtD,WAAK,OAAO,cAAc,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,EAAG;AAAA,EACpF;AACJ;AAIA,IAAI,aAA4B,WAAY;AAKxC,WAASC,YAAW,IAAI;AACpB,SAAK,SAAS;AACd,QAAI;AACA,WAAK,IAAI,IAAI,YAAY;AAAA;AAEzB,WAAK,IAAI;AAAA,EACjB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,YAAQ,CAAC,CAAC;AACV,QAAI,KAAK,GAAG;AACR,WAAK,OAAO,KAAK,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,GAAG,KAAK;AACzD,UAAI,OAAO;AACP,YAAI,KAAK,EAAE,OAAO,EAAE;AAChB,gBAAM;AACV,aAAK,IAAI;AAAA,MACb;AACA;AAAA,IACJ;AACA,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,MAAM,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AAC7C,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,OAAO,KAAK,EAAE,MAAM;AAC5B,QAAIC,MAAK,MAAM,GAAG,GAAGC,MAAKD,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC;AAC1C,QAAI,OAAO;AACP,UAAI,GAAG;AACH,cAAM;AACV,WAAK,IAAI;AAAA,IACb;AAEI,WAAK,IAAI;AACb,SAAK,OAAOC,KAAI,KAAK;AAAA,EACzB;AACA,SAAOF;AACX,EAAE;AAKF,IAAI,aAA4B,WAAY;AAKxC,WAASG,YAAW,IAAI;AACpB,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,KAAK;AACL,YAAM;AACV,SAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,EACvD;AACA,SAAOA;AACX,EAAE;AASK,SAAS,QAAQ,KAAK,QAAQ;AACjC,MAAI,QAAQ;AACR,QAAI,OAAO,IAAI,GAAG,IAAI,MAAM;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAC9B,WAAK,CAAC,IAAI,IAAI,WAAW,CAAC;AAC9B,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAO,GAAG,OAAO,GAAG;AACxB,MAAI,IAAI,IAAI;AACZ,MAAI,KAAK,IAAI,GAAG,IAAI,UAAU,IAAI,UAAU,EAAE;AAC9C,MAAI,KAAK;AACT,MAAI,IAAI,SAAU,GAAG;AAAE,OAAG,IAAI,IAAI;AAAA,EAAG;AACrC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,KAAK,IAAI,GAAG,QAAQ;AACpB,UAAI,IAAI,IAAI,GAAG,KAAK,KAAM,IAAI,KAAM,EAAE;AACtC,QAAE,IAAI,EAAE;AACR,WAAK;AAAA,IACT;AACA,QAAI,IAAI,IAAI,WAAW,CAAC;AACxB,QAAI,IAAI,OAAO;AACX,QAAE,CAAC;AAAA,aACE,IAAI;AACT,QAAE,MAAO,KAAK,CAAE,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA,aAC9B,IAAI,SAAS,IAAI;AACtB,UAAI,SAAS,IAAI,QAAQ,MAAO,IAAI,WAAW,EAAE,CAAC,IAAI,MAClD,EAAE,MAAO,KAAK,EAAG,GAAG,EAAE,MAAQ,KAAK,KAAM,EAAG,GAAG,EAAE,MAAQ,KAAK,IAAK,EAAG,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA;AAE7F,QAAE,MAAO,KAAK,EAAG,GAAG,EAAE,MAAQ,KAAK,IAAK,EAAG,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA,EACtE;AACA,SAAO,IAAI,IAAI,GAAG,EAAE;AACxB;AAQO,SAAS,UAAU,KAAK,QAAQ;AACnC,MAAI,QAAQ;AACR,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,WAAK,OAAO,aAAa,MAAM,MAAM,IAAI,SAAS,GAAG,IAAI,KAAK,CAAC;AACnE,WAAO;AAAA,EACX,WACS;AACL,WAAO,GAAG,OAAO,GAAG;AAAA,OACnB;AACD,QAAIC,MAAK,MAAM,GAAG,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC5C,QAAI,IAAI;AACJ,YAAM;AACV,WAAO;AAAA,EACX;AACJ;AAGA,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAG;AAUzE,IAAI,OAAO,SAAU,GAAG,GAAG;AACvB,SAAO,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC;AACtC;AACJ,SAAO,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AACtD;AAEA,IAAI,OAAO,SAAU,IAAI;AACrB,MAAI,KAAK;AACT,MAAI,IAAI;AACJ,aAAS,KAAK,IAAI;AACd,UAAI,IAAI,GAAG,CAAC,EAAE;AACd,UAAI,IAAI;AACJ,cAAM;AACV,YAAM,IAAI;AAAA,IACd;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI;AAC3C,MAAIC,MAAK,GAAG,QAAQ,KAAK,EAAE,OAAO,MAAM,MAAM,GAAG;AACjD,MAAI,MAAM,KAAK,EAAE;AACjB,SAAO,GAAG,GAAG,MAAM,OAAO,WAAY,QAAS,GAAG,KAAK;AACvD,MAAI,MAAM;AACN,MAAE,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE;AAC5B,IAAE,CAAC,IAAI,IAAI,KAAK;AAChB,IAAE,GAAG,IAAK,EAAE,QAAQ,KAAM,KAAK,QAAQ,IAAI,EAAE,GAAG,IAAI,KAAK;AACzD,IAAE,GAAG,IAAI,EAAE,cAAc,KAAK,EAAE,GAAG,IAAI,EAAE,eAAe;AACxD,MAAI,KAAK,IAAI,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,IAAI,GAAG,YAAY,IAAI;AAClF,MAAI,IAAI,KAAK,IAAI;AACb,UAAM;AACV,SAAO,GAAG,GAAI,KAAK,KAAQ,GAAG,SAAS,IAAI,KAAM,KAAO,GAAG,QAAQ,KAAK,KAAO,GAAG,SAAS,KAAK,KAAO,GAAG,WAAW,KAAK,IAAM,GAAG,WAAW,MAAM,CAAE,GAAG,KAAK;AAC9J,MAAI,KAAK,MAAM;AACX,WAAO,GAAG,GAAG,EAAE,GAAG;AAClB,WAAO,GAAG,IAAI,GAAG,CAAC;AAClB,WAAO,GAAG,IAAI,GAAG,EAAE,IAAI;AAAA,EAC3B;AACA,SAAO,GAAG,IAAI,IAAIA,GAAE;AACpB,SAAO,GAAG,IAAI,IAAI,GAAG,GAAG,KAAK;AAC7B,MAAI,MAAM,MAAM;AACZ,WAAO,GAAG,GAAG,GAAG;AAChB,WAAO,GAAG,IAAI,GAAG,EAAE,KAAK;AACxB,WAAO,GAAG,IAAI,IAAI,EAAE,GAAG,KAAK;AAAA,EAChC;AACA,IAAE,IAAI,IAAI,CAAC;AACX,OAAKA;AACL,MAAI,KAAK;AACL,aAAS,KAAK,IAAI;AACd,UAAI,MAAM,GAAG,CAAC,GAAG,IAAI,IAAI;AACzB,aAAO,GAAG,GAAG,CAAC,CAAC;AACf,aAAO,GAAG,IAAI,GAAG,CAAC;AAClB,QAAE,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI;AAAA,IAChC;AAAA,EACJ;AACA,MAAI;AACA,MAAE,IAAI,IAAI,CAAC,GAAG,KAAK;AACvB,SAAO;AACX;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/B,SAAO,GAAG,GAAG,SAAS;AACtB,SAAO,GAAG,IAAI,GAAG,CAAC;AAClB,SAAO,GAAG,IAAI,IAAI,CAAC;AACnB,SAAO,GAAG,IAAI,IAAI,CAAC;AACnB,SAAO,GAAG,IAAI,IAAI,CAAC;AACvB;AAIA,IAAI,iBAAgC,WAAY;AAK5C,WAASC,gBAAe,UAAU;AAC9B,SAAK,WAAW;AAChB,SAAK,IAAI,IAAI;AACb,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACvB;AASA,EAAAA,gBAAe,UAAU,UAAU,SAAU,OAAO,OAAO;AACvD,SAAK,OAAO,MAAM,OAAO,KAAK;AAAA,EAClC;AAQA,EAAAA,gBAAe,UAAU,OAAO,SAAU,OAAO,OAAO;AACpD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,SAAK,EAAE,EAAE,KAAK;AACd,SAAK,QAAQ,MAAM;AACnB,QAAI;AACA,WAAK,MAAM,KAAK,EAAE,EAAE;AACxB,SAAK,QAAQ,OAAO,SAAS,KAAK;AAAA,EACtC;AACA,SAAOA;AACX,EAAE;AAOF,IAAI,aAA4B,WAAY;AAMxC,WAASC,YAAW,UAAU,MAAM;AAChC,QAAI,UAAU;AACd,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,mBAAe,KAAK,MAAM,QAAQ;AAClC,SAAK,IAAI,IAAI,QAAQ,MAAM,SAAU,KAAK,OAAO;AAC7C,cAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,IACnC,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,OAAO,IAAI,KAAK,KAAK;AAAA,EAC9B;AACA,EAAAA,YAAW,UAAU,UAAU,SAAU,OAAO,OAAO;AACnD,QAAI;AACA,WAAK,EAAE,KAAK,OAAO,KAAK;AAAA,IAC5B,SACO,GAAG;AACN,WAAK,OAAO,GAAG,MAAM,KAAK;AAAA,IAC9B;AAAA,EACJ;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,mBAAe,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACzD;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,kBAAiC,WAAY;AAM7C,WAASC,iBAAgB,UAAU,MAAM;AACrC,QAAI,UAAU;AACd,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,mBAAe,KAAK,MAAM,QAAQ;AAClC,SAAK,IAAI,IAAI,aAAa,MAAM,SAAU,KAAK,KAAK,OAAO;AACvD,cAAQ,OAAO,KAAK,KAAK,KAAK;AAAA,IAClC,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,OAAO,IAAI,KAAK,KAAK;AAC1B,SAAK,YAAY,KAAK,EAAE;AAAA,EAC5B;AACA,EAAAA,iBAAgB,UAAU,UAAU,SAAU,OAAO,OAAO;AACxD,SAAK,EAAE,KAAK,OAAO,KAAK;AAAA,EAC5B;AAMA,EAAAA,iBAAgB,UAAU,OAAO,SAAU,OAAO,OAAO;AACrD,mBAAe,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACzD;AACA,SAAOA;AACX,EAAE;AAMF,IAAI,MAAqB,WAAY;AAMjC,WAASC,KAAI,IAAI;AACb,SAAK,SAAS;AACd,SAAK,IAAI,CAAC;AACV,SAAK,IAAI;AAAA,EACb;AAKA,EAAAA,KAAI,UAAU,MAAM,SAAU,MAAM;AAChC,QAAI,UAAU;AACd,QAAI,KAAK,IAAI;AACT,YAAM;AACV,QAAI,IAAI,QAAQ,KAAK,QAAQ,GAAGC,MAAK,EAAE;AACvC,QAAI,MAAM,KAAK,SAAS,IAAI,OAAO,QAAQ,GAAG;AAC9C,QAAI,IAAIA,OAAM,KAAK,SAAS,UAAW,KAAM,IAAI,UAAU,EAAE;AAC7D,QAAI,KAAKA,MAAK,KAAK,KAAK,KAAK,IAAI;AACjC,QAAIA,MAAK;AACL,YAAM;AACV,QAAI,SAAS,IAAI,GAAG,EAAE;AACtB,QAAI,QAAQ,GAAG,MAAM,GAAG,CAAC;AACzB,QAAI,OAAO,CAAC,MAAM;AAClB,QAAI,OAAO,WAAY;AACnB,eAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,YAAI,MAAM,OAAO,EAAE;AACnB,gBAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,MACnC;AACA,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,KAAK,KAAK;AACd,SAAK,IAAI;AACT,QAAI,MAAM,KAAK,EAAE;AACjB,QAAI,KAAK,IAAI,MAAM;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,WAAY;AACX,YAAI,KAAK;AACL,eAAK,UAAU;AAAA,MACvB;AAAA,MACA,GAAG,WAAY;AACX,aAAK;AACL,YAAI,IAAI;AACJ,cAAI,MAAM,QAAQ,EAAE,MAAM,CAAC;AAC3B,cAAI;AACA,gBAAI,EAAE;AAAA;AAEN,oBAAQ,IAAI;AAAA,QACpB;AACA,aAAK;AAAA,MACT;AAAA,IACJ,CAAC;AACD,QAAI,KAAK;AACT,SAAK,SAAS,SAAU,KAAK,KAAK,OAAO;AACrC,UAAI,KAAK;AACL,gBAAQ,OAAO,KAAK,KAAK,KAAK;AAC9B,gBAAQ,UAAU;AAAA,MACtB,OACK;AACD,cAAM,IAAI;AACV,aAAK,KAAK,GAAG;AACb,YAAI,OAAO;AACP,cAAI,KAAK,IAAI,GAAG,EAAE;AAClB,iBAAO,IAAI,GAAG,SAAS;AACvB,iBAAO,IAAI,GAAG,KAAK,GAAG;AACtB,iBAAO,IAAI,GAAG,EAAE;AAChB,iBAAO,IAAI,IAAI,KAAK,IAAI;AACxB,eAAK,KAAK,EAAE;AACZ,aAAG,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,OAAO,KAAK;AAClE,cAAI;AACA,eAAG,EAAE;AACT,eAAK;AAAA,QACT,WACS;AACL,eAAK;AAAA,MACb;AAAA,IACJ;AACA,SAAK,EAAE,KAAK,EAAE;AAAA,EAClB;AAMA,EAAAD,KAAI,UAAU,MAAM,WAAY;AAC5B,QAAI,UAAU;AACd,QAAI,KAAK,IAAI,GAAG;AACZ,UAAI,KAAK,IAAI;AACT,cAAM;AACV,YAAM;AAAA,IACV;AACA,QAAI,KAAK;AACL,WAAK,EAAE;AAAA;AAEP,WAAK,EAAE,KAAK;AAAA,QACR,GAAG,WAAY;AACX,cAAI,EAAE,QAAQ,IAAI;AACd;AACJ,kBAAQ,EAAE,OAAO,IAAI,CAAC;AACtB,kBAAQ,EAAE;AAAA,QACd;AAAA,QACA,GAAG,WAAY;AAAA,QAAE;AAAA,MACrB,CAAC;AACL,SAAK,IAAI;AAAA,EACb;AACA,EAAAA,KAAI,UAAU,IAAI,WAAY;AAC1B,QAAI,KAAK,GAAG,IAAI,GAAG,KAAK;AACxB,aAAS,KAAK,GAAGE,MAAK,KAAK,GAAG,KAAKA,IAAG,QAAQ,MAAM;AAChD,UAAI,IAAIA,IAAG,EAAE;AACb,YAAM,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,IAChE;AACA,QAAI,MAAM,IAAI,GAAG,KAAK,EAAE;AACxB,aAASC,MAAK,GAAG,KAAK,KAAK,GAAGA,MAAK,GAAG,QAAQA,OAAM;AAChD,UAAI,IAAI,GAAGA,GAAE;AACb,UAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;AACrC,YAAM,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS,IAAI,KAAK,EAAE;AAAA,IAC3E;AACA,QAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,IAAI,CAAC;AACjC,SAAK,OAAO,MAAM,KAAK,IAAI;AAC3B,SAAK,IAAI;AAAA,EACb;AAKA,EAAAH,KAAI,UAAU,YAAY,WAAY;AAClC,aAAS,KAAK,GAAGE,MAAK,KAAK,GAAG,KAAKA,IAAG,QAAQ,MAAM;AAChD,UAAI,IAAIA,IAAG,EAAE;AACb,QAAE,EAAE;AAAA,IACR;AACA,SAAK,IAAI;AAAA,EACb;AACA,SAAOF;AACX,EAAE;AAgJF,IAAI,mBAAkC,WAAY;AAC9C,WAASI,oBAAmB;AAAA,EAC5B;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,MAAM,OAAO;AACrD,SAAK,OAAO,MAAM,MAAM,KAAK;AAAA,EACjC;AACA,EAAAA,kBAAiB,cAAc;AAC/B,SAAOA;AACX,EAAE;AAMF,IAAI,eAA8B,WAAY;AAI1C,WAASC,gBAAe;AACpB,QAAI,UAAU;AACd,SAAK,IAAI,IAAI,QAAQ,SAAU,KAAK,OAAO;AACvC,cAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,IACnC,CAAC;AAAA,EACL;AACA,EAAAA,cAAa,UAAU,OAAO,SAAU,MAAM,OAAO;AACjD,QAAI;AACA,WAAK,EAAE,KAAK,MAAM,KAAK;AAAA,IAC3B,SACO,GAAG;AACN,WAAK,OAAO,GAAG,MAAM,KAAK;AAAA,IAC9B;AAAA,EACJ;AACA,EAAAA,cAAa,cAAc;AAC3B,SAAOA;AACX,EAAE;AAKF,IAAI,oBAAmC,WAAY;AAI/C,WAASC,mBAAkB,GAAG,IAAI;AAC9B,QAAI,UAAU;AACd,QAAI,KAAK,MAAQ;AACb,WAAK,IAAI,IAAI,QAAQ,SAAU,KAAK,OAAO;AACvC,gBAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,MACnC,CAAC;AAAA,IACL,OACK;AACD,WAAK,IAAI,IAAI,aAAa,SAAU,KAAK,KAAK,OAAO;AACjD,gBAAQ,OAAO,KAAK,KAAK,KAAK;AAAA,MAClC,CAAC;AACD,WAAK,YAAY,KAAK,EAAE;AAAA,IAC5B;AAAA,EACJ;AACA,EAAAA,mBAAkB,UAAU,OAAO,SAAU,MAAM,OAAO;AACtD,QAAI,KAAK,EAAE;AACP,aAAO,IAAI,MAAM,CAAC;AACtB,SAAK,EAAE,KAAK,MAAM,KAAK;AAAA,EAC3B;AACA,EAAAA,mBAAkB,cAAc;AAChC,SAAOA;AACX,EAAE;AAKF,IAAI,QAAuB,WAAY;AAKnC,WAASC,OAAM,IAAI;AACf,SAAK,SAAS;AACd,SAAK,IAAI,CAAC;AACV,SAAK,IAAI;AAAA,MACL,GAAG;AAAA,IACP;AACA,SAAK,IAAI;AAAA,EACb;AAMA,EAAAA,OAAM,UAAU,OAAO,SAAU,OAAO,OAAO;AAC3C,QAAI,UAAU;AACd,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,KAAK,IAAI,GAAG;AACZ,UAAI,MAAM,KAAK,IAAI,KAAK,GAAG,MAAM,MAAM;AACvC,UAAI,QAAQ,MAAM,SAAS,GAAG,GAAG;AACjC,WAAK,KAAK;AACV,UAAI,KAAK;AACL,aAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA;AAE1B,aAAK,EAAE,CAAC,EAAE,KAAK,KAAK;AACxB,cAAQ,MAAM,SAAS,GAAG;AAC1B,UAAI,MAAM;AACN,eAAO,KAAK,KAAK,OAAO,KAAK;AAAA,IACrC,OACK;AACD,UAAI,IAAI,GAAG,IAAI,GAAG,KAAK,QAAQ,MAAM;AACrC,UAAI,CAAC,KAAK,EAAE;AACR,cAAM;AAAA,eACD,CAAC,MAAM;AACZ,cAAM,KAAK;AAAA,WACV;AACD,cAAM,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AACzC,YAAI,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,MAAM;AAAA,MACjD;AACA,UAAI,IAAI,IAAI,QAAQ,KAAK,KAAK,GAAG,MAAM,MAAM,KAAK;AAClD,UAAI,UAAU,WAAY;AACtB,YAAIC;AACJ,YAAI,MAAM,GAAG,KAAK,CAAC;AACnB,YAAI,OAAO,UAAW;AAClB,cAAI,GAAG,KAAK;AACZ,iBAAO,IAAI;AACX,iBAAO,IAAI;AACX,cAAI,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,IAAI,EAAE;AACvH,cAAI,IAAI,IAAI,KAAK,MAAM,IAAI;AACvB,gBAAI,SAAS,CAAC;AACd,mBAAO,EAAE,QAAQ,MAAM;AACvB,gBAAI;AACJ,gBAAI,OAAO,GAAG,KAAK,IAAI,EAAE,GAAG,OAAO,GAAG,KAAK,IAAI,EAAE;AACjD,gBAAI,OAAO,UAAU,IAAI,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5D,gBAAI,QAAQ,YAAY;AACpB,cAAAA,MAAK,KAAK,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG,OAAOA,IAAG,CAAC,GAAG,OAAOA,IAAG,CAAC;AAAA,YAC5D,WACS;AACL,qBAAO;AACX,iBAAK;AACL,mBAAO,IAAI;AACX,gBAAI;AACJ,gBAAI,SAAS;AAAA,cACT,MAAM;AAAA,cACN,aAAa;AAAA,cACb,OAAO,WAAY;AACf,oBAAI,CAAC,OAAO;AACR,wBAAM;AACV,oBAAI,CAAC;AACD,yBAAO,OAAO,MAAM,IAAI,IAAI;AAAA,qBAC3B;AACD,sBAAI,MAAM,QAAQ,EAAE,KAAK;AACzB,sBAAI,CAAC;AACD,0BAAM,8BAA8B;AACxC,wBAAM,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM,IAAI;AACzD,sBAAI,SAAS,SAAU,KAAKC,MAAKC,QAAO;AAAE,2BAAO,OAAO,KAAKD,MAAKC,MAAK;AAAA,kBAAG;AAC1E,2BAAS,KAAK,GAAG,SAAS,QAAQ,KAAK,OAAO,QAAQ,MAAM;AACxD,wBAAID,OAAM,OAAO,EAAE;AACnB,wBAAI,KAAKA,MAAK,KAAK;AAAA,kBACvB;AACA,sBAAI,QAAQ,EAAE,CAAC,KAAK,UAAU,QAAQ;AAClC,4BAAQ,IAAI;AAAA;AAEZ,wBAAI,KAAK,IAAI,IAAI;AAAA,gBACzB;AAAA,cACJ;AAAA,cACA,WAAW,WAAY;AACnB,oBAAI,OAAO,IAAI;AACX,sBAAI,UAAU;AAAA,cACtB;AAAA,YACJ;AACA,gBAAI,QAAQ;AACR,qBAAO,OAAO,MAAM,OAAO,eAAe;AAC9C,mBAAO,OAAO,MAAM;AAAA,UACxB;AACA,iBAAO;AAAA,QACX,WACS,IAAI;AACT,cAAI,OAAO,WAAW;AAClB,iBAAK,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,GAAG,OAAO,IAAI;AAClD,mBAAO;AAAA,UACX,WACS,OAAO,UAAW;AACvB,iBAAK,KAAK,GAAG,IAAI,GAAG,OAAO,IAAI;AAC/B,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,SAAS;AACb,aAAO,IAAI,IAAI,GAAG,EAAE,GAAG;AACnB,YAAI,UAAU,QAAQ;AACtB,YAAI,YAAY;AACZ;AAAA,MACR;AACA,WAAK,IAAI;AACT,UAAI,KAAK,GAAG;AACR,YAAI,MAAM,IAAI,IAAI,SAAS,GAAG,KAAK,MAAM,MAAM,MAAM,MAAM,GAAG,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE,IAAI,IAAI,SAAS,GAAG,CAAC;AACnH,YAAI;AACA,cAAI,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA;AAEjB,eAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,GAAG;AAAA,MAClC;AACA,UAAI,IAAI;AACJ,eAAO,KAAK,KAAK,IAAI,SAAS,CAAC,GAAG,KAAK;AAC3C,WAAK,IAAI,IAAI,SAAS,CAAC;AAAA,IAC3B;AACA,QAAI,OAAO;AACP,UAAI,KAAK;AACL,cAAM;AACV,WAAK,IAAI;AAAA,IACb;AAAA,EACJ;AAMA,EAAAF,OAAM,UAAU,WAAW,SAAU,SAAS;AAC1C,SAAK,EAAE,QAAQ,WAAW,IAAI;AAAA,EAClC;AACA,SAAOA;AACX,EAAE;;;AC9xEF,SAAS,SAAU,GAAG,GAAG,GAAI;AAE5B,QAAM,IAAI,EAAE,SAAS,IAAI;AAEzB,MAAK,KAAK,EAAG,CAAE,GAAI;AAElB,WAAO,IAAI;AAAA,EAEZ;AAEA,MAAK,KAAK,EAAG,CAAE,GAAI;AAElB,WAAO;AAAA,EAER;AAEA,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,MAAM,KAAK,OAAS,MAAM,QAAS,CAAE;AAEzC,SAAQ,IAAI,EAAG,GAAI,KAAK,KAAK,EAAG,MAAM,CAAE,GAAI;AAE3C,QAAK,IAAI,EAAG,GAAI,GAAI;AAEnB,aAAO;AAAA,IAER,OAAO;AAEN,YAAM;AAAA,IAEP;AAEA,UAAM,KAAK,OAAS,MAAM,QAAS,CAAE;AAAA,EAEtC;AAEA,SAAO;AAER;AAaA,SAAS,mBAAoB,MAAM,GAAG,GAAG,GAAI;AAE5C,QAAM,IAAI,CAAC;AACX,QAAM,OAAO,CAAC;AACd,QAAM,QAAQ,CAAC;AACf,IAAG,CAAE,IAAI;AAET,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,SAAM,CAAE,IAAI,IAAI,EAAG,OAAO,IAAI,CAAE;AAChC,UAAO,CAAE,IAAI,EAAG,OAAO,CAAE,IAAI;AAE7B,QAAI,QAAQ;AAEZ,aAAU,IAAI,GAAG,IAAI,GAAG,EAAG,GAAI;AAE9B,YAAM,KAAK,MAAO,IAAI,CAAE;AACxB,YAAM,KAAK,KAAM,IAAI,CAAE;AACvB,YAAM,OAAO,EAAG,CAAE,KAAM,KAAK;AAC7B,QAAG,CAAE,IAAI,QAAQ,KAAK;AACtB,cAAQ,KAAK;AAAA,IAEd;AAEA,MAAG,CAAE,IAAI;AAAA,EAEV;AAEA,SAAO;AAER;AAaA,SAAS,iBAAkB,GAAG,GAAG,GAAG,GAAI;AAEvC,QAAM,OAAO,SAAU,GAAG,GAAG,CAAE;AAC/B,QAAM,IAAI,mBAAoB,MAAM,GAAG,GAAG,CAAE;AAC5C,QAAM,IAAI,IAAI,QAAS,GAAG,GAAG,GAAG,CAAE;AAElC,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,UAAM,QAAQ,EAAG,OAAO,IAAI,CAAE;AAC9B,UAAM,KAAK,EAAG,CAAE;AAChB,UAAM,MAAM,MAAM,IAAI;AACtB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AACjB,MAAE,KAAK,MAAM,IAAI;AAAA,EAElB;AAEA,SAAO;AAER;AAcA,SAAS,6BAA8B,MAAM,GAAG,GAAG,GAAG,GAAI;AAEzD,QAAM,UAAU,CAAC;AACjB,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG;AAC3B,YAAS,CAAE,IAAI;AAEhB,QAAM,OAAO,CAAC;AAEd,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG;AAC3B,SAAM,CAAE,IAAI,QAAQ,MAAO,CAAE;AAE9B,QAAM,MAAM,CAAC;AAEb,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG;AAC3B,QAAK,CAAE,IAAI,QAAQ,MAAO,CAAE;AAE7B,MAAK,CAAE,EAAG,CAAE,IAAI;AAEhB,QAAM,OAAO,QAAQ,MAAO,CAAE;AAC9B,QAAM,QAAQ,QAAQ,MAAO,CAAE;AAE/B,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,SAAM,CAAE,IAAI,IAAI,EAAG,OAAO,IAAI,CAAE;AAChC,UAAO,CAAE,IAAI,EAAG,OAAO,CAAE,IAAI;AAE7B,QAAI,QAAQ;AAEZ,aAAUI,KAAI,GAAGA,KAAI,GAAG,EAAGA,IAAI;AAE9B,YAAM,KAAK,MAAOA,KAAI,CAAE;AACxB,YAAM,KAAK,KAAM,IAAIA,EAAE;AACvB,UAAK,CAAE,EAAGA,EAAE,IAAI,KAAK;AAErB,YAAM,OAAO,IAAKA,EAAE,EAAG,IAAI,CAAE,IAAI,IAAK,CAAE,EAAGA,EAAE;AAC7C,UAAKA,EAAE,EAAG,CAAE,IAAI,QAAQ,KAAK;AAC7B,cAAQ,KAAK;AAAA,IAEd;AAEA,QAAK,CAAE,EAAG,CAAE,IAAI;AAAA,EAEjB;AAEA,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,SAAM,CAAE,EAAG,CAAE,IAAI,IAAK,CAAE,EAAG,CAAE;AAAA,EAE9B;AAEA,WAAUA,KAAI,GAAGA,MAAK,GAAG,EAAGA,IAAI;AAE/B,QAAI,KAAK;AACT,QAAI,KAAK;AAET,UAAM,IAAI,CAAC;AACX,aAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,QAAG,CAAE,IAAI,QAAQ,MAAO,CAAE;AAAA,IAE3B;AAEA,MAAG,CAAE,EAAG,CAAE,IAAI;AAEd,aAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,UAAI,IAAI;AACR,YAAM,KAAKA,KAAI;AACf,YAAM,KAAK,IAAI;AAEf,UAAKA,MAAK,GAAI;AAEb,UAAG,EAAG,EAAG,CAAE,IAAI,EAAG,EAAG,EAAG,CAAE,IAAI,IAAK,KAAK,CAAE,EAAG,EAAG;AAChD,YAAI,EAAG,EAAG,EAAG,CAAE,IAAI,IAAK,EAAG,EAAG,EAAG;AAAA,MAElC;AAEA,YAAM,KAAO,MAAM,KAAQ,IAAI,CAAE;AACjC,YAAM,KAAOA,KAAI,KAAK,KAAO,IAAI,IAAI,IAAIA;AAEzC,eAAUC,KAAI,IAAIA,MAAK,IAAI,EAAGA,IAAI;AAEjC,UAAG,EAAG,EAAGA,EAAE,KAAM,EAAG,EAAG,EAAGA,EAAE,IAAI,EAAG,EAAG,EAAGA,KAAI,CAAE,KAAM,IAAK,KAAK,CAAE,EAAG,KAAKA,EAAE;AAC3E,aAAK,EAAG,EAAG,EAAGA,EAAE,IAAI,IAAK,KAAKA,EAAE,EAAG,EAAG;AAAA,MAEvC;AAEA,UAAKD,MAAK,IAAK;AAEd,UAAG,EAAG,EAAG,CAAE,IAAI,CAAE,EAAG,EAAG,EAAG,IAAI,CAAE,IAAI,IAAK,KAAK,CAAE,EAAGA,EAAE;AACrD,aAAK,EAAG,EAAG,EAAG,CAAE,IAAI,IAAKA,EAAE,EAAG,EAAG;AAAA,MAElC;AAEA,WAAM,CAAE,EAAGA,EAAE,IAAI;AAEjB,YAAM,IAAI;AACV,WAAK;AACL,WAAK;AAAA,IAEN;AAAA,EAED;AAEA,MAAI,IAAI;AAER,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,aAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,WAAM,CAAE,EAAG,CAAE,KAAK;AAAA,IAEnB;AAEA,SAAK,IAAI;AAAA,EAEV;AAEA,SAAO;AAER;AAcA,SAAS,uBAAwB,GAAG,GAAG,GAAG,GAAG,IAAK;AAEjD,QAAM,KAAK,KAAK,IAAI,KAAK;AACzB,QAAM,KAAK,CAAC;AACZ,QAAM,OAAO,SAAU,GAAG,GAAG,CAAE;AAC/B,QAAM,QAAQ,6BAA8B,MAAM,GAAG,GAAG,IAAI,CAAE;AAC9D,QAAM,KAAK,CAAC;AAEZ,WAAU,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAG,GAAI;AAErC,UAAM,QAAQ,EAAG,CAAE,EAAE,MAAM;AAC3B,UAAM,IAAI,MAAM;AAEhB,UAAM,KAAK;AACX,UAAM,KAAK;AACX,UAAM,KAAK;AAEX,OAAI,CAAE,IAAI;AAAA,EAEX;AAEA,WAAU,IAAI,GAAG,KAAK,IAAI,EAAG,GAAI;AAEhC,UAAM,QAAQ,GAAI,OAAO,CAAE,EAAE,MAAM,EAAE,eAAgB,MAAO,CAAE,EAAG,CAAE,CAAE;AAErE,aAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,YAAM,IAAK,GAAI,OAAO,IAAI,CAAE,EAAE,MAAM,EAAE,eAAgB,MAAO,CAAE,EAAG,CAAE,CAAE,CAAE;AAAA,IAEzE;AAEA,OAAI,CAAE,IAAI;AAAA,EAEX;AAEA,WAAU,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,EAAG,GAAI;AAEzC,OAAI,CAAE,IAAI,IAAI,QAAS,GAAG,GAAG,CAAE;AAAA,EAEhC;AAEA,SAAO;AAER;AAQA,SAAS,WAAY,GAAG,GAAI;AAE3B,MAAI,MAAM;AAEV,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,WAAO;AAAA,EAER;AAEA,MAAI,QAAQ;AAEZ,WAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,aAAS;AAAA,EAEV;AAEA,WAAU,IAAI,GAAG,KAAK,IAAI,GAAG,EAAG,GAAI;AAEnC,aAAS;AAAA,EAEV;AAEA,SAAO,MAAM;AAEd;AAUA,SAAS,6BAA8B,OAAQ;AAE9C,QAAM,KAAK,MAAM;AACjB,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AAEf,WAAU,IAAI,GAAG,IAAI,IAAI,EAAG,GAAI;AAE/B,UAAM,QAAQ,MAAO,CAAE;AACvB,UAAO,CAAE,IAAI,IAAI,QAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAE;AACpD,UAAO,CAAE,IAAI,MAAM;AAAA,EAEpB;AAEA,QAAM,KAAK,CAAC;AAEZ,WAAU,IAAI,GAAG,IAAI,IAAI,EAAG,GAAI;AAE/B,UAAM,IAAI,MAAO,CAAE,EAAE,MAAM;AAE3B,aAAU,IAAI,GAAG,KAAK,GAAG,EAAG,GAAI;AAE/B,QAAE,IAAK,GAAI,IAAI,CAAE,EAAE,MAAM,EAAE,eAAgB,WAAY,GAAG,CAAE,IAAI,MAAO,CAAE,CAAE,CAAE;AAAA,IAE9E;AAEA,OAAI,CAAE,IAAI,EAAE,aAAc,MAAO,CAAE,CAAE;AAAA,EAEtC;AAEA,SAAO;AAER;AAcA,SAAS,qBAAsB,GAAG,GAAG,GAAG,GAAG,IAAK;AAE/C,QAAM,QAAQ,uBAAwB,GAAG,GAAG,GAAG,GAAG,EAAG;AACrD,SAAO,6BAA8B,KAAM;AAE5C;;;ACzZA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAE9B,YACC,QACA,OACA,eACA,WACA,SACC;AAED,UAAM;AAEN,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,gBAAgB,CAAC;AAEtB,SAAK,YAAY,aAAa;AAC9B,SAAK,UAAU,WAAa,KAAK,MAAM,SAAS;AAEhD,aAAU,IAAI,GAAG,IAAI,cAAc,QAAQ,EAAG,GAAI;AAGjD,YAAM,QAAQ,cAAe,CAAE;AAC/B,WAAK,cAAe,CAAE,IAAI,IAAI,QAAS,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAE;AAAA,IAE3E;AAAA,EAED;AAAA,EAEA,SAAU,GAAG,iBAAiB,IAAI,QAAQ,GAAI;AAE7C,UAAM,QAAQ;AAEd,UAAM,IAAI,KAAK,MAAO,KAAK,SAAU,IAAI,KAAM,KAAK,MAAO,KAAK,OAAQ,IAAI,KAAK,MAAO,KAAK,SAAU;AAGvG,UAAM,SAAoB,iBAAkB,KAAK,QAAQ,KAAK,OAAO,KAAK,eAAe,CAAE;AAE3F,QAAK,OAAO,MAAM,GAAM;AAGvB,aAAO,aAAc,OAAO,CAAE;AAAA,IAE/B;AAEA,WAAO,MAAM,IAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAAA,EAEhD;AAAA,EAEA,WAAY,GAAG,iBAAiB,IAAI,QAAQ,GAAI;AAE/C,UAAM,UAAU;AAEhB,UAAM,IAAI,KAAK,MAAO,CAAE,IAAI,KAAM,KAAK,MAAO,KAAK,MAAM,SAAS,CAAE,IAAI,KAAK,MAAO,CAAE;AACtF,UAAM,OAAkB,qBAAsB,KAAK,QAAQ,KAAK,OAAO,KAAK,eAAe,GAAG,CAAE;AAChG,YAAQ,KAAM,KAAM,CAAE,CAAE,EAAE,UAAU;AAEpC,WAAO;AAAA,EAER;AAED;;;ACbA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAM,YAAN,cAAwB,OAAO;AAAA,EAE9B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAAA,EAEhB;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,OAAS,MAAM,SAAS,KAAO,YAAY,eAAgB,GAAI,IAAI,MAAM;AAE/E,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,MAAM,IAAK;AAC3B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,MAAM,aAAc;AAC7C,WAAO,mBAAoB,MAAM,eAAgB;AAEjD,WAAO,KAAM,KAAK,SAAW,QAAS;AAErC,UAAI;AAEH,eAAQ,MAAM,MAAO,QAAQ,IAAK,CAAE;AAAA,MAErC,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,MAAO,WAAW,MAAO;AAExB,QAAK,kBAAmB,SAAU,GAAI;AAErC,gBAAU,IAAI,aAAa,EAAE,MAAO,SAAU;AAAA,IAE/C,OAAO;AAEN,YAAM,UAAU,2BAA4B,SAAU;AAEtD,UAAK,CAAE,iBAAkB,OAAQ,GAAI;AAEpC,cAAM,IAAI,MAAO,kCAAmC;AAAA,MAErD;AAEA,UAAK,cAAe,OAAQ,IAAI,KAAO;AAEtC,cAAM,IAAI,MAAO,8DAA8D,cAAe,OAAQ,CAAE;AAAA,MAEzG;AAEA,gBAAU,IAAI,WAAW,EAAE,MAAO,OAAQ;AAAA,IAE3C;AAIA,UAAM,gBAAgB,IAAI,cAAe,KAAK,OAAQ,EAAE,QAAS,KAAK,gBAAgB,IAAK,EAAE,eAAgB,KAAK,WAAY;AAE9H,WAAO,IAAI,cAAe,eAAe,KAAK,OAAQ,EAAE,MAAO,OAAQ;AAAA,EAExE;AAED;AAGA,IAAM,gBAAN,MAAoB;AAAA,EAEnB,YAAa,eAAe,SAAU;AAErC,SAAK,gBAAgB;AACrB,SAAK,UAAU;AAAA,EAEhB;AAAA,EAEA,QAAQ;AAEP,kBAAc,KAAK,iBAAiB;AAEpC,UAAM,SAAS,KAAK,YAAY;AAChC,UAAM,WAAW,KAAK,cAAe,MAAO;AAC5C,UAAM,YAAY,KAAK,eAAgB,QAAS;AAChD,UAAM,YAAY,KAAK,eAAe;AACtC,UAAM,cAAc,IAAI,eAAe,EAAE,MAAO,SAAU;AAE1D,SAAK,WAAY,WAAW,aAAa,SAAU;AAEnD,WAAO;AAAA,EAER;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAElB,UAAM,gBAAgB,oBAAI,IAAI;AAE9B,QAAK,iBAAiB,SAAU;AAE/B,YAAM,iBAAiB,QAAQ,YAAY;AAE3C,qBAAe,QAAS,SAAW,eAAgB;AAElD,cAAM,SAAS,cAAe,CAAE;AAChC,cAAM,OAAO,cAAe,CAAE;AAC9B,cAAM,eAAe,cAAe,CAAE;AAEtC,YAAK,CAAE,cAAc,IAAK,MAAO,GAAI;AAEpC,wBAAc,IAAK,QAAQ;AAAA,YAC1B,SAAS,CAAC;AAAA,YACV,UAAU,CAAC;AAAA,UACZ,CAAE;AAAA,QAEH;AAEA,cAAM,qBAAqB,EAAE,IAAI,MAAM,aAA2B;AAClE,sBAAc,IAAK,MAAO,EAAE,QAAQ,KAAM,kBAAmB;AAE7D,YAAK,CAAE,cAAc,IAAK,IAAK,GAAI;AAElC,wBAAc,IAAK,MAAM;AAAA,YACxB,SAAS,CAAC;AAAA,YACV,UAAU,CAAC;AAAA,UACZ,CAAE;AAAA,QAEH;AAEA,cAAM,oBAAoB,EAAE,IAAI,QAAQ,aAA2B;AACnE,sBAAc,IAAK,IAAK,EAAE,SAAS,KAAM,iBAAkB;AAAA,MAE5D,CAAE;AAAA,IAEH;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAEb,UAAM,SAAS,CAAC;AAChB,UAAM,QAAQ,CAAC;AAEf,QAAK,WAAW,QAAQ,SAAU;AAEjC,YAAM,aAAa,QAAQ,QAAQ;AAEnC,iBAAY,UAAU,YAAa;AAElC,cAAM,YAAY,WAAY,MAAO;AAErC,cAAM,KAAK,SAAU,MAAO;AAE5B,eAAQ,EAAG,IAAI,UAAU,oBAAoB,UAAU;AAGvD,YAAK,aAAa,WAAY;AAE7B,gBAAM,qBAAuB,UAAU,mBAAmB,eAAmB,UAAU,QAAQ,aAAa;AAC5G,gBAAM,gBAAkB,OAAO,UAAU,YAAY,YAAgB,UAAU,YAAY;AAE3F,cAAK,sBAAsB,eAAgB;AAE1C,kBAAM,QAAQ,KAAK,WAAY,WAAY,MAAO,CAAE;AAEpD,kBAAO,UAAU,oBAAoB,UAAU,QAAS,IAAI;AAAA,UAE7D;AAAA,QAED;AAAA,MAED;AAAA,IAED;AAEA,eAAY,MAAM,QAAS;AAE1B,YAAM,WAAW,OAAQ,EAAG;AAE5B,UAAK,MAAO,QAAS,MAAM,OAAY,QAAQ,EAAG,IAAI,MAAO,QAAS;AAAA,UACjE,QAAQ,EAAG,IAAI,OAAQ,EAAG,EAAE,MAAO,IAAK,EAAE,IAAI;AAAA,IAEpD;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,WAAY,WAAY;AAEvB,UAAM,UAAU,UAAU;AAC1B,UAAM,WAAW,UAAU,oBAAoB,UAAU;AACzD,UAAM,YAAY,SAAS,MAAO,SAAS,YAAa,GAAI,IAAI,CAAE,EAAE,YAAY;AAEhF,QAAI;AAEJ,YAAS,WAAY;AAAA,MAEpB,KAAK;AAEJ,eAAO;AACP;AAAA,MAED,KAAK;AAAA,MACL,KAAK;AAEJ,eAAO;AACP;AAAA,MAED,KAAK;AAEJ,eAAO;AACP;AAAA,MAED,KAAK;AAEJ,eAAO;AACP;AAAA,MAED,KAAK;AAEJ,YAAK,KAAK,QAAQ,WAAY,MAAO,MAAM,MAAO;AAEjD,kBAAQ,KAAM,8CAA8C,QAAS;AAAA,QAEtE;AAEA,eAAO;AACP;AAAA,MAED;AAEC,gBAAQ,KAAM,4BAA4B,YAAY,qBAAsB;AAC5E;AAAA,IAEF;AAEA,QAAK,OAAO,YAAY,UAAW;AAElC,aAAO,UAAU,OAAO,aAAa;AAAA,IAEtC,OAAO;AAEN,YAAM,QAAQ,IAAI,WAAY,OAAQ;AACtC,aAAO,OAAO,IAAI,gBAAiB,IAAI,KAAM,CAAE,KAAM,GAAG,EAAE,KAAW,CAAE,CAAE;AAAA,IAE1E;AAAA,EAED;AAAA;AAAA;AAAA;AAAA,EAKA,cAAe,QAAS;AAEvB,UAAM,aAAa,oBAAI,IAAI;AAE3B,QAAK,aAAa,QAAQ,SAAU;AAEnC,YAAM,eAAe,QAAQ,QAAQ;AACrC,iBAAY,UAAU,cAAe;AAEpC,cAAM,UAAU,KAAK,aAAc,aAAc,MAAO,GAAG,MAAO;AAClE,mBAAW,IAAK,SAAU,MAAO,GAAG,OAAQ;AAAA,MAE7C;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,aAAc,aAAa,QAAS;AAEnC,UAAM,UAAU,KAAK,YAAa,aAAa,MAAO;AAEtD,YAAQ,KAAK,YAAY;AAEzB,YAAQ,OAAO,YAAY;AAE3B,UAAM,YAAY,YAAY;AAC9B,UAAM,YAAY,YAAY;AAE9B,UAAM,SAAS,cAAc,SAAY,UAAU,QAAQ;AAC3D,UAAM,SAAS,cAAc,SAAY,UAAU,QAAQ;AAK3D,YAAQ,QAAQ,WAAW,IAAI,iBAAiB;AAChD,YAAQ,QAAQ,WAAW,IAAI,iBAAiB;AAEhD,QAAK,aAAa,aAAc;AAE/B,YAAM,SAAS,YAAY,QAAQ;AAEnC,cAAQ,OAAO,IAAI,OAAQ,CAAE;AAC7B,cAAQ,OAAO,IAAI,OAAQ,CAAE;AAAA,IAE9B;AAEA,QAAK,iBAAiB,aAAc;AAEnC,YAAM,SAAS,YAAY,YAAY;AAEvC,cAAQ,OAAO,IAAI,OAAQ,CAAE;AAC7B,cAAQ,OAAO,IAAI,OAAQ,CAAE;AAAA,IAE9B;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,YAAa,aAAa,QAAS;AAElC,QAAI;AAEJ,UAAM,cAAc,KAAK,cAAc;AAEvC,UAAM,WAAW,YAAY,IAAK,YAAY,EAAG,EAAE;AAEnD,QAAK,aAAa,UAAa,SAAS,SAAS,KAAK,OAAQ,SAAU,CAAE,EAAE,EAAG,MAAM,QAAY;AAEhG,iBAAW,OAAQ,SAAU,CAAE,EAAE,EAAG;AAEpC,UAAK,SAAS,QAAS,OAAQ,MAAM,KAAK,SAAS,QAAS,OAAQ,MAAM,GAAI;AAE7E,aAAK,cAAc,QAAS,MAAU;AAAA,MAEvC;AAAA,IAED;AAEA,QAAI;AAEJ,UAAM,YAAY,YAAY,SAAS,MAAO,EAAI,EAAE,YAAY;AAEhE,QAAK,cAAc,OAAQ;AAE1B,YAAM,SAAS,KAAK,QAAQ,WAAY,MAAO;AAE/C,UAAK,WAAW,MAAO;AAEtB,gBAAQ,KAAM,qEAAqE,YAAY,gBAAiB;AAChH,kBAAU,IAAI,QAAQ;AAAA,MAEvB,OAAO;AAEN,eAAO,QAAS,KAAK,cAAc,IAAK;AACxC,kBAAU,OAAO,KAAM,QAAS;AAAA,MAEjC;AAAA,IAED,WAAY,cAAc,OAAQ;AAEjC,YAAM,SAAS,KAAK,QAAQ,WAAY,MAAO;AAE/C,UAAK,WAAW,MAAO;AAEtB,gBAAQ,KAAM,qEAAqE,YAAY,gBAAiB;AAChH,kBAAU,IAAI,QAAQ;AAAA,MAEvB,OAAO;AAEN,eAAO,QAAS,KAAK,cAAc,IAAK;AACxC,kBAAU,OAAO,KAAM,QAAS;AAAA,MAEjC;AAAA,IAED,WAAY,cAAc,OAAQ;AAEjC,cAAQ,KAAM,+EAA+E,YAAY,gBAAiB;AAC1H,gBAAU,IAAI,QAAQ;AAAA,IAEvB,OAAO;AAEN,gBAAU,KAAK,cAAc,KAAM,QAAS;AAAA,IAE7C;AAEA,SAAK,cAAc,QAAS,WAAY;AAExC,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,eAAgB,YAAa;AAE5B,UAAM,cAAc,oBAAI,IAAI;AAE5B,QAAK,cAAc,QAAQ,SAAU;AAEpC,YAAM,gBAAgB,QAAQ,QAAQ;AAEtC,iBAAY,UAAU,eAAgB;AAErC,cAAM,WAAW,KAAK,cAAe,cAAe,MAAO,GAAG,UAAW;AAEzE,YAAK,aAAa,KAAO,aAAY,IAAK,SAAU,MAAO,GAAG,QAAS;AAAA,MAExE;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA,EAKA,cAAe,cAAc,YAAa;AAEzC,UAAM,KAAK,aAAa;AACxB,UAAM,OAAO,aAAa;AAC1B,QAAI,OAAO,aAAa;AAGxB,QAAK,OAAO,SAAS,UAAW;AAE/B,aAAO,KAAK;AAAA,IAEb;AAGA,QAAK,CAAE,YAAY,IAAK,EAAG,EAAI,QAAO;AAEtC,UAAM,aAAa,KAAK,gBAAiB,cAAc,YAAY,EAAG;AAEtE,QAAI;AAEJ,YAAS,KAAK,YAAY,GAAI;AAAA,MAE7B,KAAK;AACJ,mBAAW,IAAI,kBAAkB;AACjC;AAAA,MACD,KAAK;AACJ,mBAAW,IAAI,oBAAoB;AACnC;AAAA,MACD;AACC,gBAAQ,KAAM,iFAAiF,IAAK;AACpG,mBAAW,IAAI,kBAAkB;AACjC;AAAA,IAEF;AAEA,aAAS,UAAW,UAAW;AAC/B,aAAS,OAAO;AAEhB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA,EAIA,gBAAiB,cAAc,YAAY,IAAK;AAE/C,UAAM,aAAa,CAAC;AAEpB,QAAK,aAAa,YAAa;AAE9B,iBAAW,YAAY,aAAa,WAAW;AAAA,IAEhD;AAEA,QAAK,aAAa,SAAU;AAE3B,iBAAW,QAAQ,IAAI,MAAM,EAAE,UAAW,aAAa,QAAQ,KAAM,EAAE,oBAAoB;AAAA,IAE5F,WAAY,aAAa,iBAAkB,aAAa,aAAa,SAAS,WAAW,aAAa,aAAa,SAAS,aAAe;AAG1I,iBAAW,QAAQ,IAAI,MAAM,EAAE,UAAW,aAAa,aAAa,KAAM,EAAE,oBAAoB;AAAA,IAEjG;AAEA,QAAK,aAAa,oBAAqB;AAEtC,iBAAW,oBAAoB,aAAa,mBAAmB;AAAA,IAEhE;AAEA,QAAK,aAAa,UAAW;AAE5B,iBAAW,WAAW,IAAI,MAAM,EAAE,UAAW,aAAa,SAAS,KAAM,EAAE,oBAAoB;AAAA,IAEhG,WAAY,aAAa,kBAAmB,aAAa,cAAc,SAAS,WAAW,aAAa,cAAc,SAAS,aAAe;AAG7I,iBAAW,WAAW,IAAI,MAAM,EAAE,UAAW,aAAa,cAAc,KAAM,EAAE,oBAAoB;AAAA,IAErG;AAEA,QAAK,aAAa,gBAAiB;AAElC,iBAAW,oBAAoB,WAAY,aAAa,eAAe,KAAM;AAAA,IAE9E;AAEA,QAAK,aAAa,SAAU;AAE3B,iBAAW,UAAU,WAAY,aAAa,QAAQ,KAAM;AAAA,IAE7D;AAEA,QAAK,WAAW,UAAU,GAAM;AAE/B,iBAAW,cAAc;AAAA,IAE1B;AAEA,QAAK,aAAa,kBAAmB;AAEpC,iBAAW,eAAe,aAAa,iBAAiB;AAAA,IAEzD;AAEA,QAAK,aAAa,WAAY;AAE7B,iBAAW,YAAY,aAAa,UAAU;AAAA,IAE/C;AAEA,QAAK,aAAa,UAAW;AAE5B,iBAAW,WAAW,IAAI,MAAM,EAAE,UAAW,aAAa,SAAS,KAAM,EAAE,oBAAoB;AAAA,IAEhG,WAAY,aAAa,iBAAiB,aAAa,cAAc,SAAS,SAAU;AAGvF,iBAAW,WAAW,IAAI,MAAM,EAAE,UAAW,aAAa,cAAc,KAAM,EAAE,oBAAoB;AAAA,IAErG;AAEA,UAAM,QAAQ;AACd,gBAAY,IAAK,EAAG,EAAE,SAAS,QAAS,SAAW,OAAQ;AAE1D,YAAM,OAAO,MAAM;AAEnB,cAAS,MAAO;AAAA,QAEf,KAAK;AACJ,qBAAW,UAAU,MAAM,WAAY,YAAY,MAAM,EAAG;AAC5D;AAAA,QAED,KAAK;AACJ,qBAAW,QAAQ,MAAM,WAAY,YAAY,MAAM,EAAG;AAC1D;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,qBAAW,MAAM,MAAM,WAAY,YAAY,MAAM,EAAG;AACxD,cAAK,WAAW,QAAQ,QAAY;AAEnC,uBAAW,IAAI,aAAa;AAAA,UAE7B;AAEA;AAAA,QAED,KAAK;AACJ,qBAAW,kBAAkB,MAAM,WAAY,YAAY,MAAM,EAAG;AACpE;AAAA,QAED,KAAK;AACJ,qBAAW,cAAc,MAAM,WAAY,YAAY,MAAM,EAAG;AAChE,cAAK,WAAW,gBAAgB,QAAY;AAE3C,uBAAW,YAAY,aAAa;AAAA,UAErC;AAEA;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,qBAAW,YAAY,MAAM,WAAY,YAAY,MAAM,EAAG;AAC9D;AAAA,QAED,KAAK;AACJ,qBAAW,SAAS,MAAM,WAAY,YAAY,MAAM,EAAG;AAC3D,cAAK,WAAW,WAAW,QAAY;AAEtC,uBAAW,OAAO,UAAU;AAC5B,uBAAW,OAAO,aAAa;AAAA,UAEhC;AAEA;AAAA,QAED,KAAK;AACJ,qBAAW,cAAc,MAAM,WAAY,YAAY,MAAM,EAAG;AAChE,cAAK,WAAW,gBAAgB,QAAY;AAE3C,uBAAW,YAAY,aAAa;AAAA,UAErC;AAEA;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,qBAAW,WAAW,MAAM,WAAY,YAAY,MAAM,EAAG;AAC7D,qBAAW,cAAc;AACzB;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACC,kBAAQ,KAAM,2EAA2E,IAAK;AAC9F;AAAA,MAEF;AAAA,IAED,CAAE;AAEF,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,WAAY,YAAY,IAAK;AAG5B,QAAK,oBAAoB,QAAQ,WAAW,MAAM,QAAQ,QAAQ,gBAAiB;AAElF,cAAQ,KAAM,kGAAmG;AACjH,WAAK,YAAY,IAAK,EAAG,EAAE,SAAU,CAAE,EAAE;AAAA,IAE1C;AAEA,WAAO,WAAW,IAAK,EAAG;AAAA,EAE3B;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAEhB,UAAM,YAAY,CAAC;AACnB,UAAM,eAAe,CAAC;AAEtB,QAAK,cAAc,QAAQ,SAAU;AAEpC,YAAM,gBAAgB,QAAQ,QAAQ;AAEtC,iBAAY,UAAU,eAAgB;AAErC,cAAM,eAAe,cAAe,MAAO;AAE3C,cAAM,gBAAgB,YAAY,IAAK,SAAU,MAAO,CAAE;AAE1D,YAAK,aAAa,aAAa,QAAS;AAEvC,gBAAM,WAAW,KAAK,cAAe,eAAe,aAAc;AAClE,mBAAS,KAAK;AAEd,cAAK,cAAc,QAAQ,SAAS,EAAI,SAAQ,KAAM,gFAAiF;AACvI,mBAAS,aAAa,cAAc,QAAS,CAAE,EAAE;AAEjD,oBAAW,MAAO,IAAI;AAAA,QAEvB,WAAY,aAAa,aAAa,cAAe;AAEpD,gBAAM,cAAc;AAAA,YACnB,IAAI;AAAA,UACL;AAEA,sBAAY,aAAa,KAAK,kBAAmB,eAAe,aAAc;AAC9E,sBAAY,KAAK;AAEjB,cAAK,cAAc,QAAQ,SAAS,EAAI,SAAQ,KAAM,oFAAqF;AAE3I,uBAAc,MAAO,IAAI;AAAA,QAE1B;AAAA,MAED;AAAA,IAED;AAEA,WAAO;AAAA,MAEN;AAAA,MACA;AAAA,IAED;AAAA,EAED;AAAA;AAAA;AAAA;AAAA,EAKA,cAAe,eAAe,eAAgB;AAE7C,UAAM,WAAW,CAAC;AAElB,kBAAc,SAAS,QAAS,SAAW,OAAQ;AAElD,YAAM,WAAW,cAAe,MAAM,EAAG;AAEzC,UAAK,SAAS,aAAa,UAAY;AAEvC,YAAM,UAAU;AAAA,QAEf,IAAI,MAAM;AAAA,QACV,SAAS,CAAC;AAAA,QACV,SAAS,CAAC;AAAA,QACV,eAAe,IAAI,QAAQ,EAAE,UAAW,SAAS,cAAc,CAAE;AAAA;AAAA;AAAA,MAIlE;AAEA,UAAK,aAAa,UAAW;AAE5B,gBAAQ,UAAU,SAAS,QAAQ;AACnC,gBAAQ,UAAU,SAAS,QAAQ;AAAA,MAEpC;AAEA,eAAS,KAAM,OAAQ;AAAA,IAExB,CAAE;AAEF,WAAO;AAAA,MAEN;AAAA,MACA,OAAO,CAAC;AAAA,IAET;AAAA,EAED;AAAA;AAAA,EAGA,kBAAmB,eAAe,eAAgB;AAEjD,UAAM,kBAAkB,CAAC;AAEzB,aAAU,IAAI,GAAG,IAAI,cAAc,SAAS,QAAQ,KAAO;AAE1D,YAAM,QAAQ,cAAc,SAAU,CAAE;AAExC,YAAM,kBAAkB,cAAe,MAAM,EAAG;AAEhD,YAAM,iBAAiB;AAAA,QAEtB,MAAM,gBAAgB;AAAA,QACtB,eAAe,gBAAgB;AAAA,QAC/B,IAAI,gBAAgB;AAAA,QACpB,aAAa,gBAAgB,YAAY;AAAA,MAE1C;AAEA,UAAK,gBAAgB,aAAa,oBAAsB;AAExD,qBAAe,QAAQ,YAAY,IAAK,SAAU,MAAM,EAAG,CAAE,EAAE,SAAS,OAAQ,SAAWE,QAAQ;AAElG,eAAOA,OAAM,iBAAiB;AAAA,MAE/B,CAAE,EAAG,CAAE,EAAE;AAET,sBAAgB,KAAM,cAAe;AAAA,IAEtC;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,WAAY,WAAW,aAAa,aAAc;AAEjD,iBAAa,IAAI,MAAM;AAEvB,UAAM,WAAW,KAAK,YAAa,UAAU,WAAW,aAAa,WAAY;AAEjF,UAAM,aAAa,QAAQ,QAAQ;AAEnC,UAAM,QAAQ;AACd,aAAS,QAAS,SAAW,OAAQ;AAEpC,YAAM,YAAY,WAAY,MAAM,EAAG;AACvC,YAAM,oBAAqB,OAAO,SAAU;AAE5C,YAAM,oBAAoB,YAAY,IAAK,MAAM,EAAG,EAAE;AAEtD,wBAAkB,QAAS,SAAW,YAAa;AAElD,cAAM,SAAS,SAAS,IAAK,WAAW,EAAG;AAC3C,YAAK,WAAW,OAAY,QAAO,IAAK,KAAM;AAAA,MAE/C,CAAE;AAEF,UAAK,MAAM,WAAW,MAAO;AAE5B,mBAAW,IAAK,KAAM;AAAA,MAEvB;AAAA,IAGD,CAAE;AAEF,SAAK,aAAc,UAAU,WAAW,aAAa,QAAS;AAE9D,SAAK,uBAAuB;AAE5B,eAAW,SAAU,SAAW,MAAO;AAEtC,UAAK,KAAK,SAAS,eAAgB;AAElC,YAAK,KAAK,QAAS;AAElB,eAAK,SAAS,cAAc,eAAe,KAAK,OAAO;AACvD,eAAK,SAAS,cAAc,oBAAoB,KAAK,OAAO;AAAA,QAE7D;AAEA,cAAM,YAAY,kBAAmB,KAAK,SAAS,aAAc;AAEjE,aAAK,aAAc,SAAU;AAC7B,aAAK,kBAAkB;AAAA,MAExB;AAAA,IAED,CAAE;AAEF,UAAM,aAAa,IAAI,gBAAgB,EAAE,MAAM;AAG/C,QAAK,WAAW,SAAS,WAAW,KAAK,WAAW,SAAU,CAAE,EAAE,SAAU;AAE3E,iBAAW,SAAU,CAAE,EAAE,aAAa;AACtC,mBAAa,WAAW,SAAU,CAAE;AAAA,IAErC;AAEA,eAAW,aAAa;AAAA,EAEzB;AAAA;AAAA,EAGA,YAAa,WAAW,aAAa,aAAc;AAElD,UAAM,WAAW,oBAAI,IAAI;AACzB,UAAM,aAAa,QAAQ,QAAQ;AAEnC,eAAY,UAAU,YAAa;AAElC,YAAM,KAAK,SAAU,MAAO;AAC5B,YAAM,OAAO,WAAY,MAAO;AAChC,YAAM,gBAAgB,YAAY,IAAK,EAAG;AAE1C,UAAI,QAAQ,KAAK,cAAe,eAAe,WAAW,IAAI,KAAK,QAAS;AAE5E,UAAK,CAAE,OAAQ;AAEd,gBAAS,KAAK,UAAW;AAAA,UAExB,KAAK;AACJ,oBAAQ,KAAK,aAAc,aAAc;AACzC;AAAA,UACD,KAAK;AACJ,oBAAQ,KAAK,YAAa,aAAc;AACxC;AAAA,UACD,KAAK;AACJ,oBAAQ,KAAK,WAAY,eAAe,aAAa,WAAY;AACjE;AAAA,UACD,KAAK;AACJ,oBAAQ,KAAK,YAAa,eAAe,WAAY;AACrD;AAAA,UACD,KAAK;AAAA,UACL,KAAK;AACJ,oBAAQ,IAAI,KAAK;AACjB;AAAA,UACD,KAAK;AAAA,UACL;AACC,oBAAQ,IAAI,MAAM;AAClB;AAAA,QAEF;AAEA,cAAM,OAAO,KAAK,WAAW,gBAAgB,iBAAkB,KAAK,QAAS,IAAI;AACjF,cAAM,SAAS,eAAe,KAAK;AAEnC,cAAM,KAAK;AAAA,MAEZ;AAEA,WAAK,iBAAkB,OAAO,IAAK;AACnC,eAAS,IAAK,IAAI,KAAM;AAAA,IAEzB;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,cAAe,eAAe,WAAW,IAAI,MAAO;AAEnD,QAAI,OAAO;AAEX,kBAAc,QAAQ,QAAS,SAAW,QAAS;AAElD,iBAAY,MAAM,WAAY;AAE7B,cAAM,WAAW,UAAW,EAAG;AAE/B,iBAAS,SAAS,QAAS,SAAW,SAAS,GAAI;AAElD,cAAK,QAAQ,OAAO,OAAO,IAAK;AAE/B,kBAAM,UAAU;AAChB,mBAAO,IAAI,KAAK;AAEhB,iBAAK,YAAY,KAAM,QAAQ,aAAc;AAI7C,iBAAK,OAAO,OAAO,gBAAgB,iBAAkB,IAAK,IAAI;AAC9D,iBAAK,SAAS,eAAe;AAC7B,iBAAK,KAAK;AAEV,qBAAS,MAAO,CAAE,IAAI;AAItB,gBAAK,YAAY,MAAO;AAEvB,mBAAK,IAAK,OAAQ;AAAA,YAEnB;AAAA,UAED;AAAA,QAED,CAAE;AAAA,MAEH;AAAA,IAED,CAAE;AAEF,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,aAAc,eAAgB;AAE7B,QAAI;AACJ,QAAI;AAEJ,kBAAc,SAAS,QAAS,SAAW,OAAQ;AAElD,YAAM,OAAO,QAAQ,QAAQ,cAAe,MAAM,EAAG;AAErD,UAAK,SAAS,QAAY;AAEzB,0BAAkB;AAAA,MAEnB;AAAA,IAED,CAAE;AAEF,QAAK,oBAAoB,QAAY;AAEpC,cAAQ,IAAI,SAAS;AAAA,IAEtB,OAAO;AAEN,UAAI,OAAO;AACX,UAAK,gBAAgB,yBAAyB,UAAa,gBAAgB,qBAAqB,UAAU,GAAI;AAE7G,eAAO;AAAA,MAER;AAEA,UAAI,oBAAoB;AACxB,UAAK,gBAAgB,cAAc,QAAY;AAE9C,4BAAoB,gBAAgB,UAAU,QAAQ;AAAA,MAEvD;AAEA,UAAI,mBAAmB;AACvB,UAAK,gBAAgB,aAAa,QAAY;AAE7C,2BAAmB,gBAAgB,SAAS,QAAQ;AAAA,MAErD;AAGA,UAAI,QAAQ,OAAO;AACnB,UAAI,SAAS,OAAO;AAEpB,UAAK,gBAAgB,gBAAgB,UAAa,gBAAgB,iBAAiB,QAAY;AAE9F,gBAAQ,gBAAgB,YAAY;AACpC,iBAAS,gBAAgB,aAAa;AAAA,MAEvC;AAEA,YAAM,SAAS,QAAQ;AAEvB,UAAI,MAAM;AACV,UAAK,gBAAgB,gBAAgB,QAAY;AAEhD,cAAM,gBAAgB,YAAY;AAAA,MAEnC;AAEA,YAAM,cAAc,gBAAgB,cAAc,gBAAgB,YAAY,QAAQ;AAEtF,cAAS,MAAO;AAAA,QAEf,KAAK;AACJ,kBAAQ,IAAI,kBAAmB,KAAK,QAAQ,mBAAmB,gBAAiB;AAChF,cAAK,gBAAgB,KAAO,OAAM,eAAgB,WAAY;AAC9D;AAAA,QAED,KAAK;AACJ,kBAAQ,IAAI,mBAAoB,CAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAE,SAAS,GAAG,mBAAmB,gBAAiB;AACtH;AAAA,QAED;AACC,kBAAQ,KAAM,0CAA0C,OAAO,GAAI;AACnE,kBAAQ,IAAI,SAAS;AACrB;AAAA,MAEF;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,YAAa,eAAgB;AAE5B,QAAI;AACJ,QAAI;AAEJ,kBAAc,SAAS,QAAS,SAAW,OAAQ;AAElD,YAAM,OAAO,QAAQ,QAAQ,cAAe,MAAM,EAAG;AAErD,UAAK,SAAS,QAAY;AAEzB,yBAAiB;AAAA,MAElB;AAAA,IAED,CAAE;AAEF,QAAK,mBAAmB,QAAY;AAEnC,cAAQ,IAAI,SAAS;AAAA,IAEtB,OAAO;AAEN,UAAI;AAGJ,UAAK,eAAe,cAAc,QAAY;AAE7C,eAAO;AAAA,MAER,OAAO;AAEN,eAAO,eAAe,UAAU;AAAA,MAEjC;AAEA,UAAI,QAAQ;AAEZ,UAAK,eAAe,UAAU,QAAY;AAEzC,gBAAQ,IAAI,MAAM,EAAE,UAAW,eAAe,MAAM,KAAM,EAAE,oBAAoB;AAAA,MAEjF;AAEA,UAAI,YAAc,eAAe,cAAc,SAAc,IAAI,eAAe,UAAU,QAAQ;AAGlG,UAAK,eAAe,sBAAsB,UAAa,eAAe,kBAAkB,UAAU,GAAI;AAErG,oBAAY;AAAA,MAEb;AAEA,UAAI,WAAW;AACf,UAAK,eAAe,sBAAsB,QAAY;AAErD,YAAK,eAAe,yBAAyB,UAAa,eAAe,qBAAqB,UAAU,GAAI;AAE3G,qBAAW;AAAA,QAEZ,OAAO;AAEN,qBAAW,eAAe,kBAAkB;AAAA,QAE7C;AAAA,MAED;AAGA,YAAM,QAAQ;AAEd,cAAS,MAAO;AAAA,QAEf,KAAK;AACJ,kBAAQ,IAAI,WAAY,OAAO,WAAW,UAAU,KAAM;AAC1D;AAAA,QAED,KAAK;AACJ,kBAAQ,IAAI,iBAAkB,OAAO,SAAU;AAC/C;AAAA,QAED,KAAK;AACJ,cAAI,QAAQ,KAAK,KAAK;AAEtB,cAAK,eAAe,eAAe,QAAY;AAE9C,oBAAQ,UAAU,SAAU,eAAe,WAAW,KAAM;AAAA,UAE7D;AAEA,cAAI,WAAW;AACf,cAAK,eAAe,eAAe,QAAY;AAK9C,uBAAW,UAAU,SAAU,eAAe,WAAW,KAAM;AAC/D,uBAAW,KAAK,IAAK,UAAU,CAAE;AAAA,UAElC;AAEA,kBAAQ,IAAI,UAAW,OAAO,WAAW,UAAU,OAAO,UAAU,KAAM;AAC1E;AAAA,QAED;AACC,kBAAQ,KAAM,yCAAyC,eAAe,UAAU,QAAQ,+BAAgC;AACxH,kBAAQ,IAAI,WAAY,OAAO,SAAU;AACzC;AAAA,MAEF;AAEA,UAAK,eAAe,gBAAgB,UAAa,eAAe,YAAY,UAAU,GAAI;AAEzF,cAAM,aAAa;AAAA,MAEpB;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,WAAY,eAAe,aAAa,aAAc;AAErD,QAAI;AACJ,QAAI,WAAW;AACf,QAAI,WAAW;AACf,UAAM,YAAY,CAAC;AAGnB,kBAAc,SAAS,QAAS,SAAW,OAAQ;AAElD,UAAK,YAAY,IAAK,MAAM,EAAG,GAAI;AAElC,mBAAW,YAAY,IAAK,MAAM,EAAG;AAAA,MAEtC;AAEA,UAAK,YAAY,IAAK,MAAM,EAAG,GAAI;AAElC,kBAAU,KAAM,YAAY,IAAK,MAAM,EAAG,CAAE;AAAA,MAE7C;AAAA,IAED,CAAE;AAEF,QAAK,UAAU,SAAS,GAAI;AAE3B,iBAAW;AAAA,IAEZ,WAAY,UAAU,SAAS,GAAI;AAElC,iBAAW,UAAW,CAAE;AAAA,IAEzB,OAAO;AAEN,iBAAW,IAAI,kBAAmB;AAAA,QACjC,MAAM,OAAO;AAAA,QACb,OAAO;AAAA,MACR,CAAE;AACF,gBAAU,KAAM,QAAS;AAAA,IAE1B;AAEA,QAAK,WAAW,SAAS,YAAa;AAErC,gBAAU,QAAS,SAAWC,WAAW;AAExC,QAAAA,UAAS,eAAe;AAAA,MAEzB,CAAE;AAAA,IAEH;AAEA,QAAK,SAAS,cAAe;AAE5B,cAAQ,IAAI,YAAa,UAAU,QAAS;AAC5C,YAAM,qBAAqB;AAAA,IAE5B,OAAO;AAEN,cAAQ,IAAI,KAAM,UAAU,QAAS;AAAA,IAEtC;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,YAAa,eAAe,aAAc;AAEzC,UAAM,WAAW,cAAc,SAAS,OAAQ,SAAW,KAAK,OAAQ;AAEvE,UAAK,YAAY,IAAK,MAAM,EAAG,EAAI,OAAM,YAAY,IAAK,MAAM,EAAG;AAEnE,aAAO;AAAA,IAER,GAAG,IAAK;AAGR,UAAM,WAAW,IAAI,kBAAmB;AAAA,MACvC,MAAM,OAAO;AAAA,MACb,OAAO;AAAA,MACP,WAAW;AAAA,IACZ,CAAE;AACF,WAAO,IAAI,KAAM,UAAU,QAAS;AAAA,EAErC;AAAA;AAAA,EAGA,iBAAkB,OAAO,WAAY;AAEpC,UAAM,gBAAgB,CAAC;AAEvB,QAAK,iBAAiB,UAAY,eAAc,cAAc,SAAU,UAAU,YAAY,KAAM;AAEpG,QAAK,mBAAmB,UAAY,eAAc,aAAa,cAAe,UAAU,cAAc,KAAM;AAAA,QACvG,eAAc,aAAa;AAEhC,QAAK,qBAAqB,UAAY,eAAc,cAAc,UAAU,gBAAgB;AAE5F,QAAK,iBAAiB,UAAY,eAAc,cAAc,UAAU,YAAY;AACpF,QAAK,kBAAkB,UAAY,eAAc,WAAW,UAAU,aAAa;AACnF,QAAK,kBAAkB,UAAY,eAAc,eAAe,UAAU,aAAa;AAEvF,QAAK,iBAAiB,UAAY,eAAc,QAAQ,UAAU,YAAY;AAE9E,QAAK,mBAAmB,UAAY,eAAc,gBAAgB,UAAU,cAAc;AAC1F,QAAK,kBAAkB,UAAY,eAAc,eAAe,UAAU,aAAa;AAEvF,QAAK,oBAAoB,UAAY,eAAc,iBAAiB,UAAU,eAAe;AAC7F,QAAK,mBAAmB,UAAY,eAAc,gBAAgB,UAAU,cAAc;AAE1F,UAAM,SAAS,gBAAgB;AAAA,EAEhC;AAAA,EAEA,oBAAqB,OAAO,WAAY;AAEvC,QAAK,oBAAoB,WAAY;AAEpC,YAAM,WAAW,YAAY,IAAK,MAAM,EAAG,EAAE;AAE7C,eAAS,QAAS,SAAW,OAAQ;AAEpC,YAAK,MAAM,iBAAiB,kBAAmB;AAE9C,gBAAM,eAAe,QAAQ,QAAQ,MAAO,MAAM,EAAG;AAErD,cAAK,qBAAqB,cAAe;AAExC,kBAAM,MAAM,aAAa,gBAAgB;AAGzC,gBAAK,MAAM,WAAW,QAAY;AAEjC,oBAAM,OAAO,SAAS,UAAW,GAAI;AACrC,yBAAW,IAAK,MAAM,MAAO;AAAA,YAE9B,OAAO;AAEN,oBAAM,OAAQ,IAAI,QAAQ,EAAE,UAAW,GAAI,CAAE;AAAA,YAE9C;AAAA,UAED;AAAA,QAED;AAAA,MAED,CAAE;AAAA,IAEH;AAAA,EAED;AAAA,EAEA,aAAc,WAAW,aAAa,UAAW;AAEhD,UAAM,eAAe,KAAK,eAAe;AAEzC,eAAY,MAAM,WAAY;AAE7B,YAAM,WAAW,UAAW,EAAG;AAE/B,YAAM,UAAU,YAAY,IAAK,SAAU,SAAS,EAAG,CAAE,EAAE;AAE3D,cAAQ,QAAS,SAAW,QAAS;AAEpC,YAAK,YAAY,IAAK,OAAO,EAAG,GAAI;AAEnC,gBAAM,QAAQ,OAAO;AACrB,gBAAM,mBAAmB,YAAY,IAAK,KAAM;AAEhD,2BAAiB,QAAQ,QAAS,SAAW,eAAgB;AAE5D,gBAAK,SAAS,IAAK,cAAc,EAAG,GAAI;AAEvC,oBAAM,QAAQ,SAAS,IAAK,cAAc,EAAG;AAE7C,oBAAM,KAAM,IAAI,SAAU,SAAS,KAAM,GAAG,aAAc,cAAc,EAAG,CAAE;AAAA,YAE9E;AAAA,UAED,CAAE;AAAA,QAEH;AAAA,MAED,CAAE;AAAA,IAEH;AAAA,EAED;AAAA,EAEA,iBAAiB;AAEhB,UAAM,eAAe,CAAC;AAEtB,QAAK,UAAU,QAAQ,SAAU;AAEhC,YAAM,eAAe,QAAQ,QAAQ;AAErC,iBAAY,UAAU,cAAe;AAEpC,YAAK,aAAc,MAAO,EAAE,aAAa,cAAc,aAAc,MAAO,EAAE,cAAc,GAAI;AAE/F,gBAAM,YAAY,aAAc,MAAO,EAAE;AAEzC,cAAK,MAAM,QAAS,SAAU,GAAI;AAEjC,sBAAU,QAAS,SAAW,UAAW;AAExC,2BAAc,SAAS,IAAK,IAAI,IAAI,QAAQ,EAAE,UAAW,SAAS,OAAO,CAAE;AAAA,YAE5E,CAAE;AAAA,UAEH,OAAO;AAEN,yBAAc,UAAU,IAAK,IAAI,IAAI,QAAQ,EAAE,UAAW,UAAU,OAAO,CAAE;AAAA,UAE9E;AAAA,QAED;AAAA,MAED;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,yBAAyB;AAExB,QAAK,oBAAoB,SAAU;AAElC,UAAK,kBAAkB,QAAQ,gBAAiB;AAI/C,cAAM,eAAe,QAAQ,eAAe,aAAa;AACzD,cAAM,IAAI,aAAc,CAAE;AAC1B,cAAM,IAAI,aAAc,CAAE;AAC1B,cAAM,IAAI,aAAc,CAAE;AAE1B,YAAK,MAAM,KAAK,MAAM,KAAK,MAAM,GAAI;AAEpC,gBAAM,QAAQ,IAAI,MAAO,GAAG,GAAG,CAAE,EAAE,oBAAoB;AACvD,qBAAW,IAAK,IAAI,aAAc,OAAO,CAAE,CAAE;AAAA,QAE9C;AAAA,MAED;AAEA,UAAK,qBAAqB,QAAQ,gBAAiB;AAElD,mBAAW,SAAS,kBAAkB,QAAQ,eAAe,gBAAgB;AAAA,MAE9E;AAAA,IAED;AAAA,EAED;AAED;AAGA,IAAM,iBAAN,MAAqB;AAAA,EAEpB,cAAc;AAEb,SAAK,0BAA0B;AAAA,EAEhC;AAAA;AAAA,EAGA,MAAO,WAAY;AAElB,UAAM,cAAc,oBAAI,IAAI;AAE5B,QAAK,cAAc,QAAQ,SAAU;AAEpC,YAAM,WAAW,QAAQ,QAAQ;AAEjC,iBAAY,UAAU,UAAW;AAEhC,cAAM,gBAAgB,YAAY,IAAK,SAAU,MAAO,CAAE;AAC1D,cAAM,MAAM,KAAK,cAAe,eAAe,SAAU,MAAO,GAAG,SAAU;AAE7E,oBAAY,IAAK,SAAU,MAAO,GAAG,GAAI;AAAA,MAE1C;AAAA,IAED;AAIA,QAAK,KAAK,4BAA4B,MAAO;AAE5C,cAAQ,KAAM,qHAAsH;AAAA,IAErI;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,cAAe,eAAe,SAAS,WAAY;AAElD,YAAS,QAAQ,UAAW;AAAA,MAE3B,KAAK;AACJ,eAAO,KAAK,kBAAmB,eAAe,SAAS,SAAU;AACjE;AAAA,MAED,KAAK;AACJ,eAAO,KAAK,mBAAoB,OAAQ;AACxC;AAAA,IAEF;AAAA,EAED;AAAA;AAAA,EAGA,kBAAmB,eAAe,SAAS,WAAY;AAEtD,UAAM,YAAY,UAAU;AAC5B,UAAM,eAAe,CAAC;AAEtB,UAAM,aAAa,cAAc,QAAQ,IAAK,SAAW,QAAS;AAEjE,aAAO,QAAQ,QAAQ,MAAO,OAAO,EAAG;AAAA,IAEzC,CAAE;AAGF,QAAK,WAAW,WAAW,EAAI;AAE/B,UAAM,WAAW,cAAc,SAAS,OAAQ,SAAWC,WAAU,OAAQ;AAE5E,UAAK,UAAW,MAAM,EAAG,MAAM,OAAY,CAAAA,YAAW,UAAW,MAAM,EAAG;AAE1E,aAAOA;AAAA,IAER,GAAG,IAAK;AAER,kBAAc,SAAS,QAAS,SAAW,OAAQ;AAElD,UAAK,UAAU,aAAc,MAAM,EAAG,MAAM,QAAY;AAEvD,qBAAa,KAAM,UAAU,aAAc,MAAM,EAAG,CAAE;AAAA,MAEvD;AAAA,IAED,CAAE;AAIF,UAAM,YAAY,WAAY,CAAE;AAEhC,UAAM,gBAAgB,CAAC;AAEvB,QAAK,mBAAmB,UAAY,eAAc,aAAa,cAAe,UAAU,cAAc,KAAM;AAC5G,QAAK,iBAAiB,UAAY,eAAc,cAAc,SAAU,UAAU,YAAY,KAAM;AAEpG,QAAK,0BAA0B,UAAY,eAAc,cAAc,UAAU,qBAAqB;AACtG,QAAK,uBAAuB,UAAY,eAAc,WAAW,UAAU,kBAAkB;AAC7F,QAAK,sBAAsB,UAAY,eAAc,QAAQ,UAAU,iBAAiB;AAExF,UAAM,YAAY,kBAAmB,aAAc;AAEnD,WAAO,KAAK,YAAa,SAAS,UAAU,cAAc,SAAU;AAAA,EAErE;AAAA;AAAA,EAGA,YAAa,SAAS,UAAU,cAAc,cAAe;AAE5D,UAAM,MAAM,IAAI,eAAe;AAC/B,QAAK,QAAQ,SAAW,KAAI,OAAO,QAAQ;AAE3C,UAAM,UAAU,KAAK,aAAc,SAAS,QAAS;AACrD,UAAM,UAAU,KAAK,WAAY,OAAQ;AAEzC,UAAM,oBAAoB,IAAI,uBAAwB,QAAQ,QAAQ,CAAE;AAExE,sBAAkB,aAAc,YAAa;AAE7C,QAAI,aAAc,YAAY,iBAAkB;AAEhD,QAAK,QAAQ,OAAO,SAAS,GAAI;AAEhC,UAAI,aAAc,SAAS,IAAI,uBAAwB,QAAQ,QAAQ,CAAE,CAAE;AAAA,IAE5E;AAEA,QAAK,UAAW;AAEf,UAAI,aAAc,aAAa,IAAI,sBAAuB,QAAQ,gBAAgB,CAAE,CAAE;AAEtF,UAAI,aAAc,cAAc,IAAI,uBAAwB,QAAQ,eAAe,CAAE,CAAE;AAGvF,UAAI,eAAe;AAAA,IAEpB;AAEA,QAAK,QAAQ,OAAO,SAAS,GAAI;AAEhC,YAAM,eAAe,IAAI,QAAQ,EAAE,gBAAiB,YAAa;AAEjE,YAAM,kBAAkB,IAAI,uBAAwB,QAAQ,QAAQ,CAAE;AACtE,sBAAgB,kBAAmB,YAAa;AAEhD,UAAI,aAAc,UAAU,eAAgB;AAAA,IAE7C;AAEA,YAAQ,IAAI,QAAS,SAAW,UAAU,GAAI;AAE7C,YAAM,OAAO,MAAM,IAAI,OAAO,KAAM,CAAE;AAEtC,UAAI,aAAc,MAAM,IAAI,uBAAwB,QAAQ,IAAK,CAAE,GAAG,CAAE,CAAE;AAAA,IAE3E,CAAE;AAEF,QAAK,QAAQ,YAAY,QAAQ,SAAS,gBAAgB,WAAY;AAGrE,UAAI,oBAAoB,QAAQ,cAAe,CAAE;AACjD,UAAI,aAAa;AAEjB,cAAQ,cAAc,QAAS,SAAW,cAAc,GAAI;AAE3D,YAAK,iBAAiB,mBAAoB;AAEzC,cAAI,SAAU,YAAY,IAAI,YAAY,iBAAkB;AAE5D,8BAAoB;AACpB,uBAAa;AAAA,QAEd;AAAA,MAED,CAAE;AAGF,UAAK,IAAI,OAAO,SAAS,GAAI;AAE5B,cAAM,YAAY,IAAI,OAAQ,IAAI,OAAO,SAAS,CAAE;AACpD,cAAM,YAAY,UAAU,QAAQ,UAAU;AAE9C,YAAK,cAAc,QAAQ,cAAc,QAAS;AAEjD,cAAI,SAAU,WAAW,QAAQ,cAAc,SAAS,WAAW,iBAAkB;AAAA,QAEtF;AAAA,MAED;AAIA,UAAK,IAAI,OAAO,WAAW,GAAI;AAE9B,YAAI,SAAU,GAAG,QAAQ,cAAc,QAAQ,QAAQ,cAAe,CAAE,CAAE;AAAA,MAE3E;AAAA,IAED;AAEA,SAAK,gBAAiB,KAAK,SAAS,cAAc,YAAa;AAE/D,WAAO;AAAA,EAER;AAAA,EAEA,aAAc,SAAS,UAAW;AAEjC,UAAM,UAAU,CAAC;AAEjB,YAAQ,kBAAoB,QAAQ,aAAa,SAAc,QAAQ,SAAS,IAAI,CAAC;AACrF,YAAQ,gBAAkB,QAAQ,uBAAuB,SAAc,QAAQ,mBAAmB,IAAI,CAAC;AAEvG,QAAK,QAAQ,mBAAoB;AAEhC,cAAQ,QAAQ,KAAK,kBAAmB,QAAQ,kBAAmB,CAAE,CAAE;AAAA,IAExE;AAEA,QAAK,QAAQ,sBAAuB;AAEnC,cAAQ,WAAW,KAAK,qBAAsB,QAAQ,qBAAsB,CAAE,CAAE;AAAA,IAEjF;AAEA,QAAK,QAAQ,oBAAqB;AAEjC,cAAQ,SAAS,KAAK,aAAc,QAAQ,mBAAoB,CAAE,CAAE;AAAA,IAErE;AAEA,QAAK,QAAQ,gBAAiB;AAE7B,cAAQ,KAAK,CAAC;AAEd,UAAI,IAAI;AACR,aAAQ,QAAQ,eAAgB,CAAE,GAAI;AAErC,YAAK,QAAQ,eAAgB,CAAE,EAAE,IAAK;AAErC,kBAAQ,GAAG,KAAM,KAAK,SAAU,QAAQ,eAAgB,CAAE,CAAE,CAAE;AAAA,QAE/D;AAEA;AAAA,MAED;AAAA,IAED;AAEA,YAAQ,cAAc,CAAC;AAEvB,QAAK,aAAa,MAAO;AAExB,cAAQ,WAAW;AAEnB,eAAS,SAAS,QAAS,SAAW,SAAS,GAAI;AAGlD,gBAAQ,QAAQ,QAAS,SAAW,OAAO,GAAI;AAE9C,cAAK,QAAQ,YAAa,KAAM,MAAM,OAAY,SAAQ,YAAa,KAAM,IAAI,CAAC;AAElF,kBAAQ,YAAa,KAAM,EAAE,KAAM;AAAA,YAElC,IAAI;AAAA,YACJ,QAAQ,QAAQ,QAAS,CAAE;AAAA,UAE5B,CAAE;AAAA,QAEH,CAAE;AAAA,MAEH,CAAE;AAAA,IAEH;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,WAAY,SAAU;AAErB,UAAM,UAAU;AAAA,MACf,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,KAAK,CAAC;AAAA,MACN,eAAe,CAAC;AAAA,MAChB,eAAe,CAAC;AAAA,MAChB,gBAAgB,CAAC;AAAA,IAClB;AAEA,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,0BAA0B;AAG9B,QAAI,sBAAsB,CAAC;AAC3B,QAAI,cAAc,CAAC;AACnB,QAAI,aAAa,CAAC;AAClB,QAAI,UAAU,CAAC;AACf,QAAI,cAAc,CAAC;AACnB,QAAI,oBAAoB,CAAC;AAEzB,UAAM,QAAQ;AACd,YAAQ,cAAc,QAAS,SAAW,aAAa,oBAAqB;AAE3E,UAAI;AACJ,UAAI,YAAY;AAShB,UAAK,cAAc,GAAI;AAEtB,sBAAc,cAAc;AAC5B,oBAAY;AAAA,MAEb;AAEA,UAAI,gBAAgB,CAAC;AACrB,UAAI,UAAU,CAAC;AAEf,0BAAoB,KAAM,cAAc,GAAG,cAAc,IAAI,GAAG,cAAc,IAAI,CAAE;AAEpF,UAAK,QAAQ,OAAQ;AAEpB,cAAM,OAAO,QAAS,oBAAoB,cAAc,aAAa,QAAQ,KAAM;AAEnF,mBAAW,KAAM,KAAM,CAAE,GAAG,KAAM,CAAE,GAAG,KAAM,CAAE,CAAE;AAAA,MAElD;AAEA,UAAK,QAAQ,UAAW;AAEvB,YAAK,QAAQ,YAAa,WAAY,MAAM,QAAY;AAEvD,kBAAQ,YAAa,WAAY,EAAE,QAAS,SAAW,IAAK;AAE3D,oBAAQ,KAAM,GAAG,MAAO;AACxB,0BAAc,KAAM,GAAG,EAAG;AAAA,UAE3B,CAAE;AAAA,QAGH;AAEA,YAAK,QAAQ,SAAS,GAAI;AAEzB,cAAK,CAAE,yBAA0B;AAEhC,oBAAQ,KAAM,2GAA4G;AAC1H,sCAA0B;AAAA,UAE3B;AAEA,gBAAM,SAAS,CAAE,GAAG,GAAG,GAAG,CAAE;AAC5B,gBAAM,SAAS,CAAE,GAAG,GAAG,GAAG,CAAE;AAE5B,kBAAQ,QAAS,SAAW,QAAQ,aAAc;AAEjD,gBAAI,gBAAgB;AACpB,gBAAI,eAAe,cAAe,WAAY;AAE9C,mBAAO,QAAS,SAAW,gBAAgB,qBAAqB,qBAAsB;AAErF,kBAAK,gBAAgB,gBAAiB;AAErC,oCAAqB,mBAAoB,IAAI;AAC7C,gCAAgB;AAEhB,sBAAM,MAAM,OAAQ,mBAAoB;AACxC,uBAAQ,mBAAoB,IAAI;AAChC,+BAAe;AAAA,cAEhB;AAAA,YAED,CAAE;AAAA,UAEH,CAAE;AAEF,0BAAgB;AAChB,oBAAU;AAAA,QAEX;AAGA,eAAQ,QAAQ,SAAS,GAAI;AAE5B,kBAAQ,KAAM,CAAE;AAChB,wBAAc,KAAM,CAAE;AAAA,QAEvB;AAEA,iBAAU,IAAI,GAAG,IAAI,GAAG,EAAG,GAAI;AAE9B,sBAAY,KAAM,QAAS,CAAE,CAAE;AAC/B,4BAAkB,KAAM,cAAe,CAAE,CAAE;AAAA,QAE5C;AAAA,MAED;AAEA,UAAK,QAAQ,QAAS;AAErB,cAAM,OAAO,QAAS,oBAAoB,cAAc,aAAa,QAAQ,MAAO;AAEpF,oBAAY,KAAM,KAAM,CAAE,GAAG,KAAM,CAAE,GAAG,KAAM,CAAE,CAAE;AAAA,MAEnD;AAEA,UAAK,QAAQ,YAAY,QAAQ,SAAS,gBAAgB,WAAY;AAErE,wBAAgB,QAAS,oBAAoB,cAAc,aAAa,QAAQ,QAAS,EAAG,CAAE;AAE9F,YAAK,gBAAgB,GAAI;AAExB,gBAAM,0BAA0B;AAChC,0BAAgB;AAAA,QAEjB;AAAA,MAED;AAEA,UAAK,QAAQ,IAAK;AAEjB,gBAAQ,GAAG,QAAS,SAAW,IAAI,GAAI;AAEtC,gBAAM,OAAO,QAAS,oBAAoB,cAAc,aAAa,EAAG;AAExE,cAAK,QAAS,CAAE,MAAM,QAAY;AAEjC,oBAAS,CAAE,IAAI,CAAC;AAAA,UAEjB;AAEA,kBAAS,CAAE,EAAE,KAAM,KAAM,CAAE,CAAE;AAC7B,kBAAS,CAAE,EAAE,KAAM,KAAM,CAAE,CAAE;AAAA,QAE9B,CAAE;AAAA,MAEH;AAEA;AAEA,UAAK,WAAY;AAEhB,cAAM,QAAS,SAAS,SAAS,qBAAqB,eAAe,aAAa,YAAY,SAAS,aAAa,mBAAmB,UAAW;AAElJ;AACA,qBAAa;AAGb,8BAAsB,CAAC;AACvB,sBAAc,CAAC;AACf,qBAAa,CAAC;AACd,kBAAU,CAAC;AACX,sBAAc,CAAC;AACf,4BAAoB,CAAC;AAAA,MAEtB;AAAA,IAED,CAAE;AAEF,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,gBAAiB,UAAW;AAE3B,UAAM,SAAS,IAAI,QAAS,GAAK,GAAK,CAAI;AAE1C,aAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,YAAM,UAAU,SAAU,CAAE;AAC5B,YAAM,OAAO,UAAY,IAAI,KAAM,SAAS,MAAO;AAEnD,aAAO,MAAO,QAAQ,IAAI,KAAK,MAAQ,QAAQ,IAAI,KAAK;AACxD,aAAO,MAAO,QAAQ,IAAI,KAAK,MAAQ,QAAQ,IAAI,KAAK;AACxD,aAAO,MAAO,QAAQ,IAAI,KAAK,MAAQ,QAAQ,IAAI,KAAK;AAAA,IAEzD;AAEA,WAAO,UAAU;AAEjB,WAAO;AAAA,EAER;AAAA,EAEA,6BAA8B,UAAW;AAExC,UAAM,eAAe,KAAK,gBAAiB,QAAS;AAEpD,UAAM,KAAK,KAAK,IAAK,aAAa,CAAE,IAAI,MAAM,IAAI,QAAS,GAAK,GAAK,CAAI,IAAI,IAAI,QAAS,GAAK,GAAK,CAAI;AACxG,UAAM,UAAU,GAAG,MAAO,YAAa,EAAE,UAAU;AACnD,UAAM,YAAY,aAAa,MAAM,EAAE,MAAO,OAAQ,EAAE,UAAU;AAElE,WAAO;AAAA,MACN,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAAA,EAEA,cAAe,QAAQ,eAAe,iBAAkB;AAEvD,WAAO,IAAI;AAAA,MACV,OAAO,IAAK,aAAc;AAAA,MAC1B,OAAO,IAAK,eAAgB;AAAA,IAC7B;AAAA,EAED;AAAA;AAAA,EAGA,QAAS,SAAS,SAAS,qBAAqB,eAAe,aAAa,YAAY,SAAS,aAAa,mBAAmB,YAAa;AAE7I,QAAI;AAEJ,QAAK,aAAa,GAAI;AAIrB,YAAM,WAAW,CAAC;AAElB,eAAU,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK,GAAI;AAEzD,iBAAS,KAAM,IAAI;AAAA,UAClB,QAAQ,gBAAiB,oBAAqB,CAAE,CAAE;AAAA,UAClD,QAAQ,gBAAiB,oBAAqB,IAAI,CAAE,CAAE;AAAA,UACtD,QAAQ,gBAAiB,oBAAqB,IAAI,CAAE,CAAE;AAAA,QACvD,CAAE;AAAA,MAEH;AAEA,YAAM,EAAE,SAAS,UAAU,IAAI,KAAK,6BAA8B,QAAS;AAC3E,YAAM,qBAAqB,CAAC;AAE5B,iBAAY,UAAU,UAAW;AAEhC,2BAAmB,KAAM,KAAK,cAAe,QAAQ,SAAS,SAAU,CAAE;AAAA,MAE3E;AAEA,kBAAY,WAAW,iBAAkB,oBAAoB,CAAC,CAAE;AAAA,IAEjE,OAAO;AAGN,kBAAY,CAAC,CAAE,GAAG,GAAG,CAAE,CAAC;AAAA,IAEzB;AAEA,eAAY,CAAE,IAAI,IAAI,EAAG,KAAK,WAAY;AAEzC,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,CAAE,CAAE,CAAE;AAC9E,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAClF,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAElF,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,CAAE,CAAE,CAAE;AAC9E,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAClF,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAElF,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,CAAE,CAAE,CAAE;AAC9E,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAClF,cAAQ,OAAO,KAAM,QAAQ,gBAAiB,oBAAqB,KAAK,IAAI,CAAE,CAAE,CAAE;AAElF,UAAK,QAAQ,UAAW;AAEvB,gBAAQ,cAAc,KAAM,YAAa,KAAK,CAAE,CAAE;AAClD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAEtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,CAAE,CAAE;AAClD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAEtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,CAAE,CAAE;AAClD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AACtD,gBAAQ,cAAc,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAEtD,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,CAAE,CAAE;AACzD,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAE7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,CAAE,CAAE;AACzD,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAE7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,CAAE,CAAE;AACzD,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAC7D,gBAAQ,eAAe,KAAM,kBAAmB,KAAK,IAAI,CAAE,CAAE;AAAA,MAE9D;AAEA,UAAK,QAAQ,OAAQ;AAEpB,gBAAQ,OAAO,KAAM,WAAY,KAAK,CAAE,CAAE;AAC1C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAC9C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAE9C,gBAAQ,OAAO,KAAM,WAAY,KAAK,CAAE,CAAE;AAC1C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAC9C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAE9C,gBAAQ,OAAO,KAAM,WAAY,KAAK,CAAE,CAAE;AAC1C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAC9C,gBAAQ,OAAO,KAAM,WAAY,KAAK,IAAI,CAAE,CAAE;AAAA,MAE/C;AAEA,UAAK,QAAQ,YAAY,QAAQ,SAAS,gBAAgB,WAAY;AAErE,gBAAQ,cAAc,KAAM,aAAc;AAC1C,gBAAQ,cAAc,KAAM,aAAc;AAC1C,gBAAQ,cAAc,KAAM,aAAc;AAAA,MAE3C;AAEA,UAAK,QAAQ,QAAS;AAErB,gBAAQ,OAAO,KAAM,YAAa,KAAK,CAAE,CAAE;AAC3C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAC/C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAE/C,gBAAQ,OAAO,KAAM,YAAa,KAAK,CAAE,CAAE;AAC3C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAC/C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAE/C,gBAAQ,OAAO,KAAM,YAAa,KAAK,CAAE,CAAE;AAC3C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAC/C,gBAAQ,OAAO,KAAM,YAAa,KAAK,IAAI,CAAE,CAAE;AAAA,MAEhD;AAEA,UAAK,QAAQ,IAAK;AAEjB,gBAAQ,GAAG,QAAS,SAAW,IAAI,GAAI;AAEtC,cAAK,QAAQ,IAAK,CAAE,MAAM,OAAY,SAAQ,IAAK,CAAE,IAAI,CAAC;AAE1D,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,CAAE,CAAE;AAC9C,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,IAAI,CAAE,CAAE;AAElD,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,CAAE,CAAE;AAC9C,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,IAAI,CAAE,CAAE;AAElD,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,CAAE,CAAE;AAC9C,kBAAQ,IAAK,CAAE,EAAE,KAAM,QAAS,CAAE,EAAG,KAAK,IAAI,CAAE,CAAE;AAAA,QAEnD,CAAE;AAAA,MAEH;AAAA,IAED;AAAA,EAED;AAAA,EAEA,gBAAiB,WAAW,eAAe,cAAc,cAAe;AAEvE,QAAK,aAAa,WAAW,EAAI;AAEjC,cAAU,uBAAuB;AAEjC,cAAU,gBAAgB,WAAW,CAAC;AAGtC,UAAM,QAAQ;AACd,iBAAa,QAAS,SAAW,aAAc;AAE9C,kBAAY,WAAW,QAAS,SAAW,WAAY;AAEtD,cAAM,eAAe,QAAQ,QAAQ,SAAU,UAAU,KAAM;AAE/D,YAAK,iBAAiB,QAAY;AAEjC,gBAAM,iBAAkB,WAAW,eAAe,cAAc,cAAc,UAAU,IAAK;AAAA,QAE9F;AAAA,MAED,CAAE;AAAA,IAEH,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAkB,WAAW,eAAe,cAAc,cAAc,MAAO;AAE9E,UAAM,gBAAkB,cAAc,uBAAuB,SAAc,cAAc,mBAAmB,IAAI,CAAC;AAEjH,UAAM,uBAAyB,aAAa,aAAa,SAAc,aAAa,SAAS,IAAI,CAAC;AAClG,UAAM,UAAY,aAAa,YAAY,SAAc,aAAa,QAAQ,IAAI,CAAC;AAEnF,UAAM,SAAS,UAAU,WAAW,SAAS,QAAQ;AACrD,UAAM,iBAAiB,IAAI,aAAc,MAAO;AAEhD,aAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAO;AAE3C,YAAM,aAAa,QAAS,CAAE,IAAI;AAElC,qBAAgB,UAAW,IAAI,qBAAsB,IAAI,CAAE;AAC3D,qBAAgB,aAAa,CAAE,IAAI,qBAAsB,IAAI,IAAI,CAAE;AACnE,qBAAgB,aAAa,CAAE,IAAI,qBAAsB,IAAI,IAAI,CAAE;AAAA,IAEpE;AAGA,UAAM,eAAe;AAAA,MACpB;AAAA,MACA,iBAAiB;AAAA,IAElB;AAEA,UAAM,eAAe,KAAK,WAAY,YAAa;AAEnD,UAAM,oBAAoB,IAAI,uBAAwB,aAAa,QAAQ,CAAE;AAC7E,sBAAkB,OAAO,QAAQ,aAAa;AAE9C,sBAAkB,aAAc,YAAa;AAE7C,cAAU,gBAAgB,SAAS,KAAM,iBAAkB;AAAA,EAE5D;AAAA;AAAA,EAGA,aAAc,YAAa;AAE1B,UAAM,cAAc,WAAW;AAC/B,UAAM,gBAAgB,WAAW;AACjC,UAAM,SAAS,WAAW,QAAQ;AAClC,QAAI,cAAc,CAAC;AACnB,QAAK,kBAAkB,iBAAkB;AAExC,UAAK,iBAAiB,YAAa;AAElC,sBAAc,WAAW,YAAY;AAAA,MAEtC,WAAY,kBAAkB,YAAa;AAE1C,sBAAc,WAAW,aAAa;AAAA,MAEvC;AAAA,IAED;AAEA,WAAO;AAAA,MACN,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAAA;AAAA,EAGA,SAAU,QAAS;AAElB,UAAM,cAAc,OAAO;AAC3B,UAAM,gBAAgB,OAAO;AAC7B,UAAM,SAAS,OAAO,GAAG;AACzB,QAAI,cAAc,CAAC;AACnB,QAAK,kBAAkB,iBAAkB;AAExC,oBAAc,OAAO,QAAQ;AAAA,IAE9B;AAEA,WAAO;AAAA,MACN,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAAA;AAAA,EAGA,kBAAmB,WAAY;AAE9B,UAAM,cAAc,UAAU;AAC9B,UAAM,gBAAgB,UAAU;AAChC,UAAM,SAAS,UAAU,OAAO;AAChC,QAAI,cAAc,CAAC;AACnB,QAAK,kBAAkB,iBAAkB;AAExC,oBAAc,UAAU,WAAW;AAAA,IAEpC;AAEA,aAAU,IAAI,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAI;AAE7D,QAAE,UAAW,QAAQ,CAAE,EAAE,oBAAoB,EAAE,QAAS,QAAQ,CAAE;AAAA,IAEnE;AAEA,WAAO;AAAA,MACN,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAAA;AAAA,EAGA,qBAAsB,cAAe;AAEpC,UAAM,cAAc,aAAa;AACjC,UAAM,gBAAgB,aAAa;AAEnC,QAAK,gBAAgB,wBAAyB;AAE7C,aAAO;AAAA,QACN,UAAU;AAAA,QACV,QAAQ,CAAE,CAAE;AAAA,QACZ,SAAS,CAAE,CAAE;AAAA,QACb,aAAa;AAAA,QACb;AAAA,MACD;AAAA,IAED;AAEA,UAAM,sBAAsB,aAAa,UAAU;AAKnD,UAAM,kBAAkB,CAAC;AAEzB,aAAU,IAAI,GAAG,IAAI,oBAAoB,QAAQ,EAAG,GAAI;AAEvD,sBAAgB,KAAM,CAAE;AAAA,IAEzB;AAEA,WAAO;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACD;AAAA,EAED;AAAA;AAAA,EAGA,mBAAoB,SAAU;AAE7B,UAAM,QAAQ,SAAU,QAAQ,KAAM;AAEtC,QAAK,MAAO,KAAM,GAAI;AAErB,cAAQ,MAAO,+DAA+D,QAAQ,OAAO,QAAQ,EAAG;AACxG,aAAO,IAAI,eAAe;AAAA,IAE3B;AAEA,UAAM,SAAS,QAAQ;AAEvB,UAAM,QAAQ,QAAQ,WAAW;AACjC,UAAM,gBAAgB,CAAC;AACvB,UAAM,eAAe,QAAQ,OAAO;AAEpC,aAAU,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK,GAAI;AAEzD,oBAAc,KAAM,IAAI,QAAQ,EAAE,UAAW,cAAc,CAAE,CAAE;AAAA,IAEhE;AAEA,QAAI,WAAW;AAEf,QAAK,QAAQ,SAAS,UAAW;AAEhC,oBAAc,KAAM,cAAe,CAAE,CAAE;AAAA,IAExC,WAAY,QAAQ,SAAS,YAAa;AAEzC,kBAAY;AACZ,gBAAU,MAAM,SAAS,IAAI;AAE7B,eAAU,IAAI,GAAG,IAAI,QAAQ,EAAG,GAAI;AAEnC,sBAAc,KAAM,cAAe,CAAE,CAAE;AAAA,MAExC;AAAA,IAED;AAEA,UAAM,QAAQ,IAAI,WAAY,QAAQ,OAAO,eAAe,WAAW,OAAQ;AAC/E,UAAM,SAAS,MAAM,UAAW,cAAc,SAAS,EAAG;AAE1D,WAAO,IAAI,eAAe,EAAE,cAAe,MAAO;AAAA,EAEnD;AAED;AAGA,IAAM,kBAAN,MAAsB;AAAA;AAAA,EAGrB,QAAQ;AAEP,UAAM,iBAAiB,CAAC;AAExB,UAAM,WAAW,KAAK,WAAW;AAEjC,QAAK,aAAa,QAAY;AAE7B,iBAAY,OAAO,UAAW;AAE7B,cAAM,UAAU,SAAU,GAAI;AAE9B,cAAM,OAAO,KAAK,QAAS,OAAQ;AAEnC,uBAAe,KAAM,IAAK;AAAA,MAE3B;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,aAAa;AAIZ,QAAK,QAAQ,QAAQ,mBAAmB,OAAY,QAAO;AAE3D,UAAM,gBAAgB,KAAK,yBAAyB;AAEpD,SAAK,qBAAsB,aAAc;AAEzC,UAAM,YAAY,KAAK,qBAAsB,aAAc;AAC3D,UAAM,WAAW,KAAK,gBAAiB,SAAU;AAEjD,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AAE1B,UAAM,gBAAgB,QAAQ,QAAQ;AAEtC,UAAM,gBAAgB,oBAAI,IAAI;AAE9B,eAAY,UAAU,eAAgB;AAErC,YAAM,eAAe,cAAe,MAAO;AAE3C,UAAK,aAAa,SAAS,MAAO,qBAAsB,MAAM,MAAO;AAEpE,cAAM,YAAY;AAAA,UAEjB,IAAI,aAAa;AAAA,UACjB,MAAM,aAAa;AAAA,UACnB,QAAQ,CAAC;AAAA,QAEV;AAEA,sBAAc,IAAK,UAAU,IAAI,SAAU;AAAA,MAE5C;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAsB,eAAgB;AAErC,UAAM,YAAY,QAAQ,QAAQ;AASlC,eAAY,UAAU,WAAY;AAEjC,YAAM,iBAAiB;AAAA,QAEtB,IAAI,UAAW,MAAO,EAAE;AAAA,QACxB,OAAO,UAAW,MAAO,EAAE,QAAQ,EAAE,IAAK,uBAAwB;AAAA,QAClE,QAAQ,UAAW,MAAO,EAAE,cAAc;AAAA,MAE3C;AAEA,YAAM,gBAAgB,YAAY,IAAK,eAAe,EAAG;AAEzD,UAAK,kBAAkB,QAAY;AAElC,cAAM,mBAAmB,cAAc,QAAS,CAAE,EAAE;AACpD,cAAM,6BAA6B,cAAc,QAAS,CAAE,EAAE;AAE9D,YAAK,2BAA2B,MAAO,GAAI,GAAI;AAE9C,wBAAc,IAAK,gBAAiB,EAAE,OAAQ,GAAI,IAAI;AAAA,QAEvD,WAAY,2BAA2B,MAAO,GAAI,GAAI;AAErD,wBAAc,IAAK,gBAAiB,EAAE,OAAQ,GAAI,IAAI;AAAA,QAEvD,WAAY,2BAA2B,MAAO,GAAI,GAAI;AAErD,wBAAc,IAAK,gBAAiB,EAAE,OAAQ,GAAI,IAAI;AAAA,QAEvD,WAAY,2BAA2B,MAAO,eAAgB,KAAK,cAAc,IAAK,gBAAiB,GAAI;AAE1G,wBAAc,IAAK,gBAAiB,EAAE,OAAQ,OAAQ,IAAI;AAAA,QAE3D;AAAA,MAED;AAAA,IAED;AAAA,EAED;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAsB,eAAgB;AAErC,UAAM,YAAY,QAAQ,QAAQ;AAElC,UAAM,YAAY,oBAAI,IAAI;AAE1B,eAAY,UAAU,WAAY;AAEjC,YAAM,kBAAkB,CAAC;AAEzB,YAAM,aAAa,YAAY,IAAK,SAAU,MAAO,CAAE;AAEvD,UAAK,eAAe,QAAY;AAG/B,cAAM,WAAW,WAAW;AAE5B,iBAAS,QAAS,SAAW,OAAO,GAAI;AAEvC,cAAK,cAAc,IAAK,MAAM,EAAG,GAAI;AAEpC,kBAAM,YAAY,cAAc,IAAK,MAAM,EAAG;AAG9C,gBAAK,UAAU,OAAO,MAAM,UAAa,UAAU,OAAO,MAAM,UAAa,UAAU,OAAO,MAAM,QAAY;AAE/G,kBAAK,gBAAiB,CAAE,MAAM,QAAY;AAEzC,sBAAM,UAAU,YAAY,IAAK,MAAM,EAAG,EAAE,QAAQ,OAAQ,SAAW,QAAS;AAE/E,yBAAO,OAAO,iBAAiB;AAAA,gBAEhC,CAAE,EAAG,CAAE,EAAE;AAET,oBAAK,YAAY,QAAY;AAE5B,wBAAM,WAAW,QAAQ,QAAQ,MAAO,QAAQ,SAAS,CAAE;AAE3D,sBAAK,aAAa,QAAY;AAE7B,4BAAQ,KAAM,gDAAgD,KAAM;AACpE;AAAA,kBAED;AAEA,wBAAM,OAAO;AAAA,oBAEZ,WAAW,SAAS,WAAW,gBAAgB,iBAAkB,SAAS,QAAS,IAAI;AAAA,oBACvF,IAAI,SAAS;AAAA,oBACb,iBAAiB,CAAE,GAAG,GAAG,CAAE;AAAA,oBAC3B,iBAAiB,CAAE,GAAG,GAAG,CAAE;AAAA,oBAC3B,cAAc,CAAE,GAAG,GAAG,CAAE;AAAA,kBAEzB;AAEA,6BAAW,SAAU,SAAWF,QAAQ;AAEvC,wBAAKA,OAAM,OAAO,SAAS,IAAK;AAE/B,2BAAK,YAAYA,OAAM;AAEvB,0BAAKA,OAAM,SAAS,cAAgB,MAAK,aAAaA,OAAM,SAAS,cAAc;AAAA,oBAEpF;AAAA,kBAED,CAAE;AAEF,sBAAK,CAAE,KAAK,UAAY,MAAK,YAAY,IAAI,QAAQ;AAIrD,sBAAK,iBAAiB,SAAW,MAAK,cAAc,SAAS,YAAY;AACzE,sBAAK,kBAAkB,SAAW,MAAK,eAAe,SAAS,aAAa;AAE5E,kCAAiB,CAAE,IAAI;AAAA,gBAExB;AAAA,cAED;AAEA,kBAAK,gBAAiB,CAAE,EAAI,iBAAiB,CAAE,EAAG,UAAU,IAAK,IAAI;AAAA,YAEtE,WAAY,UAAU,OAAO,UAAU,QAAY;AAElD,kBAAK,gBAAiB,CAAE,MAAM,QAAY;AAEzC,sBAAM,aAAa,YAAY,IAAK,MAAM,EAAG,EAAE,QAAQ,OAAQ,SAAW,QAAS;AAElF,yBAAO,OAAO,iBAAiB;AAAA,gBAEhC,CAAE,EAAG,CAAE,EAAE;AAET,sBAAM,YAAY,YAAY,IAAK,UAAW,EAAE,QAAS,CAAE,EAAE;AAC7D,sBAAM,QAAQ,YAAY,IAAK,SAAU,EAAE,QAAS,CAAE,EAAE;AAGxD,sBAAM,UAAU,YAAY,IAAK,KAAM,EAAE,QAAS,CAAE,EAAE;AAEtD,sBAAM,WAAW,QAAQ,QAAQ,MAAO,OAAQ;AAEhD,sBAAM,OAAO;AAAA,kBAEZ,WAAW,SAAS,WAAW,gBAAgB,iBAAkB,SAAS,QAAS,IAAI;AAAA,kBACvF,WAAW,QAAQ,QAAQ,SAAU,UAAW,EAAE;AAAA,gBAEnD;AAEA,gCAAiB,CAAE,IAAI;AAAA,cAExB;AAEA,8BAAiB,CAAE,EAAG,UAAU,IAAK,IAAI;AAAA,YAE1C;AAAA,UAED;AAAA,QAED,CAAE;AAEF,kBAAU,IAAK,SAAU,MAAO,GAAG,eAAgB;AAAA,MAEpD;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA,EAIA,gBAAiB,WAAY;AAE5B,UAAM,YAAY,QAAQ,QAAQ;AAGlC,UAAM,WAAW,CAAC;AAElB,eAAY,UAAU,WAAY;AAEjC,YAAM,WAAW,YAAY,IAAK,SAAU,MAAO,CAAE,EAAE;AAEvD,UAAK,SAAS,SAAS,GAAI;AAI1B,gBAAQ,KAAM,oIAAqI;AAAA,MAEpJ;AAEA,YAAM,QAAQ,UAAU,IAAK,SAAU,CAAE,EAAE,EAAG;AAE9C,eAAU,MAAO,IAAI;AAAA,QAEpB,MAAM,UAAW,MAAO,EAAE;AAAA,QAC1B;AAAA,MAED;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,QAAS,SAAU;AAElB,QAAI,SAAS,CAAC;AAEd,UAAM,QAAQ;AACd,YAAQ,MAAM,QAAS,SAAW,WAAY;AAE7C,eAAS,OAAO,OAAQ,MAAM,eAAgB,SAAU,CAAE;AAAA,IAE3D,CAAE;AAEF,WAAO,IAAI,cAAe,QAAQ,MAAM,IAAK,MAAO;AAAA,EAErD;AAAA,EAEA,eAAgB,WAAY;AAE3B,UAAM,SAAS,CAAC;AAEhB,QAAI,kBAAkB,IAAI,QAAQ;AAClC,QAAI,eAAe,IAAI,QAAQ;AAE/B,QAAK,UAAU,UAAY,WAAU,UAAU,UAAW,iBAAiB,IAAI,WAAW,GAAG,YAAa;AAE1G,sBAAkB,gBAAgB,QAAQ;AAC1C,mBAAe,aAAa,QAAQ;AAEpC,QAAK,UAAU,MAAM,UAAa,OAAO,KAAM,UAAU,EAAE,MAAO,EAAE,SAAS,GAAI;AAEhF,YAAM,gBAAgB,KAAK,oBAAqB,UAAU,WAAW,UAAU,EAAE,QAAQ,iBAAiB,UAAW;AACrH,UAAK,kBAAkB,OAAY,QAAO,KAAM,aAAc;AAAA,IAE/D;AAEA,QAAK,UAAU,MAAM,UAAa,OAAO,KAAM,UAAU,EAAE,MAAO,EAAE,SAAS,GAAI;AAEhF,YAAM,gBAAgB,KAAK,sBAAuB,UAAU,WAAW,UAAU,EAAE,QAAQ,UAAU,aAAa,UAAU,cAAc,UAAU,UAAW;AAC/J,UAAK,kBAAkB,OAAY,QAAO,KAAM,aAAc;AAAA,IAE/D;AAEA,QAAK,UAAU,MAAM,UAAa,OAAO,KAAM,UAAU,EAAE,MAAO,EAAE,SAAS,GAAI;AAEhF,YAAM,aAAa,KAAK,oBAAqB,UAAU,WAAW,UAAU,EAAE,QAAQ,cAAc,OAAQ;AAC5G,UAAK,eAAe,OAAY,QAAO,KAAM,UAAW;AAAA,IAEzD;AAEA,QAAK,UAAU,kBAAkB,QAAY;AAE5C,YAAM,aAAa,KAAK,mBAAoB,SAAU;AACtD,UAAK,eAAe,OAAY,QAAO,KAAM,UAAW;AAAA,IAEzD;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,oBAAqB,WAAW,QAAQ,cAAc,MAAO;AAE5D,UAAM,QAAQ,KAAK,mBAAoB,MAAO;AAC9C,UAAM,SAAS,KAAK,uBAAwB,OAAO,QAAQ,YAAa;AAExE,WAAO,IAAI,oBAAqB,YAAY,MAAM,MAAM,OAAO,MAAO;AAAA,EAEvE;AAAA,EAEA,sBAAuB,WAAW,QAAQ,aAAa,cAAc,YAAa;AAEjF,QAAI;AACJ,QAAI;AAEJ,QAAK,OAAO,MAAM,UAAa,OAAO,MAAM,UAAa,OAAO,MAAM,QAAY;AAEjF,YAAM,SAAS,KAAK,qBAAsB,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,UAAW;AAEnF,cAAQ,OAAQ,CAAE;AAClB,eAAS,OAAQ,CAAE;AAAA,IAEpB;AAEA,QAAK,gBAAgB,QAAY;AAEhC,oBAAc,YAAY,IAAK,UAAU,QAAS;AAClD,kBAAY,KAAM,UAAW;AAE7B,oBAAc,IAAI,MAAM,EAAE,UAAW,WAAY;AACjD,oBAAc,IAAI,WAAW,EAAE,aAAc,WAAY;AAAA,IAE1D;AAEA,QAAK,iBAAiB,QAAY;AAEjC,qBAAe,aAAa,IAAK,UAAU,QAAS;AACpD,mBAAa,KAAM,UAAW;AAE9B,qBAAe,IAAI,MAAM,EAAE,UAAW,YAAa;AACnD,qBAAe,IAAI,WAAW,EAAE,aAAc,YAAa,EAAE,OAAO;AAAA,IAErE;AAEA,UAAM,aAAa,IAAI,WAAW;AAClC,UAAM,QAAQ,IAAI,MAAM;AAExB,UAAM,mBAAmB,CAAC;AAE1B,QAAK,CAAE,UAAU,CAAE,MAAQ,QAAO,IAAI,wBAAyB,YAAY,eAAe,CAAC,GAAG,CAAC,CAAE;AAEjG,aAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAI;AAE5C,YAAM,IAAK,OAAQ,CAAE,GAAG,OAAQ,IAAI,CAAE,GAAG,OAAQ,IAAI,CAAE,GAAG,UAAW;AACrE,iBAAW,aAAc,KAAM;AAE/B,UAAK,gBAAgB,OAAY,YAAW,YAAa,WAAY;AACrE,UAAK,iBAAiB,OAAY,YAAW,SAAU,YAAa;AAGpE,UAAK,IAAI,GAAI;AAEZ,cAAM,WAAW,IAAI,WAAW,EAAE;AAAA,UACjC;AAAA,WACI,IAAI,KAAM,IAAM;AAAA,QACrB;AAEA,YAAK,SAAS,IAAK,UAAW,IAAI,GAAI;AAErC,qBAAW,IAAK,CAAE,WAAW,GAAG,CAAE,WAAW,GAAG,CAAE,WAAW,GAAG,CAAE,WAAW,CAAE;AAAA,QAEhF;AAAA,MAED;AAEA,iBAAW,QAAS,kBAAoB,IAAI,IAAM,CAAE;AAAA,IAErD;AAEA,WAAO,IAAI,wBAAyB,YAAY,eAAe,OAAO,gBAAiB;AAAA,EAExF;AAAA,EAEA,mBAAoB,WAAY;AAE/B,UAAM,SAAS,UAAU,cAAc,OAAO;AAC9C,UAAM,SAAS,OAAO,OAAO,IAAK,SAAW,KAAM;AAElD,aAAO,MAAM;AAAA,IAEd,CAAE;AAEF,UAAM,WAAW,WAAW,gBAAiB,UAAU,SAAU,EAAE,sBAAuB,UAAU,SAAU;AAE9G,WAAO,IAAI,oBAAqB,UAAU,YAAY,4BAA4B,WAAW,KAAK,OAAO,OAAO,MAAO;AAAA,EAExH;AAAA;AAAA;AAAA,EAIA,mBAAoB,QAAS;AAE5B,QAAI,QAAQ,CAAC;AAGb,QAAK,OAAO,MAAM,OAAY,SAAQ,MAAM,OAAQ,OAAO,EAAE,KAAM;AACnE,QAAK,OAAO,MAAM,OAAY,SAAQ,MAAM,OAAQ,OAAO,EAAE,KAAM;AACnE,QAAK,OAAO,MAAM,OAAY,SAAQ,MAAM,OAAQ,OAAO,EAAE,KAAM;AAGnE,YAAQ,MAAM,KAAM,SAAW,GAAG,GAAI;AAErC,aAAO,IAAI;AAAA,IAEZ,CAAE;AAGF,QAAK,MAAM,SAAS,GAAI;AAEvB,UAAI,cAAc;AAClB,UAAI,YAAY,MAAO,CAAE;AACzB,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAO;AAEzC,cAAM,eAAe,MAAO,CAAE;AAC9B,YAAK,iBAAiB,WAAY;AAEjC,gBAAO,WAAY,IAAI;AACvB,sBAAY;AACZ;AAAA,QAED;AAAA,MAED;AAEA,cAAQ,MAAM,MAAO,GAAG,WAAY;AAAA,IAErC;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,uBAAwB,OAAO,QAAQ,cAAe;AAErD,UAAM,YAAY;AAElB,UAAM,SAAS,CAAC;AAEhB,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,UAAM,QAAS,SAAW,MAAO;AAEhC,UAAK,OAAO,EAAI,UAAS,OAAO,EAAE,MAAM,QAAS,IAAK;AACtD,UAAK,OAAO,EAAI,UAAS,OAAO,EAAE,MAAM,QAAS,IAAK;AACtD,UAAK,OAAO,EAAI,UAAS,OAAO,EAAE,MAAM,QAAS,IAAK;AAGtD,UAAK,WAAW,IAAM;AAErB,cAAM,SAAS,OAAO,EAAE,OAAQ,MAAO;AACvC,eAAO,KAAM,MAAO;AACpB,kBAAW,CAAE,IAAI;AAAA,MAElB,OAAO;AAGN,eAAO,KAAM,UAAW,CAAE,CAAE;AAAA,MAE7B;AAEA,UAAK,WAAW,IAAM;AAErB,cAAM,SAAS,OAAO,EAAE,OAAQ,MAAO;AACvC,eAAO,KAAM,MAAO;AACpB,kBAAW,CAAE,IAAI;AAAA,MAElB,OAAO;AAEN,eAAO,KAAM,UAAW,CAAE,CAAE;AAAA,MAE7B;AAEA,UAAK,WAAW,IAAM;AAErB,cAAM,SAAS,OAAO,EAAE,OAAQ,MAAO;AACvC,eAAO,KAAM,MAAO;AACpB,kBAAW,CAAE,IAAI;AAAA,MAElB,OAAO;AAEN,eAAO,KAAM,UAAW,CAAE,CAAE;AAAA,MAE7B;AAAA,IAED,CAAE;AAEF,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAsB,QAAQ,QAAQ,QAAQ,YAAa;AAE1D,UAAM,QAAQ,CAAC;AACf,UAAM,SAAS,CAAC;AAGhB,UAAM,KAAM,OAAO,MAAO,CAAE,CAAE;AAC9B,WAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AACtD,WAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AACtD,WAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AAEtD,aAAU,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAO;AAEjD,YAAM,eAAe;AAAA,QACpB,OAAO,OAAQ,IAAI,CAAE;AAAA,QACrB,OAAO,OAAQ,IAAI,CAAE;AAAA,QACrB,OAAO,OAAQ,IAAI,CAAE;AAAA,MACtB;AAEA,UAAK,MAAO,aAAc,CAAE,CAAE,KAAK,MAAO,aAAc,CAAE,CAAE,KAAK,MAAO,aAAc,CAAE,CAAE,GAAI;AAE7F;AAAA,MAED;AAEA,YAAM,kBAAkB,aAAa,IAAK,UAAU,QAAS;AAE7D,YAAM,eAAe;AAAA,QACpB,OAAO,OAAQ,CAAE;AAAA,QACjB,OAAO,OAAQ,CAAE;AAAA,QACjB,OAAO,OAAQ,CAAE;AAAA,MAClB;AAEA,UAAK,MAAO,aAAc,CAAE,CAAE,KAAK,MAAO,aAAc,CAAE,CAAE,KAAK,MAAO,aAAc,CAAE,CAAE,GAAI;AAE7F;AAAA,MAED;AAEA,YAAM,kBAAkB,aAAa,IAAK,UAAU,QAAS;AAE7D,YAAM,aAAa;AAAA,QAClB,aAAc,CAAE,IAAI,aAAc,CAAE;AAAA,QACpC,aAAc,CAAE,IAAI,aAAc,CAAE;AAAA,QACpC,aAAc,CAAE,IAAI,aAAc,CAAE;AAAA,MACrC;AAEA,YAAM,eAAe;AAAA,QACpB,KAAK,IAAK,WAAY,CAAE,CAAE;AAAA,QAC1B,KAAK,IAAK,WAAY,CAAE,CAAE;AAAA,QAC1B,KAAK,IAAK,WAAY,CAAE,CAAE;AAAA,MAC3B;AAEA,UAAK,aAAc,CAAE,KAAK,OAAO,aAAc,CAAE,KAAK,OAAO,aAAc,CAAE,KAAK,KAAM;AAEvF,cAAM,aAAa,KAAK,IAAK,GAAG,YAAa;AAE7C,cAAM,kBAAkB,aAAa;AAErC,cAAM,KAAK,IAAI,MAAO,GAAG,iBAAiB,UAAW;AACrD,cAAM,KAAK,IAAI,MAAO,GAAG,iBAAiB,UAAW;AAErD,cAAM,KAAK,IAAI,WAAW,EAAE,aAAc,EAAG;AAC7C,cAAM,KAAK,IAAI,WAAW,EAAE,aAAc,EAAG;AAG7C,YAAK,GAAG,IAAK,EAAG,GAAI;AAEnB,aAAG,IAAK,CAAE,GAAG,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE;AAAA,QAExC;AAGA,cAAM,cAAc,OAAO,MAAO,IAAI,CAAE;AACxC,cAAM,WAAW,OAAO,MAAO,CAAE,IAAI;AAErC,cAAM,IAAI,IAAI,WAAW;AACzB,cAAM,IAAI,IAAI,MAAM;AACpB,iBAAU,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,iBAAkB;AAElD,YAAE,KAAM,GAAG,MAAM,EAAE,MAAO,GAAG,MAAM,GAAG,CAAE,CAAE;AAE1C,gBAAM,KAAM,cAAc,IAAI,QAAS;AACvC,YAAE,kBAAmB,GAAG,UAAW;AAEnC,iBAAO,KAAM,EAAE,CAAE;AACjB,iBAAO,KAAM,EAAE,CAAE;AACjB,iBAAO,KAAM,EAAE,CAAE;AAAA,QAElB;AAAA,MAED,OAAO;AAEN,cAAM,KAAM,OAAO,MAAO,CAAE,CAAE;AAC9B,eAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AACtD,eAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AACtD,eAAO,KAAM,UAAU,SAAU,OAAO,OAAQ,CAAE,CAAE,CAAE;AAAA,MAEvD;AAAA,IAED;AAEA,WAAO,CAAE,OAAO,MAAO;AAAA,EAExB;AAED;AAGA,IAAM,aAAN,MAAiB;AAAA,EAEhB,cAAc;AAEb,WAAO,KAAK,UAAW,KAAK,gBAAgB,CAAE;AAAA,EAE/C;AAAA,EAEA,iBAAiB;AAEhB,WAAO,KAAK,UAAW,KAAK,gBAAgB,CAAE;AAAA,EAE/C;AAAA,EAEA,iBAAiB;AAEhB,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,UAAW,MAAO;AAEjB,SAAK,UAAU,KAAM,IAAK;AAC1B,SAAK,iBAAiB;AAAA,EAEvB;AAAA,EAEA,WAAW;AAEV,SAAK,UAAU,IAAI;AACnB,SAAK,iBAAiB;AAAA,EAEvB;AAAA,EAEA,eAAgB,KAAK,MAAO;AAE3B,SAAK,cAAc;AACnB,SAAK,kBAAkB;AAAA,EAExB;AAAA,EAEA,MAAO,MAAO;AAEb,SAAK,gBAAgB;AAErB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,YAAY,CAAC;AAClB,SAAK,cAAc,CAAC;AACpB,SAAK,kBAAkB;AAEvB,UAAM,QAAQ;AAEd,UAAM,QAAQ,KAAK,MAAO,SAAU;AAEpC,UAAM,QAAS,SAAW,MAAM,GAAI;AAEnC,YAAM,eAAe,KAAK,MAAO,WAAY;AAC7C,YAAM,aAAa,KAAK,MAAO,WAAY;AAE3C,UAAK,gBAAgB,WAAa;AAElC,YAAM,iBAAiB,KAAK,MAAO,UAAU,MAAM,gBAAgB,iBAAiB,EAAG;AACvF,YAAM,gBAAgB,KAAK,MAAO,UAAY,MAAM,gBAAkB,4BAA6B;AACnG,YAAM,WAAW,KAAK,MAAO,WAAY,MAAM,gBAAgB,KAAM,IAAK;AAE1E,UAAK,gBAAiB;AAErB,cAAM,eAAgB,MAAM,cAAe;AAAA,MAE5C,WAAY,eAAgB;AAE3B,cAAM,kBAAmB,MAAM,eAAe,MAAO,EAAG,CAAE,CAAE;AAAA,MAE7D,WAAY,UAAW;AAEtB,cAAM,SAAS;AAAA,MAEhB,WAAY,KAAK,MAAO,WAAY,GAAI;AAIvC,cAAM,2BAA4B,IAAK;AAAA,MAExC;AAAA,IAED,CAAE;AAEF,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,eAAgB,MAAM,UAAW;AAEhC,UAAM,WAAW,SAAU,CAAE,EAAE,KAAK,EAAE,QAAS,MAAM,EAAG,EAAE,QAAS,MAAM,EAAG;AAE5E,UAAM,YAAY,SAAU,CAAE,EAAE,MAAO,GAAI,EAAE,IAAK,SAAW,MAAO;AAEnE,aAAO,KAAK,KAAK,EAAE,QAAS,MAAM,EAAG,EAAE,QAAS,MAAM,EAAG;AAAA,IAE1D,CAAE;AAEF,UAAM,OAAO,EAAE,MAAM,SAAS;AAC9B,UAAM,QAAQ,KAAK,cAAe,SAAU;AAE5C,UAAM,cAAc,KAAK,eAAe;AAGxC,QAAK,KAAK,kBAAkB,GAAI;AAE/B,WAAK,SAAS,IAAK,UAAU,IAAK;AAAA,IAEnC,OAAO;AAGN,UAAK,YAAY,aAAc;AAG9B,YAAK,aAAa,YAAa;AAE9B,sBAAY,SAAS,KAAM,IAAK;AAAA,QAEjC,WAAY,YAAa,QAAS,EAAE,OAAO,QAAY;AAEtD,sBAAa,QAAS,IAAI,CAAC;AAC3B,sBAAa,QAAS,EAAG,YAAa,QAAS,EAAE,EAAG,IAAI,YAAa,QAAS;AAAA,QAE/E;AAEA,YAAK,MAAM,OAAO,GAAK,aAAa,QAAS,EAAG,MAAM,EAAG,IAAI;AAAA,MAE9D,WAAY,OAAO,MAAM,OAAO,UAAW;AAE1C,oBAAa,QAAS,IAAI,CAAC;AAC3B,oBAAa,QAAS,EAAG,MAAM,EAAG,IAAI;AAAA,MAEvC,WAAY,aAAa,gBAAiB;AAEzC,YAAK,aAAa,WAAa,aAAa,QAAS,IAAI,CAAE,IAAK;AAAA,YAC3D,aAAa,QAAS,IAAI;AAAA,MAEhC;AAAA,IAED;AAEA,QAAK,OAAO,MAAM,OAAO,SAAW,MAAK,KAAK,MAAM;AACpD,QAAK,MAAM,SAAS,GAAK,MAAK,WAAW,MAAM;AAC/C,QAAK,MAAM,SAAS,GAAK,MAAK,WAAW,MAAM;AAE/C,SAAK,UAAW,IAAK;AAAA,EAEtB;AAAA,EAEA,cAAe,OAAQ;AAEtB,QAAI,KAAK,MAAO,CAAE;AAElB,QAAK,MAAO,CAAE,MAAM,IAAK;AAExB,WAAK,SAAU,MAAO,CAAE,CAAE;AAE1B,UAAK,MAAO,EAAG,GAAI;AAElB,aAAK,MAAO,CAAE;AAAA,MAEf;AAAA,IAED;AAEA,QAAI,OAAO,IAAI,OAAO;AAEtB,QAAK,MAAM,SAAS,GAAI;AAEvB,aAAO,MAAO,CAAE,EAAE,QAAS,YAAY,EAAG;AAC1C,aAAO,MAAO,CAAE;AAAA,IAEjB;AAEA,WAAO,EAAE,IAAQ,MAAY,KAAW;AAAA,EAEzC;AAAA,EAEA,kBAAmB,MAAM,UAAU,aAAc;AAEhD,QAAI,WAAW,SAAU,CAAE,EAAE,QAAS,MAAM,EAAG,EAAE,QAAS,MAAM,EAAG,EAAE,KAAK;AAC1E,QAAI,YAAY,SAAU,CAAE,EAAE,QAAS,MAAM,EAAG,EAAE,QAAS,MAAM,EAAG,EAAE,KAAK;AAK3E,QAAK,aAAa,aAAa,cAAc,KAAM;AAElD,kBAAY,YAAY,QAAS,MAAM,EAAG,EAAE,QAAS,MAAM,EAAG,EAAE,KAAK;AAAA,IAEtE;AAEA,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,aAAa,YAAY;AAE/B,QAAK,eAAe,gBAAiB;AAEpC,WAAK,yBAA0B,MAAM,UAAU,SAAU;AACzD;AAAA,IAED;AAGA,QAAK,aAAa,KAAM;AAEvB,YAAM,YAAY,UAAU,MAAO,GAAI,EAAE,MAAO,CAAE;AAClD,YAAM,OAAO,SAAU,UAAW,CAAE,CAAE;AACtC,YAAM,KAAK,SAAU,UAAW,CAAE,CAAE;AAEpC,UAAI,OAAO,UAAU,MAAO,GAAI,EAAE,MAAO,CAAE;AAE3C,aAAO,KAAK,IAAK,SAAW,MAAO;AAElC,eAAO,KAAK,KAAK,EAAE,QAAS,MAAM,EAAG;AAAA,MAEtC,CAAE;AAEF,iBAAW;AACX,kBAAY,CAAE,MAAM,EAAG;AACvB,aAAQ,WAAW,IAAK;AAExB,UAAK,YAAa,QAAS,MAAM,QAAY;AAE5C,oBAAa,QAAS,IAAI,CAAC;AAAA,MAE5B;AAAA,IAED;AAGA,QAAK,aAAa,OAAS,aAAY,KAAK;AAG5C,QAAK,YAAY,eAAe,MAAM,QAAS,YAAa,QAAS,CAAE,GAAI;AAE1E,kBAAa,QAAS,EAAE,KAAM,SAAU;AAAA,IAEzC,OAAO;AAEN,UAAK,aAAa,IAAM,aAAa,QAAS,IAAI;AAAA,UAC7C,aAAY,IAAI;AAAA,IAEtB;AAEA,SAAK,eAAgB,aAAa,QAAS;AAG3C,QAAK,aAAa,OAAO,UAAU,MAAO,EAAI,MAAM,KAAM;AAEzD,kBAAY,IAAI,iBAAkB,SAAU;AAAA,IAE7C;AAAA,EAED;AAAA,EAEA,2BAA4B,MAAO;AAElC,UAAM,cAAc,KAAK,eAAe;AAExC,gBAAY,KAAK;AAIjB,QAAK,KAAK,MAAO,EAAI,MAAM,KAAM;AAEhC,kBAAY,IAAI,iBAAkB,YAAY,CAAE;AAAA,IAEjD;AAAA,EAED;AAAA;AAAA,EAGA,yBAA0B,MAAM,UAAU,WAAY;AAMrD,UAAM,QAAQ,UAAU,MAAO,IAAK,EAAE,IAAK,SAAW,MAAO;AAE5D,aAAO,KAAK,KAAK,EAAE,QAAS,OAAO,EAAG,EAAE,QAAS,MAAM,GAAI;AAAA,IAE5D,CAAE;AAEF,UAAM,gBAAgB,MAAO,CAAE;AAC/B,UAAM,iBAAiB,MAAO,CAAE;AAChC,UAAM,iBAAiB,MAAO,CAAE;AAChC,UAAM,gBAAgB,MAAO,CAAE;AAC/B,QAAI,iBAAiB,MAAO,CAAE;AAG9B,YAAS,gBAAiB;AAAA,MAEzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACJ,yBAAiB,WAAY,cAAe;AAC5C;AAAA,MAED,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACJ,yBAAiB,iBAAkB,cAAe;AAClD;AAAA,IAEF;AAGA,SAAK,YAAY,EAAG,aAAc,IAAI;AAAA,MAErC,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,IAEV;AAEA,SAAK,eAAgB,KAAK,YAAY,GAAG,aAAc;AAAA,EAExD;AAED;AAGA,IAAM,eAAN,MAAmB;AAAA,EAElB,MAAO,QAAS;AAEf,UAAM,SAAS,IAAI,aAAc,MAAO;AACxC,WAAO,KAAM,EAAG;AAEhB,UAAM,UAAU,OAAO,UAAU;AAEjC,QAAK,UAAU,MAAO;AAErB,YAAM,IAAI,MAAO,8DAA8D,OAAQ;AAAA,IAExF;AAEA,UAAM,WAAW,IAAI,QAAQ;AAE7B,WAAQ,CAAE,KAAK,aAAc,MAAO,GAAI;AAEvC,YAAM,OAAO,KAAK,UAAW,QAAQ,OAAQ;AAC7C,UAAK,SAAS,KAAO,UAAS,IAAK,KAAK,MAAM,IAAK;AAAA,IAEpD;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,aAAc,QAAS;AAUtB,QAAK,OAAO,KAAK,IAAI,OAAO,GAAI;AAE/B,cAAW,OAAO,UAAU,IAAI,MAAM,KAAO,CAAE,OAAS,OAAO,KAAK;AAAA,IAErE,OAAO;AAEN,aAAO,OAAO,UAAU,IAAI,MAAM,MAAM,OAAO,KAAK;AAAA,IAErD;AAAA,EAED;AAAA;AAAA,EAGA,UAAW,QAAQ,SAAU;AAE5B,UAAM,OAAO,CAAC;AAGd,UAAM,YAAc,WAAW,OAAS,OAAO,UAAU,IAAI,OAAO,UAAU;AAC9E,UAAM,gBAAkB,WAAW,OAAS,OAAO,UAAU,IAAI,OAAO,UAAU;AAElF,IAAE,WAAW,OAAS,OAAO,UAAU,IAAI,OAAO,UAAU;AAE5D,UAAM,UAAU,OAAO,SAAS;AAChC,UAAM,OAAO,OAAO,UAAW,OAAQ;AAGvC,QAAK,cAAc,EAAI,QAAO;AAE9B,UAAM,eAAe,CAAC;AAEtB,aAAU,IAAI,GAAG,IAAI,eAAe,KAAO;AAE1C,mBAAa,KAAM,KAAK,cAAe,MAAO,CAAE;AAAA,IAEjD;AAGA,UAAM,KAAK,aAAa,SAAS,IAAI,aAAc,CAAE,IAAI;AACzD,UAAM,WAAW,aAAa,SAAS,IAAI,aAAc,CAAE,IAAI;AAC/D,UAAM,WAAW,aAAa,SAAS,IAAI,aAAc,CAAE,IAAI;AAI/D,SAAK,iBAAmB,kBAAkB,KAAK,OAAO,UAAU,MAAM,YAAc,OAAO;AAE3F,WAAQ,YAAY,OAAO,UAAU,GAAI;AAExC,YAAM,UAAU,KAAK,UAAW,QAAQ,OAAQ;AAEhD,UAAK,YAAY,KAAO,MAAK,aAAc,MAAM,MAAM,OAAQ;AAAA,IAEhE;AAEA,SAAK,eAAe;AAEpB,QAAK,OAAO,OAAO,SAAW,MAAK,KAAK;AACxC,QAAK,aAAa,GAAK,MAAK,WAAW;AACvC,QAAK,aAAa,GAAK,MAAK,WAAW;AACvC,QAAK,SAAS,GAAK,MAAK,OAAO;AAE/B,WAAO;AAAA,EAER;AAAA,EAEA,aAAc,MAAM,MAAM,SAAU;AAGnC,QAAK,QAAQ,mBAAmB,MAAO;AAEtC,YAAM,QAAQ,QAAQ,aAAc,CAAE;AAEtC,UAAK,MAAM,QAAS,KAAM,GAAI;AAE7B,aAAM,QAAQ,IAAK,IAAI;AAEvB,gBAAQ,IAAI;AAAA,MAEb,OAAO;AAEN,aAAM,QAAQ,IAAK,IAAI;AAAA,MAExB;AAAA,IAED,WAAY,SAAS,iBAAiB,QAAQ,SAAS,KAAM;AAE5D,YAAM,QAAQ,CAAC;AAEf,cAAQ,aAAa,QAAS,SAAW,UAAU,GAAI;AAGtD,YAAK,MAAM,EAAI,OAAM,KAAM,QAAS;AAAA,MAErC,CAAE;AAEF,UAAK,KAAK,gBAAgB,QAAY;AAErC,aAAK,cAAc,CAAC;AAAA,MAErB;AAEA,WAAK,YAAY,KAAM,KAAM;AAAA,IAE9B,WAAY,QAAQ,SAAS,gBAAiB;AAE7C,YAAM,OAAO,OAAO,KAAM,OAAQ;AAElC,WAAK,QAAS,SAAW,KAAM;AAE9B,aAAM,GAAI,IAAI,QAAS,GAAI;AAAA,MAE5B,CAAE;AAAA,IAEH,WAAY,SAAS,kBAAkB,QAAQ,SAAS,KAAM;AAE7D,UAAI,gBAAgB,QAAQ,aAAc,CAAE;AAC5C,UAAI,iBAAiB,QAAQ,aAAc,CAAE;AAC7C,YAAM,iBAAiB,QAAQ,aAAc,CAAE;AAC/C,YAAM,gBAAgB,QAAQ,aAAc,CAAE;AAC9C,UAAI;AAEJ,UAAK,cAAc,QAAS,MAAO,MAAM,EAAI,iBAAgB,cAAc,QAAS,QAAQ,MAAO;AACnG,UAAK,eAAe,QAAS,MAAO,MAAM,EAAI,kBAAiB,eAAe,QAAS,QAAQ,MAAO;AAEtG,UAAK,mBAAmB,WAAW,mBAAmB,cAAc,mBAAmB,YAAY,mBAAmB,cAAc,eAAe,QAAS,MAAO,MAAM,GAAI;AAE5K,yBAAiB;AAAA,UAChB,QAAQ,aAAc,CAAE;AAAA,UACxB,QAAQ,aAAc,CAAE;AAAA,UACxB,QAAQ,aAAc,CAAE;AAAA,QACzB;AAAA,MAED,OAAO;AAEN,yBAAiB,QAAQ,aAAc,CAAE;AAAA,MAE1C;AAGA,WAAM,aAAc,IAAI;AAAA,QAEvB,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,MAEV;AAAA,IAED,WAAY,KAAM,QAAQ,IAAK,MAAM,QAAY;AAEhD,UAAK,OAAO,QAAQ,OAAO,UAAW;AAErC,aAAM,QAAQ,IAAK,IAAI,CAAC;AACxB,aAAM,QAAQ,IAAK,EAAG,QAAQ,EAAG,IAAI;AAAA,MAEtC,OAAO;AAEN,aAAM,QAAQ,IAAK,IAAI;AAAA,MAExB;AAAA,IAED,OAAO;AAEN,UAAK,QAAQ,SAAS,YAAa;AAElC,YAAK,CAAE,MAAM,QAAS,KAAM,QAAQ,IAAK,CAAE,GAAI;AAE9C,eAAM,QAAQ,IAAK,IAAI,CAAE,KAAM,QAAQ,IAAK,CAAE;AAAA,QAE/C;AAEA,aAAM,QAAQ,IAAK,EAAE,KAAM,OAAQ;AAAA,MAEpC,WAAY,KAAM,QAAQ,IAAK,EAAG,QAAQ,EAAG,MAAM,QAAY;AAE9D,aAAM,QAAQ,IAAK,EAAG,QAAQ,EAAG,IAAI;AAAA,MAEtC;AAAA,IAED;AAAA,EAED;AAAA,EAEA,cAAe,QAAS;AAEvB,UAAM,OAAO,OAAO,UAAW,CAAE;AACjC,QAAI;AAEJ,YAAS,MAAO;AAAA,MAEf,KAAK;AACJ,eAAO,OAAO,WAAW;AAAA,MAE1B,KAAK;AACJ,eAAO,OAAO,WAAW;AAAA,MAE1B,KAAK;AACJ,eAAO,OAAO,WAAW;AAAA,MAE1B,KAAK;AACJ,eAAO,OAAO,SAAS;AAAA,MAExB,KAAK;AACJ,eAAO,OAAO,SAAS;AAAA,MAExB,KAAK;AACJ,iBAAS,OAAO,UAAU;AAC1B,eAAO,OAAO,eAAgB,MAAO;AAAA,MAEtC,KAAK;AACJ,iBAAS,OAAO,UAAU;AAC1B,eAAO,OAAO,UAAW,MAAO;AAAA,MAEjC,KAAK;AACJ,eAAO,OAAO,SAAS;AAAA,MAExB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEJ,cAAM,cAAc,OAAO,UAAU;AACrC,cAAM,WAAW,OAAO,UAAU;AAClC,cAAM,mBAAmB,OAAO,UAAU;AAE1C,YAAK,aAAa,GAAI;AAErB,kBAAS,MAAO;AAAA,YAEf,KAAK;AAAA,YACL,KAAK;AACJ,qBAAO,OAAO,gBAAiB,WAAY;AAAA,YAE5C,KAAK;AACJ,qBAAO,OAAO,gBAAiB,WAAY;AAAA,YAE5C,KAAK;AACJ,qBAAO,OAAO,gBAAiB,WAAY;AAAA,YAE5C,KAAK;AACJ,qBAAO,OAAO,cAAe,WAAY;AAAA,YAE1C,KAAK;AACJ,qBAAO,OAAO,cAAe,WAAY;AAAA,UAE3C;AAAA,QAED;AAEA,cAAM,OAAc,WAAY,IAAI,WAAY,OAAO,eAAgB,gBAAiB,CAAE,CAAE;AAC5F,cAAM,UAAU,IAAI,aAAc,KAAK,MAAO;AAE9C,gBAAS,MAAO;AAAA,UAEf,KAAK;AAAA,UACL,KAAK;AACJ,mBAAO,QAAQ,gBAAiB,WAAY;AAAA,UAE7C,KAAK;AACJ,mBAAO,QAAQ,gBAAiB,WAAY;AAAA,UAE7C,KAAK;AACJ,mBAAO,QAAQ,gBAAiB,WAAY;AAAA,UAE7C,KAAK;AACJ,mBAAO,QAAQ,cAAe,WAAY;AAAA,UAE3C,KAAK;AACJ,mBAAO,QAAQ,cAAe,WAAY;AAAA,QAE5C;AAEA;AAAA,MAED;AACC,cAAM,IAAI,MAAO,4CAA4C,IAAK;AAAA,IAEpE;AAAA,EAED;AAED;AAEA,IAAM,eAAN,MAAmB;AAAA,EAElB,YAAa,QAAQ,cAAe;AAEnC,SAAK,KAAK,IAAI,SAAU,MAAO;AAC/B,SAAK,SAAS;AACd,SAAK,eAAiB,iBAAiB,SAAc,eAAe;AACpE,SAAK,eAAe,IAAI,YAAY;AAAA,EAErC;AAAA,EAEA,YAAY;AAEX,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,OAAO;AAEN,WAAO,KAAK,GAAG,OAAO;AAAA,EAEvB;AAAA,EAEA,KAAM,QAAS;AAEd,SAAK,UAAU;AAAA,EAEhB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEZ,YAAS,KAAK,SAAS,IAAI,OAAQ;AAAA,EAEpC;AAAA,EAEA,gBAAiB,MAAO;AAEvB,UAAM,IAAI,CAAC;AAEX,aAAU,IAAI,GAAG,IAAI,MAAM,KAAO;AAEjC,QAAE,KAAM,KAAK,WAAW,CAAE;AAAA,IAE3B;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,WAAW;AAEV,UAAM,QAAQ,KAAK,GAAG,SAAU,KAAK,MAAO;AAC5C,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,WAAW;AAEV,UAAM,QAAQ,KAAK,GAAG,SAAU,KAAK,QAAQ,KAAK,YAAa;AAC/D,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,WAAW;AAEV,UAAM,QAAQ,KAAK,GAAG,SAAU,KAAK,QAAQ,KAAK,YAAa;AAC/D,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,cAAe,MAAO;AAErB,UAAM,IAAI,CAAC;AAEX,aAAU,IAAI,GAAG,IAAI,MAAM,KAAO;AAEjC,QAAE,KAAM,KAAK,SAAS,CAAE;AAAA,IAEzB;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,YAAY;AAEX,UAAM,QAAQ,KAAK,GAAG,UAAW,KAAK,QAAQ,KAAK,YAAa;AAChE,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AAEV,QAAI,KAAK;AAET,QAAK,KAAK,cAAe;AAExB,YAAM,KAAK,UAAU;AACrB,aAAO,KAAK,UAAU;AAAA,IAEvB,OAAO;AAEN,aAAO,KAAK,UAAU;AACtB,YAAM,KAAK,UAAU;AAAA,IAEtB;AAGA,QAAK,OAAO,YAAa;AAExB,aAAO,CAAE,OAAO;AAChB,YAAM,CAAE,MAAM;AAEd,UAAK,QAAQ,WAAa,QAAS,OAAO,IAAM;AAEhD,YAAQ,MAAM,IAAM;AAEpB,aAAO,EAAI,OAAO,aAAc;AAAA,IAEjC;AAEA,WAAO,OAAO,aAAc;AAAA,EAE7B;AAAA,EAEA,cAAe,MAAO;AAErB,UAAM,IAAI,CAAC;AAEX,aAAU,IAAI,GAAG,IAAI,MAAM,KAAO;AAEjC,QAAE,KAAM,KAAK,SAAS,CAAE;AAAA,IAEzB;AAEA,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,YAAY;AAEX,QAAI,KAAK;AAET,QAAK,KAAK,cAAe;AAExB,YAAM,KAAK,UAAU;AACrB,aAAO,KAAK,UAAU;AAAA,IAEvB,OAAO;AAEN,aAAO,KAAK,UAAU;AACtB,YAAM,KAAK,UAAU;AAAA,IAEtB;AAEA,WAAO,OAAO,aAAc;AAAA,EAE7B;AAAA,EAEA,aAAa;AAEZ,UAAM,QAAQ,KAAK,GAAG,WAAY,KAAK,QAAQ,KAAK,YAAa;AACjE,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,gBAAiB,MAAO;AAEvB,UAAM,IAAI,CAAC;AAEX,aAAU,IAAI,GAAG,IAAI,MAAM,KAAO;AAEjC,QAAE,KAAM,KAAK,WAAW,CAAE;AAAA,IAE3B;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,aAAa;AAEZ,UAAM,QAAQ,KAAK,GAAG,WAAY,KAAK,QAAQ,KAAK,YAAa;AACjE,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,gBAAiB,MAAO;AAEvB,UAAM,IAAI,CAAC;AAEX,aAAU,IAAI,GAAG,IAAI,MAAM,KAAO;AAEjC,QAAE,KAAM,KAAK,WAAW,CAAE;AAAA,IAE3B;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,eAAgB,MAAO;AAEtB,UAAM,QAAQ,KAAK,GAAG,OAAO,MAAO,KAAK,QAAQ,KAAK,SAAS,IAAK;AACpE,SAAK,UAAU;AACf,WAAO;AAAA,EAER;AAAA,EAEA,UAAW,MAAO;AAEjB,UAAM,QAAQ,KAAK;AACnB,QAAI,IAAI,IAAI,WAAY,KAAK,GAAG,QAAQ,OAAO,IAAK;AAEpD,SAAK,KAAM,IAAK;AAEhB,UAAM,WAAW,EAAE,QAAS,CAAE;AAC9B,QAAK,YAAY,EAAI,KAAI,IAAI,WAAY,KAAK,GAAG,QAAQ,OAAO,QAAS;AAEzE,WAAO,KAAK,aAAa,OAAQ,CAAE;AAAA,EAEpC;AAED;AAIA,IAAM,UAAN,MAAc;AAAA,EAEb,IAAK,KAAK,KAAM;AAEf,SAAM,GAAI,IAAI;AAAA,EAEf;AAED;AAIA,SAAS,kBAAmB,QAAS;AAEpC,QAAM,UAAU;AAEhB,SAAO,OAAO,cAAc,QAAQ,UAAU,YAAY,2BAA4B,QAAQ,GAAG,QAAQ,MAAO;AAEjH;AAEA,SAAS,iBAAkB,MAAO;AAEjC,QAAM,UAAU,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAK;AAEzH,MAAI,SAAS;AAEb,WAAS,KAAM,QAAS;AAEvB,UAAM,SAAS,KAAM,SAAS,CAAE;AAChC,WAAO,KAAK,MAAO,SAAS,MAAO;AACnC;AACA,WAAO;AAAA,EAER;AAEA,WAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAG,GAAI;AAE3C,UAAM,MAAM,KAAM,CAAE;AACpB,QAAK,QAAQ,QAAS,CAAE,GAAI;AAE3B,aAAO;AAAA,IAER;AAAA,EAED;AAEA,SAAO;AAER;AAEA,SAAS,cAAe,MAAO;AAE9B,QAAM,gBAAgB;AACtB,QAAM,QAAQ,KAAK,MAAO,aAAc;AAExC,MAAK,OAAQ;AAEZ,UAAM,UAAU,SAAU,MAAO,CAAE,CAAE;AACrC,WAAO;AAAA,EAER;AAEA,QAAM,IAAI,MAAO,qEAAsE;AAExF;AAGA,SAAS,wBAAyB,MAAO;AAExC,SAAO,OAAO;AAEf;AAEA,IAAM,YAAY,CAAC;AAGnB,SAAS,QAAS,oBAAoB,cAAc,aAAa,YAAa;AAE7E,MAAI;AAEJ,UAAS,WAAW,aAAc;AAAA,IAEjC,KAAK;AACJ,cAAQ;AACR;AAAA,IACD,KAAK;AACJ,cAAQ;AACR;AAAA,IACD,KAAK;AACJ,cAAQ;AACR;AAAA,IACD,KAAK;AACJ,cAAQ,WAAW,QAAS,CAAE;AAC9B;AAAA,IACD;AACC,cAAQ,KAAM,qDAAqD,WAAW,WAAY;AAAA,EAE5F;AAEA,MAAK,WAAW,kBAAkB,gBAAkB,SAAQ,WAAW,QAAS,KAAM;AAEtF,QAAM,OAAO,QAAQ,WAAW;AAChC,QAAM,KAAK,OAAO,WAAW;AAE7B,SAAO,MAAO,WAAW,WAAW,QAAQ,MAAM,EAAG;AAEtD;AAEA,IAAM,YAAY,IAAI,MAAM;AAC5B,IAAM,UAAU,IAAI,QAAQ;AAK5B,SAAS,kBAAmB,eAAgB;AAE3C,QAAM,gBAAgB,IAAI,QAAQ;AAClC,QAAM,gBAAgB,IAAI,QAAQ;AAClC,QAAM,aAAa,IAAI,QAAQ;AAC/B,QAAM,iBAAiB,IAAI,QAAQ;AAEnC,QAAM,YAAY,IAAI,QAAQ;AAC9B,QAAM,iBAAiB,IAAI,QAAQ;AACnC,QAAM,kBAAkB,IAAI,QAAQ;AACpC,QAAM,mBAAmB,IAAI,QAAQ;AACrC,QAAM,kBAAkB,IAAI,QAAQ;AAEpC,QAAM,YAAY,IAAI,QAAQ;AAC9B,QAAM,YAAY,IAAI,QAAQ;AAC9B,QAAM,WAAW,IAAI,QAAQ;AAE7B,QAAM,cAAgB,cAAc,cAAgB,cAAc,cAAc;AAEhF,MAAK,cAAc,YAAc,eAAc,YAAa,QAAQ,UAAW,cAAc,WAAY,CAAE;AAE3G,MAAK,cAAc,aAAc;AAEhC,UAAM,QAAQ,cAAc,YAAY,IAAK,UAAU,QAAS;AAChE,UAAM,KAAM,cAAc,cAAc,MAAM,aAAc;AAC5D,kBAAc,sBAAuB,UAAU,UAAW,KAAM,CAAE;AAAA,EAEnE;AAEA,MAAK,cAAc,UAAW;AAE7B,UAAM,QAAQ,cAAc,SAAS,IAAK,UAAU,QAAS;AAC7D,UAAM,KAAM,cAAc,cAAc,MAAM,aAAc;AAC5D,eAAW,sBAAuB,UAAU,UAAW,KAAM,CAAE;AAAA,EAEhE;AAEA,MAAK,cAAc,cAAe;AAEjC,UAAM,QAAQ,cAAc,aAAa,IAAK,UAAU,QAAS;AACjE,UAAM,KAAM,cAAc,cAAc,MAAM,aAAc;AAC5D,mBAAe,sBAAuB,UAAU,UAAW,KAAM,CAAE;AACnE,mBAAe,OAAO;AAAA,EAEvB;AAEA,MAAK,cAAc,MAAQ,WAAU,MAAO,QAAQ,UAAW,cAAc,KAAM,CAAE;AAGrF,MAAK,cAAc,cAAgB,iBAAgB,YAAa,QAAQ,UAAW,cAAc,aAAc,CAAE;AACjH,MAAK,cAAc,aAAe,gBAAe,YAAa,QAAQ,UAAW,cAAc,YAAa,CAAE;AAC9G,MAAK,cAAc,eAAiB,kBAAiB,YAAa,QAAQ,UAAW,cAAc,cAAe,CAAE;AACpH,MAAK,cAAc,cAAgB,iBAAgB,YAAa,QAAQ,UAAW,cAAc,aAAc,CAAE;AAGjH,MAAK,cAAc,mBAAoB;AAEtC,cAAU,KAAM,cAAc,YAAa;AAC3C,cAAU,KAAM,cAAc,iBAAkB;AAAA,EAEjD;AAEA,QAAM,OAAO,cAAc,MAAM,EAAE,SAAU,UAAW,EAAE,SAAU,cAAe;AAEnF,QAAM,aAAa,IAAI,QAAQ;AAC/B,aAAW,gBAAiB,SAAU;AAGtC,QAAM,YAAY,IAAI,QAAQ;AAC9B,YAAU,aAAc,SAAU;AAElC,QAAM,cAAc,UAAU,MAAM,EAAE,OAAO,EAAE,SAAU,SAAU;AACnE,QAAM,aAAa,WAAW,MAAM,EAAE,OAAO,EAAE,SAAU,WAAY;AACrE,QAAM,OAAO;AAEb,QAAM,YAAY,IAAI,QAAQ;AAE9B,MAAK,gBAAgB,GAAI;AAExB,cAAU,KAAM,UAAW,EAAE,SAAU,IAAK,EAAE,SAAU,UAAW,EAAE,SAAU,IAAK;AAAA,EAErF,WAAY,gBAAgB,GAAI;AAE/B,cAAU,KAAM,UAAW,EAAE,SAAU,UAAW,EAAE,SAAU,IAAK,EAAE,SAAU,IAAK;AAAA,EAErF,OAAO;AAEN,UAAM,aAAa,IAAI,QAAQ,EAAE,MAAO,IAAI,QAAQ,EAAE,mBAAoB,SAAU,CAAE;AACtF,UAAM,iBAAiB,WAAW,MAAM,EAAE,OAAO;AACjD,UAAM,qBAAqB,WAAW,MAAM,EAAE,SAAU,cAAe;AAEvE,cAAU,KAAM,UAAW,EAAE,SAAU,IAAK,EAAE,SAAU,kBAAmB,EAAE,SAAU,IAAK;AAAA,EAE7F;AAEA,QAAM,sBAAsB,gBAAgB,MAAM,EAAE,OAAO;AAC3D,QAAM,qBAAqB,eAAe,MAAM,EAAE,OAAO;AAEzD,MAAI,aAAa,cAAc,MAAM,EAAE,SAAU,gBAAiB,EAAE,SAAU,eAAgB,EAAE,SAAU,aAAc,EAAE,SAAU,UAAW,EAAE,SAAU,cAAe,EAAE,SAAU,mBAAoB,EAAE,SAAU,eAAgB,EAAE,SAAU,cAAe,EAAE,SAAU,SAAU,EAAE,SAAU,kBAAmB;AAEtT,QAAM,mCAAmC,IAAI,QAAQ,EAAE,aAAc,UAAW;AAEhF,QAAM,qBAAqB,UAAU,MAAM,EAAE,SAAU,gCAAiC;AACxF,WAAS,aAAc,kBAAmB;AAE1C,eAAa,SAAS,MAAM,EAAE,SAAU,SAAU;AAGlD,aAAW,YAAa,UAAU,OAAO,CAAE;AAE3C,SAAO;AAER;AAIA,SAAS,cAAe,OAAQ;AAE/B,UAAQ,SAAS;AAEjB,QAAM,QAAQ;AAAA,IACb;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA;AAAA,EAED;AAEA,MAAK,UAAU,GAAI;AAElB,YAAQ,KAAM,qGAAsG;AACpH,WAAO,MAAO,CAAE;AAAA,EAEjB;AAEA,SAAO,MAAO,KAAM;AAErB;AAIA,SAAS,iBAAkB,OAAQ;AAElC,QAAM,QAAQ,MAAM,MAAO,GAAI,EAAE,IAAK,SAAW,KAAM;AAEtD,WAAO,WAAY,GAAI;AAAA,EAExB,CAAE;AAEF,SAAO;AAER;AAEA,SAAS,2BAA4B,QAAQ,MAAM,IAAK;AAEvD,MAAK,SAAS,OAAY,QAAO;AACjC,MAAK,OAAO,OAAY,MAAK,OAAO;AAEpC,SAAO,IAAI,YAAY,EAAE,OAAQ,IAAI,WAAY,QAAQ,MAAM,EAAG,CAAE;AAErE;AAEA,SAAS,OAAQ,GAAG,GAAI;AAEvB,WAAU,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAAG,KAAM,KAAO;AAEhE,MAAG,CAAE,IAAI,EAAG,CAAE;AAAA,EAEf;AAED;AAEA,SAAS,MAAO,GAAG,GAAG,MAAM,IAAK;AAEhC,WAAU,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,KAAM,KAAO;AAE/C,MAAG,CAAE,IAAI,EAAG,CAAE;AAAA,EAEf;AAEA,SAAO;AAER;", "names": ["l", "_a", "_b", "i", "td", "_a", "fl", "Deflate", "AsyncDeflate", "Inflate", "AsyncInflate", "Gzip", "<PERSON><PERSON><PERSON>", "AsyncGunzip", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "Decompress", "AsyncDecompress", "DecodeUTF8", "_a", "ch", "EncodeUTF8", "_a", "fl", "ZipPassThrough", "ZipDeflate", "AsyncZipDeflate", "Zip", "fl", "_a", "_b", "UnzipPassThrough", "UnzipInflate", "AsyncUnzipInflate", "Unzip", "_a", "dat", "final", "r", "j", "child", "material", "skeleton"]}