{"version": 3, "sources": ["../../three/examples/jsm/utils/BufferGeometryUtils.js", "../../three/examples/jsm/loaders/GLTFLoader.js"], "sourcesContent": ["import {\n\tBufferAttribute,\n\tBufferGeometry,\n\tFloat32BufferAttribute,\n\tInstancedBufferAttribute,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tTrianglesDrawMode,\n\tVector3,\n} from 'three';\n\nfunction computeMikkTSpaceTangents( geometry, MikkTSpace, negateSign = true ) {\n\n\tif ( ! MikkTSpace || ! MikkTSpace.isReady ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Initialized MikkTSpace library required.' );\n\n\t}\n\n\tif ( ! geometry.hasAttribute( 'position' ) || ! geometry.hasAttribute( 'normal' ) || ! geometry.hasAttribute( 'uv' ) ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Tangents require \"position\", \"normal\", and \"uv\" attributes.' );\n\n\t}\n\n\tfunction getAttributeArray( attribute ) {\n\n\t\tif ( attribute.normalized || attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconst dstArray = new Float32Array( attribute.count * attribute.itemSize );\n\n\t\t\tfor ( let i = 0, j = 0; i < attribute.count; i ++ ) {\n\n\t\t\t\tdstArray[ j ++ ] = attribute.getX( i );\n\t\t\t\tdstArray[ j ++ ] = attribute.getY( i );\n\n\t\t\t\tif ( attribute.itemSize > 2 ) {\n\n\t\t\t\t\tdstArray[ j ++ ] = attribute.getZ( i );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn dstArray;\n\n\t\t}\n\n\t\tif ( attribute.array instanceof Float32Array ) {\n\n\t\t\treturn attribute.array;\n\n\t\t}\n\n\t\treturn new Float32Array( attribute.array );\n\n\t}\n\n\t// MikkTSpace algorithm requires non-indexed input.\n\n\tconst _geometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\n\t// Compute vertex tangents.\n\n\tconst tangents = MikkTSpace.generateTangents(\n\n\t\tgetAttributeArray( _geometry.attributes.position ),\n\t\tgetAttributeArray( _geometry.attributes.normal ),\n\t\tgetAttributeArray( _geometry.attributes.uv )\n\n\t);\n\n\t// Texture coordinate convention of glTF differs from the apparent\n\t// default of the MikkTSpace library; .w component must be flipped.\n\n\tif ( negateSign ) {\n\n\t\tfor ( let i = 3; i < tangents.length; i += 4 ) {\n\n\t\t\ttangents[ i ] *= - 1;\n\n\t\t}\n\n\t}\n\n\t//\n\n\t_geometry.setAttribute( 'tangent', new BufferAttribute( tangents, 4 ) );\n\n\tif ( geometry !== _geometry ) {\n\n\t\tgeometry.copy( _geometry );\n\n\t}\n\n\treturn geometry;\n\n}\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nfunction mergeGeometries( geometries, useGroups = false ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst morphAttributesUsed = new Set( Object.keys( geometries[ 0 ].morphAttributes ) );\n\n\tconst attributes = {};\n\tconst morphAttributes = {};\n\n\tconst morphTargetsRelative = geometries[ 0 ].morphTargetsRelative;\n\n\tconst mergedGeometry = new BufferGeometry();\n\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) attributes[ name ] = [];\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. Make sure all geometries have the same number of attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather morph attributes, exit early if they're different\n\n\t\tif ( morphTargetsRelative !== geometry.morphTargetsRelative ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. .morphTargetsRelative must be consistent throughout all geometries.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tfor ( const name in geometry.morphAttributes ) {\n\n\t\t\tif ( ! morphAttributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '.  .morphAttributes must be consistent throughout all geometries.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( morphAttributes[ name ] === undefined ) morphAttributes[ name ] = [];\n\n\t\t\tmorphAttributes[ name ].push( geometry.morphAttributes[ name ] );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. The geometry must have either an index or a position attribute' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.addGroup( offset, count, i );\n\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\n\tif ( isIndexed ) {\n\n\t\tlet indexOffset = 0;\n\t\tconst mergedIndex = [];\n\n\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\tconst index = geometries[ i ].index;\n\n\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\tmergedIndex.push( index.getX( j ) + indexOffset );\n\n\t\t\t}\n\n\t\t\tindexOffset += geometries[ i ].attributes.position.count;\n\n\t\t}\n\n\t\tmergedGeometry.setIndex( mergedIndex );\n\n\t}\n\n\t// merge attributes\n\n\tfor ( const name in attributes ) {\n\n\t\tconst mergedAttribute = mergeAttributes( attributes[ name ] );\n\n\t\tif ( ! mergedAttribute ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' attribute.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tmergedGeometry.setAttribute( name, mergedAttribute );\n\n\t}\n\n\t// merge morph attributes\n\n\tfor ( const name in morphAttributes ) {\n\n\t\tconst numMorphTargets = morphAttributes[ name ][ 0 ].length;\n\n\t\tif ( numMorphTargets === 0 ) break;\n\n\t\tmergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n\t\tmergedGeometry.morphAttributes[ name ] = [];\n\n\t\tfor ( let i = 0; i < numMorphTargets; ++ i ) {\n\n\t\t\tconst morphAttributesToMerge = [];\n\n\t\t\tfor ( let j = 0; j < morphAttributes[ name ].length; ++ j ) {\n\n\t\t\t\tmorphAttributesToMerge.push( morphAttributes[ name ][ j ][ i ] );\n\n\t\t\t}\n\n\t\t\tconst mergedMorphAttribute = mergeAttributes( morphAttributesToMerge );\n\n\t\t\tif ( ! mergedMorphAttribute ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' morphAttribute.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.morphAttributes[ name ].push( mergedMorphAttribute );\n\n\t\t}\n\n\t}\n\n\treturn mergedGeometry;\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nfunction mergeAttributes( attributes ) {\n\n\tlet TypedArray;\n\tlet itemSize;\n\tlet normalized;\n\tlet gpuType = - 1;\n\tlet arrayLength = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. InterleavedBufferAttributes are not supported.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( itemSize === undefined ) itemSize = attribute.itemSize;\n\t\tif ( itemSize !== attribute.itemSize ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( normalized === undefined ) normalized = attribute.normalized;\n\t\tif ( normalized !== attribute.normalized ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( gpuType === - 1 ) gpuType = attribute.gpuType;\n\t\tif ( gpuType !== attribute.gpuType ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.gpuType must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.array.length;\n\n\t}\n\n\tconst array = new TypedArray( arrayLength );\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tarray.set( attributes[ i ].array, offset );\n\n\t\toffset += attributes[ i ].array.length;\n\n\t}\n\n\tconst result = new BufferAttribute( array, itemSize, normalized );\n\tif ( gpuType !== undefined ) {\n\n\t\tresult.gpuType = gpuType;\n\n\t}\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferAttribute}\n * @return {BufferAttribute}\n */\nexport function deepCloneAttribute( attribute ) {\n\n\tif ( attribute.isInstancedInterleavedBufferAttribute || attribute.isInterleavedBufferAttribute ) {\n\n\t\treturn deinterleaveAttribute( attribute );\n\n\t}\n\n\tif ( attribute.isInstancedBufferAttribute ) {\n\n\t\treturn new InstancedBufferAttribute().copy( attribute );\n\n\t}\n\n\treturn new BufferAttribute().copy( attribute );\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nfunction interleaveAttributes( attributes ) {\n\n\t// Interleaves the provided attributes into an InterleavedBuffer and returns\n\t// a set of InterleavedBufferAttributes for each attribute\n\tlet TypedArray;\n\tlet arrayLength = 0;\n\tlet stride = 0;\n\n\t// calculate the length and type of the interleavedBuffer\n\tfor ( let i = 0, l = attributes.length; i < l; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'AttributeBuffers of different types cannot be interleaved' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.array.length;\n\t\tstride += attribute.itemSize;\n\n\t}\n\n\t// Create the set of buffer attributes\n\tconst interleavedBuffer = new InterleavedBuffer( new TypedArray( arrayLength ), stride );\n\tlet offset = 0;\n\tconst res = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\tfor ( let j = 0, l = attributes.length; j < l; j ++ ) {\n\n\t\tconst attribute = attributes[ j ];\n\t\tconst itemSize = attribute.itemSize;\n\t\tconst count = attribute.count;\n\t\tconst iba = new InterleavedBufferAttribute( interleavedBuffer, itemSize, offset, attribute.normalized );\n\t\tres.push( iba );\n\n\t\toffset += itemSize;\n\n\t\t// Move the data for each attribute into the new interleavedBuffer\n\t\t// at the appropriate offset\n\t\tfor ( let c = 0; c < count; c ++ ) {\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\tiba[ setters[ k ] ]( c, attribute[ getters[ k ] ]( c ) );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// returns a new, non-interleaved version of the provided attribute\nexport function deinterleaveAttribute( attribute ) {\n\n\tconst cons = attribute.data.array.constructor;\n\tconst count = attribute.count;\n\tconst itemSize = attribute.itemSize;\n\tconst normalized = attribute.normalized;\n\n\tconst array = new cons( count * itemSize );\n\tlet newAttribute;\n\tif ( attribute.isInstancedInterleavedBufferAttribute ) {\n\n\t\tnewAttribute = new InstancedBufferAttribute( array, itemSize, normalized, attribute.meshPerAttribute );\n\n\t} else {\n\n\t\tnewAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t}\n\n\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\tnewAttribute.setX( i, attribute.getX( i ) );\n\n\t\tif ( itemSize >= 2 ) {\n\n\t\t\tnewAttribute.setY( i, attribute.getY( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 3 ) {\n\n\t\t\tnewAttribute.setZ( i, attribute.getZ( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 4 ) {\n\n\t\t\tnewAttribute.setW( i, attribute.getW( i ) );\n\n\t\t}\n\n\t}\n\n\treturn newAttribute;\n\n}\n\n// deinterleaves all attributes on the geometry\nexport function deinterleaveGeometry( geometry ) {\n\n\tconst attributes = geometry.attributes;\n\tconst morphTargets = geometry.morphTargets;\n\tconst attrMap = new Map();\n\n\tfor ( const key in attributes ) {\n\n\t\tconst attr = attributes[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tattributes[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n\tfor ( const key in morphTargets ) {\n\n\t\tconst attr = morphTargets[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tmorphTargets[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @return {number}\n */\nfunction estimateBytesUsed( geometry ) {\n\n\t// Return the estimated memory used by this geometry in bytes\n\t// Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n\t// for InterleavedBufferAttributes.\n\tlet mem = 0;\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst attr = geometry.getAttribute( name );\n\t\tmem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n\n\t}\n\n\tconst indices = geometry.getIndex();\n\tmem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n\treturn mem;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry}\n */\nfunction mergeVertices( geometry, tolerance = 1e-4 ) {\n\n\ttolerance = Math.max( tolerance, Number.EPSILON );\n\n\t// Generate an index buffer if the geometry doesn't have one, or optimize it\n\t// if it's already available.\n\tconst hashToIndex = {};\n\tconst indices = geometry.getIndex();\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst vertexCount = indices ? indices.count : positions.count;\n\n\t// next value for triangle indices\n\tlet nextIndex = 0;\n\n\t// attributes and new attribute arrays\n\tconst attributeNames = Object.keys( geometry.attributes );\n\tconst tmpAttributes = {};\n\tconst tmpMorphAttributes = {};\n\tconst newIndices = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\t// Initialize the arrays, allocating space conservatively. Extra\n\t// space will be trimmed in the last step.\n\tfor ( let i = 0, l = attributeNames.length; i < l; i ++ ) {\n\n\t\tconst name = attributeNames[ i ];\n\t\tconst attr = geometry.attributes[ name ];\n\n\t\ttmpAttributes[ name ] = new BufferAttribute(\n\t\t\tnew attr.array.constructor( attr.count * attr.itemSize ),\n\t\t\tattr.itemSize,\n\t\t\tattr.normalized\n\t\t);\n\n\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\tif ( morphAttr ) {\n\n\t\t\ttmpMorphAttributes[ name ] = new BufferAttribute(\n\t\t\t\tnew morphAttr.array.constructor( morphAttr.count * morphAttr.itemSize ),\n\t\t\t\tmorphAttr.itemSize,\n\t\t\t\tmorphAttr.normalized\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// convert the error tolerance to an amount of decimal places to truncate to\n\tconst halfTolerance = tolerance * 0.5;\n\tconst exponent = Math.log10( 1 / tolerance );\n\tconst hashMultiplier = Math.pow( 10, exponent );\n\tconst hashAdditive = halfTolerance * hashMultiplier;\n\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\tconst index = indices ? indices.getX( i ) : i;\n\n\t\t// Generate a hash for the vertex attributes at the current index 'i'\n\t\tlet hash = '';\n\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\tconst name = attributeNames[ j ];\n\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\tconst itemSize = attribute.itemSize;\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t// double tilde truncates the decimal value\n\t\t\t\thash += `${ ~ ~ ( attribute[ getters[ k ] ]( index ) * hashMultiplier + hashAdditive ) },`;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Add another reference to the vertex if it's already\n\t\t// used by another index\n\t\tif ( hash in hashToIndex ) {\n\n\t\t\tnewIndices.push( hashToIndex[ hash ] );\n\n\t\t} else {\n\n\t\t\t// copy data to the new index in the temporary attributes\n\t\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\t\tconst name = attributeNames[ j ];\n\t\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\t\t\tconst itemSize = attribute.itemSize;\n\t\t\t\tconst newarray = tmpAttributes[ name ];\n\t\t\t\tconst newMorphArrays = tmpMorphAttributes[ name ];\n\n\t\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t\tconst getterFunc = getters[ k ];\n\t\t\t\t\tconst setterFunc = setters[ k ];\n\t\t\t\t\tnewarray[ setterFunc ]( nextIndex, attribute[ getterFunc ]( index ) );\n\n\t\t\t\t\tif ( morphAttr ) {\n\n\t\t\t\t\t\tfor ( let m = 0, ml = morphAttr.length; m < ml; m ++ ) {\n\n\t\t\t\t\t\t\tnewMorphArrays[ m ][ setterFunc ]( nextIndex, morphAttr[ m ][ getterFunc ]( index ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\thashToIndex[ hash ] = nextIndex;\n\t\t\tnewIndices.push( nextIndex );\n\t\t\tnextIndex ++;\n\n\t\t}\n\n\t}\n\n\t// generate result BufferGeometry\n\tconst result = geometry.clone();\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst tmpAttribute = tmpAttributes[ name ];\n\n\t\tresult.setAttribute( name, new BufferAttribute(\n\t\t\ttmpAttribute.array.slice( 0, nextIndex * tmpAttribute.itemSize ),\n\t\t\ttmpAttribute.itemSize,\n\t\t\ttmpAttribute.normalized,\n\t\t) );\n\n\t\tif ( ! ( name in tmpMorphAttributes ) ) continue;\n\n\t\tfor ( let j = 0; j < tmpMorphAttributes[ name ].length; j ++ ) {\n\n\t\t\tconst tmpMorphAttribute = tmpMorphAttributes[ name ][ j ];\n\n\t\t\tresult.morphAttributes[ name ][ j ] = new BufferAttribute(\n\t\t\t\ttmpMorphAttribute.array.slice( 0, nextIndex * tmpMorphAttribute.itemSize ),\n\t\t\t\ttmpMorphAttribute.itemSize,\n\t\t\t\ttmpMorphAttribute.normalized,\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// indices\n\n\tresult.setIndex( newIndices );\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nfunction toTrianglesDrawMode( geometry, drawMode ) {\n\n\tif ( drawMode === TrianglesDrawMode ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.' );\n\t\treturn geometry;\n\n\t}\n\n\tif ( drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode ) {\n\n\t\tlet index = geometry.getIndex();\n\n\t\t// generate index if not present\n\n\t\tif ( index === null ) {\n\n\t\t\tconst indices = [];\n\n\t\t\tconst position = geometry.getAttribute( 'position' );\n\n\t\t\tif ( position !== undefined ) {\n\n\t\t\t\tfor ( let i = 0; i < position.count; i ++ ) {\n\n\t\t\t\t\tindices.push( i );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.setIndex( indices );\n\t\t\t\tindex = geometry.getIndex();\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.' );\n\t\t\t\treturn geometry;\n\n\t\t\t}\n\n\t\t}\n\n\t\t//\n\n\t\tconst numberOfTriangles = index.count - 2;\n\t\tconst newIndices = [];\n\n\t\tif ( drawMode === TriangleFanDrawMode ) {\n\n\t\t\t// gl.TRIANGLE_FAN\n\n\t\t\tfor ( let i = 1; i <= numberOfTriangles; i ++ ) {\n\n\t\t\t\tnewIndices.push( index.getX( 0 ) );\n\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// gl.TRIANGLE_STRIP\n\n\t\t\tfor ( let i = 0; i < numberOfTriangles; i ++ ) {\n\n\t\t\t\tif ( i % 2 === 0 ) {\n\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( ( newIndices.length / 3 ) !== numberOfTriangles ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.' );\n\n\t\t}\n\n\t\t// build final geometry\n\n\t\tconst newGeometry = geometry.clone();\n\t\tnewGeometry.setIndex( newIndices );\n\t\tnewGeometry.clearGroups();\n\n\t\treturn newGeometry;\n\n\t} else {\n\n\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode );\n\t\treturn geometry;\n\n\t}\n\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nfunction computeMorphedAttributes( object ) {\n\n\tconst _vA = new Vector3();\n\tconst _vB = new Vector3();\n\tconst _vC = new Vector3();\n\n\tconst _tempA = new Vector3();\n\tconst _tempB = new Vector3();\n\tconst _tempC = new Vector3();\n\n\tconst _morphA = new Vector3();\n\tconst _morphB = new Vector3();\n\tconst _morphC = new Vector3();\n\n\tfunction _calculateMorphedAttributeData(\n\t\tobject,\n\t\tattribute,\n\t\tmorphAttribute,\n\t\tmorphTargetsRelative,\n\t\ta,\n\t\tb,\n\t\tc,\n\t\tmodifiedAttributeArray\n\t) {\n\n\t\t_vA.fromBufferAttribute( attribute, a );\n\t\t_vB.fromBufferAttribute( attribute, b );\n\t\t_vC.fromBufferAttribute( attribute, c );\n\n\t\tconst morphInfluences = object.morphTargetInfluences;\n\n\t\tif ( morphAttribute && morphInfluences ) {\n\n\t\t\t_morphA.set( 0, 0, 0 );\n\t\t\t_morphB.set( 0, 0, 0 );\n\t\t\t_morphC.set( 0, 0, 0 );\n\n\t\t\tfor ( let i = 0, il = morphAttribute.length; i < il; i ++ ) {\n\n\t\t\t\tconst influence = morphInfluences[ i ];\n\t\t\t\tconst morph = morphAttribute[ i ];\n\n\t\t\t\tif ( influence === 0 ) continue;\n\n\t\t\t\t_tempA.fromBufferAttribute( morph, a );\n\t\t\t\t_tempB.fromBufferAttribute( morph, b );\n\t\t\t\t_tempC.fromBufferAttribute( morph, c );\n\n\t\t\t\tif ( morphTargetsRelative ) {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA, influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB, influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC, influence );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA.sub( _vA ), influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB.sub( _vB ), influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC.sub( _vC ), influence );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t_vA.add( _morphA );\n\t\t\t_vB.add( _morphB );\n\t\t\t_vC.add( _morphC );\n\n\t\t}\n\n\t\tif ( object.isSkinnedMesh ) {\n\n\t\t\tobject.applyBoneTransform( a, _vA );\n\t\t\tobject.applyBoneTransform( b, _vB );\n\t\t\tobject.applyBoneTransform( c, _vC );\n\n\t\t}\n\n\t\tmodifiedAttributeArray[ a * 3 + 0 ] = _vA.x;\n\t\tmodifiedAttributeArray[ a * 3 + 1 ] = _vA.y;\n\t\tmodifiedAttributeArray[ a * 3 + 2 ] = _vA.z;\n\t\tmodifiedAttributeArray[ b * 3 + 0 ] = _vB.x;\n\t\tmodifiedAttributeArray[ b * 3 + 1 ] = _vB.y;\n\t\tmodifiedAttributeArray[ b * 3 + 2 ] = _vB.z;\n\t\tmodifiedAttributeArray[ c * 3 + 0 ] = _vC.x;\n\t\tmodifiedAttributeArray[ c * 3 + 1 ] = _vC.y;\n\t\tmodifiedAttributeArray[ c * 3 + 2 ] = _vC.z;\n\n\t}\n\n\tconst geometry = object.geometry;\n\tconst material = object.material;\n\n\tlet a, b, c;\n\tconst index = geometry.index;\n\tconst positionAttribute = geometry.attributes.position;\n\tconst morphPosition = geometry.morphAttributes.position;\n\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\tconst normalAttribute = geometry.attributes.normal;\n\tconst morphNormal = geometry.morphAttributes.position;\n\n\tconst groups = geometry.groups;\n\tconst drawRange = geometry.drawRange;\n\tlet i, j, il, jl;\n\tlet group;\n\tlet start, end;\n\n\tconst modifiedPosition = new Float32Array( positionAttribute.count * positionAttribute.itemSize );\n\tconst modifiedNormal = new Float32Array( normalAttribute.count * normalAttribute.itemSize );\n\n\tif ( index !== null ) {\n\n\t\t// indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = index.getX( j );\n\t\t\t\t\tb = index.getX( j + 1 );\n\t\t\t\t\tc = index.getX( j + 2 );\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( index.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = index.getX( i );\n\t\t\t\tb = index.getX( i + 1 );\n\t\t\t\tc = index.getX( i + 2 );\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\t// non-indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = j;\n\t\t\t\t\tb = j + 1;\n\t\t\t\t\tc = j + 2;\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( positionAttribute.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = i;\n\t\t\t\tb = i + 1;\n\t\t\t\tc = i + 2;\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tconst morphedPositionAttribute = new Float32BufferAttribute( modifiedPosition, 3 );\n\tconst morphedNormalAttribute = new Float32BufferAttribute( modifiedNormal, 3 );\n\n\treturn {\n\n\t\tpositionAttribute: positionAttribute,\n\t\tnormalAttribute: normalAttribute,\n\t\tmorphedPositionAttribute: morphedPositionAttribute,\n\t\tmorphedNormalAttribute: morphedNormalAttribute\n\n\t};\n\n}\n\nfunction mergeGroups( geometry ) {\n\n\tif ( geometry.groups.length === 0 ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.mergeGroups(): No groups are defined. Nothing to merge.' );\n\t\treturn geometry;\n\n\t}\n\n\tlet groups = geometry.groups;\n\n\t// sort groups by material index\n\n\tgroups = groups.sort( ( a, b ) => {\n\n\t\tif ( a.materialIndex !== b.materialIndex ) return a.materialIndex - b.materialIndex;\n\n\t\treturn a.start - b.start;\n\n\t} );\n\n\t// create index for non-indexed geometries\n\n\tif ( geometry.getIndex() === null ) {\n\n\t\tconst positionAttribute = geometry.getAttribute( 'position' );\n\t\tconst indices = [];\n\n\t\tfor ( let i = 0; i < positionAttribute.count; i += 3 ) {\n\n\t\t\tindices.push( i, i + 1, i + 2 );\n\n\t\t}\n\n\t\tgeometry.setIndex( indices );\n\n\t}\n\n\t// sort index\n\n\tconst index = geometry.getIndex();\n\n\tconst newIndices = [];\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tconst groupStart = group.start;\n\t\tconst groupLength = groupStart + group.count;\n\n\t\tfor ( let j = groupStart; j < groupLength; j ++ ) {\n\n\t\t\tnewIndices.push( index.getX( j ) );\n\n\t\t}\n\n\t}\n\n\tgeometry.dispose(); // Required to force buffer recreation\n\tgeometry.setIndex( newIndices );\n\n\t// update groups indices\n\n\tlet start = 0;\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tgroup.start = start;\n\t\tstart += group.count;\n\n\t}\n\n\t// merge groups\n\n\tlet currentGroup = groups[ 0 ];\n\n\tgeometry.groups = [ currentGroup ];\n\n\tfor ( let i = 1; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tif ( currentGroup.materialIndex === group.materialIndex ) {\n\n\t\t\tcurrentGroup.count += group.count;\n\n\t\t} else {\n\n\t\t\tcurrentGroup = group;\n\t\t\tgeometry.groups.push( currentGroup );\n\n\t\t}\n\n\t}\n\n\treturn geometry;\n\n}\n\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * @param {BufferGeometry} geometry\n * @param {number} [creaseAngle]\n * @return {BufferGeometry}\n */\nfunction toCreasedNormals( geometry, creaseAngle = Math.PI / 3 /* 60 degrees */ ) {\n\n\tconst creaseDot = Math.cos( creaseAngle );\n\tconst hashMultiplier = ( 1 + 1e-10 ) * 1e2;\n\n\t// reusable vectors\n\tconst verts = [ new Vector3(), new Vector3(), new Vector3() ];\n\tconst tempVec1 = new Vector3();\n\tconst tempVec2 = new Vector3();\n\tconst tempNorm = new Vector3();\n\tconst tempNorm2 = new Vector3();\n\n\t// hashes a vector\n\tfunction hashVertex( v ) {\n\n\t\tconst x = ~ ~ ( v.x * hashMultiplier );\n\t\tconst y = ~ ~ ( v.y * hashMultiplier );\n\t\tconst z = ~ ~ ( v.z * hashMultiplier );\n\t\treturn `${x},${y},${z}`;\n\n\t}\n\n\t// BufferGeometry.toNonIndexed() warns if the geometry is non-indexed\n\t// and returns the original geometry\n\tconst resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\tconst posAttr = resultGeometry.attributes.position;\n\tconst vertexMap = {};\n\n\t// find all the normals shared by commonly located vertices\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\t// add the normal to the map for all vertices\n\t\tconst normal = new Vector3().crossVectors( tempVec1, tempVec2 ).normalize();\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tif ( ! ( hash in vertexMap ) ) {\n\n\t\t\t\tvertexMap[ hash ] = [];\n\n\t\t\t}\n\n\t\t\tvertexMap[ hash ].push( normal );\n\n\t\t}\n\n\t}\n\n\t// average normals from all vertices that share a common location if they are within the\n\t// provided crease threshold\n\tconst normalArray = new Float32Array( posAttr.count * 3 );\n\tconst normAttr = new BufferAttribute( normalArray, 3, false );\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\t// get the face normal for this vertex\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\ttempNorm.crossVectors( tempVec1, tempVec2 ).normalize();\n\n\t\t// average all normals that meet the threshold and set the normal value\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tconst otherNormals = vertexMap[ hash ];\n\t\t\ttempNorm2.set( 0, 0, 0 );\n\n\t\t\tfor ( let k = 0, lk = otherNormals.length; k < lk; k ++ ) {\n\n\t\t\t\tconst otherNorm = otherNormals[ k ];\n\t\t\t\tif ( tempNorm.dot( otherNorm ) > creaseDot ) {\n\n\t\t\t\t\ttempNorm2.add( otherNorm );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ttempNorm2.normalize();\n\t\t\tnormAttr.setXYZ( i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z );\n\n\t\t}\n\n\t}\n\n\tresultGeometry.setAttribute( 'normal', normAttr );\n\treturn resultGeometry;\n\n}\n\nfunction mergeBufferGeometries( geometries, useGroups = false ) {\n\n\tconsole.warn( 'THREE.BufferGeometryUtils: mergeBufferGeometries() has been renamed to mergeGeometries().' ); // @deprecated, r151\n\treturn mergeGeometries( geometries, useGroups );\n\n}\n\nfunction mergeBufferAttributes( attributes ) {\n\n\tconsole.warn( 'THREE.BufferGeometryUtils: mergeBufferAttributes() has been renamed to mergeAttributes().' ); // @deprecated, r151\n\treturn mergeAttributes( attributes );\n\n}\n\nexport {\n\tcomputeMikkTSpaceTangents,\n\tmergeGeometries,\n\tmergeBufferGeometries,\n\tmergeAttributes,\n\tmergeBufferAttributes,\n\tinterleaveAttributes,\n\testimateBytesUsed,\n\tmergeVertices,\n\ttoTrianglesDrawMode,\n\tcomputeMorphedAttributes,\n\tmergeGroups,\n\ttoCreasedNormals\n};\n", "import {\n\tAnimationClip,\n\tBone,\n\tBox3,\n\tBufferAttribute,\n\tBufferGeometry,\n\tClampToEdgeWrapping,\n\tColor,\n\tColorManagement,\n\tDirectionalLight,\n\tDoubleSide,\n\tFileLoader,\n\tFrontSide,\n\tGroup,\n\tImageBitmapLoader,\n\tInstancedMesh,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tInterpolant,\n\tInterpolateDiscrete,\n\tInterpolateLinear,\n\tLine,\n\tLineBasicMaterial,\n\tLineLoop,\n\tLineSegments,\n\tLinearFilter,\n\tLinearMipmapLinearFilter,\n\tLinearMipmapNearestFilter,\n\tLinearSRGBColorSpace,\n\tLoader,\n\tLoaderUtils,\n\tMaterial,\n\tMathUtils,\n\tMatrix4,\n\tMesh,\n\tMeshBasicMaterial,\n\tMeshPhysicalMaterial,\n\tMeshStandardMaterial,\n\tMirroredRepeatWrapping,\n\tNearestFilter,\n\tNearestMipmapLinearFilter,\n\tNearestMipmapNearestFilter,\n\tNumberKeyframeTrack,\n\tObject3D,\n\tOrthographicCamera,\n\tPerspectiveCamera,\n\tPointLight,\n\tPoints,\n\tPointsMaterial,\n\tPropertyBinding,\n\tQuaternion,\n\tQuaternionKeyframeTrack,\n\tRepeatWrapping,\n\tSkeleton,\n\tSkinnedMesh,\n\tSphere,\n\tSpotLight,\n\tTexture,\n\tTextureLoader,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tVector2,\n\tVector3,\n\tVectorKeyframeTrack,\n\tSRGBColorSpace,\n\tInstancedBufferAttribute\n} from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\n\nclass GLTFLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.dracoLoader = null;\n\t\tthis.ktx2Loader = null;\n\t\tthis.meshoptDecoder = null;\n\n\t\tthis.pluginCallbacks = [];\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsClearcoatExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureBasisUExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureWebPExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureAVIFExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSheenExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsTransmissionExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsVolumeExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIorExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsEmissiveStrengthExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSpecularExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIridescenceExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsAnisotropyExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsBumpExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFLightsExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshoptCompression( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshGpuInstancing( parser );\n\n\t\t} );\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tlet resourcePath;\n\n\t\tif ( this.resourcePath !== '' ) {\n\n\t\t\tresourcePath = this.resourcePath;\n\n\t\t} else if ( this.path !== '' ) {\n\n\t\t\t// If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n\t\t\t// Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n\t\t\t// resourcePath = 'https://my-cnd-server.com/assets/models/'\n\t\t\t// referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n\t\t\t// referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n\t\t\tconst relativeUrl = LoaderUtils.extractUrlBase( url );\n\t\t\tresourcePath = LoaderUtils.resolveURL( relativeUrl, this.path );\n\n\t\t} else {\n\n\t\t\tresourcePath = LoaderUtils.extractUrlBase( url );\n\n\t\t}\n\n\t\t// Tells the LoadingManager to track an extra item, which resolves after\n\t\t// the model is fully loaded. This means the count of items loaded will\n\t\t// be incorrect, but ensures manager.onLoad() does not fire early.\n\t\tthis.manager.itemStart( url );\n\n\t\tconst _onError = function ( e ) {\n\n\t\t\tif ( onError ) {\n\n\t\t\t\tonError( e );\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( e );\n\n\t\t\t}\n\n\t\t\tscope.manager.itemError( url );\n\t\t\tscope.manager.itemEnd( url );\n\n\t\t};\n\n\t\tconst loader = new FileLoader( this.manager );\n\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, function ( data ) {\n\n\t\t\ttry {\n\n\t\t\t\tscope.parse( data, resourcePath, function ( gltf ) {\n\n\t\t\t\t\tonLoad( gltf );\n\n\t\t\t\t\tscope.manager.itemEnd( url );\n\n\t\t\t\t}, _onError );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\t_onError( e );\n\n\t\t\t}\n\n\t\t}, onProgress, _onError );\n\n\t}\n\n\tsetDRACOLoader( dracoLoader ) {\n\n\t\tthis.dracoLoader = dracoLoader;\n\t\treturn this;\n\n\t}\n\n\tsetDDSLoader() {\n\n\t\tthrow new Error(\n\n\t\t\t'THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".'\n\n\t\t);\n\n\t}\n\n\tsetKTX2Loader( ktx2Loader ) {\n\n\t\tthis.ktx2Loader = ktx2Loader;\n\t\treturn this;\n\n\t}\n\n\tsetMeshoptDecoder( meshoptDecoder ) {\n\n\t\tthis.meshoptDecoder = meshoptDecoder;\n\t\treturn this;\n\n\t}\n\n\tregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) === - 1 ) {\n\n\t\t\tthis.pluginCallbacks.push( callback );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\tunregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) !== - 1 ) {\n\n\t\t\tthis.pluginCallbacks.splice( this.pluginCallbacks.indexOf( callback ), 1 );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\tparse( data, path, onLoad, onError ) {\n\n\t\tlet json;\n\t\tconst extensions = {};\n\t\tconst plugins = {};\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tif ( typeof data === 'string' ) {\n\n\t\t\tjson = JSON.parse( data );\n\n\t\t} else if ( data instanceof ArrayBuffer ) {\n\n\t\t\tconst magic = textDecoder.decode( new Uint8Array( data, 0, 4 ) );\n\n\t\t\tif ( magic === BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\t\ttry {\n\n\t\t\t\t\textensions[ EXTENSIONS.KHR_BINARY_GLTF ] = new GLTFBinaryExtension( data );\n\n\t\t\t\t} catch ( error ) {\n\n\t\t\t\t\tif ( onError ) onError( error );\n\t\t\t\t\treturn;\n\n\t\t\t\t}\n\n\t\t\t\tjson = JSON.parse( extensions[ EXTENSIONS.KHR_BINARY_GLTF ].content );\n\n\t\t\t} else {\n\n\t\t\t\tjson = JSON.parse( textDecoder.decode( data ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tjson = data;\n\n\t\t}\n\n\t\tif ( json.asset === undefined || json.asset.version[ 0 ] < 2 ) {\n\n\t\t\tif ( onError ) onError( new Error( 'THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.' ) );\n\t\t\treturn;\n\n\t\t}\n\n\t\tconst parser = new GLTFParser( json, {\n\n\t\t\tpath: path || this.resourcePath || '',\n\t\t\tcrossOrigin: this.crossOrigin,\n\t\t\trequestHeader: this.requestHeader,\n\t\t\tmanager: this.manager,\n\t\t\tktx2Loader: this.ktx2Loader,\n\t\t\tmeshoptDecoder: this.meshoptDecoder\n\n\t\t} );\n\n\t\tparser.fileLoader.setRequestHeader( this.requestHeader );\n\n\t\tfor ( let i = 0; i < this.pluginCallbacks.length; i ++ ) {\n\n\t\t\tconst plugin = this.pluginCallbacks[ i ]( parser );\n\n\t\t\tif ( ! plugin.name ) console.error( 'THREE.GLTFLoader: Invalid plugin found: missing name' );\n\n\t\t\tplugins[ plugin.name ] = plugin;\n\n\t\t\t// Workaround to avoid determining as unknown extension\n\t\t\t// in addUnknownExtensionsToUserData().\n\t\t\t// Remove this workaround if we move all the existing\n\t\t\t// extension handlers to plugin system\n\t\t\textensions[ plugin.name ] = true;\n\n\t\t}\n\n\t\tif ( json.extensionsUsed ) {\n\n\t\t\tfor ( let i = 0; i < json.extensionsUsed.length; ++ i ) {\n\n\t\t\t\tconst extensionName = json.extensionsUsed[ i ];\n\t\t\t\tconst extensionsRequired = json.extensionsRequired || [];\n\n\t\t\t\tswitch ( extensionName ) {\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MATERIALS_UNLIT:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMaterialsUnlitExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFDracoMeshCompressionExtension( json, this.dracoLoader );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFTextureTransformExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MESH_QUANTIZATION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMeshQuantizationExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\n\t\t\t\t\t\tif ( extensionsRequired.indexOf( extensionName ) >= 0 && plugins[ extensionName ] === undefined ) {\n\n\t\t\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".' );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tparser.setExtensions( extensions );\n\t\tparser.setPlugins( plugins );\n\t\tparser.parse( onLoad, onError );\n\n\t}\n\n\tparseAsync( data, path ) {\n\n\t\tconst scope = this;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tscope.parse( data, path, resolve, reject );\n\n\t\t} );\n\n\t}\n\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n\n\tlet objects = {};\n\n\treturn\t{\n\n\t\tget: function ( key ) {\n\n\t\t\treturn objects[ key ];\n\n\t\t},\n\n\t\tadd: function ( key, object ) {\n\n\t\t\tobjects[ key ] = object;\n\n\t\t},\n\n\t\tremove: function ( key ) {\n\n\t\t\tdelete objects[ key ];\n\n\t\t},\n\n\t\tremoveAll: function () {\n\n\t\t\tobjects = {};\n\n\t\t}\n\n\t};\n\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n\tKHR_BINARY_GLTF: 'KHR_binary_glTF',\n\tKHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n\tKHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n\tKHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n\tKHR_MATERIALS_IOR: 'KHR_materials_ior',\n\tKHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n\tKHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n\tKHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n\tKHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n\tKHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n\tKHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n\tKHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n\tKHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n\tKHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n\tKHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n\tKHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n\tEXT_MATERIALS_BUMP: 'EXT_materials_bump',\n\tEXT_TEXTURE_WEBP: 'EXT_texture_webp',\n\tEXT_TEXTURE_AVIF: 'EXT_texture_avif',\n\tEXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n\tEXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n\t\t// Object3D instance caches\n\t\tthis.cache = { refs: {}, uses: {} };\n\n\t}\n\n\t_markDefs() {\n\n\t\tconst parser = this.parser;\n\t\tconst nodeDefs = this.parser.json.nodes || [];\n\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.extensions\n\t\t\t\t\t&& nodeDef.extensions[ this.name ]\n\t\t\t\t\t&& nodeDef.extensions[ this.name ].light !== undefined ) {\n\n\t\t\t\tparser._addNodeRef( this.cache, nodeDef.extensions[ this.name ].light );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t_loadLight( lightIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst cacheKey = 'light:' + lightIndex;\n\t\tlet dependency = parser.cache.get( cacheKey );\n\n\t\tif ( dependency ) return dependency;\n\n\t\tconst json = parser.json;\n\t\tconst extensions = ( json.extensions && json.extensions[ this.name ] ) || {};\n\t\tconst lightDefs = extensions.lights || [];\n\t\tconst lightDef = lightDefs[ lightIndex ];\n\t\tlet lightNode;\n\n\t\tconst color = new Color( 0xffffff );\n\n\t\tif ( lightDef.color !== undefined ) color.setRGB( lightDef.color[ 0 ], lightDef.color[ 1 ], lightDef.color[ 2 ], LinearSRGBColorSpace );\n\n\t\tconst range = lightDef.range !== undefined ? lightDef.range : 0;\n\n\t\tswitch ( lightDef.type ) {\n\n\t\t\tcase 'directional':\n\t\t\t\tlightNode = new DirectionalLight( color );\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tcase 'point':\n\t\t\t\tlightNode = new PointLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\tbreak;\n\n\t\t\tcase 'spot':\n\t\t\t\tlightNode = new SpotLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\t// Handle spotlight properties.\n\t\t\t\tlightDef.spot = lightDef.spot || {};\n\t\t\t\tlightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n\t\t\t\tlightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n\t\t\t\tlightNode.angle = lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Unexpected light type: ' + lightDef.type );\n\n\t\t}\n\n\t\t// Some lights (e.g. spot) default to a position other than the origin. Reset the position\n\t\t// here, because node-level parsing will only override position if explicitly specified.\n\t\tlightNode.position.set( 0, 0, 0 );\n\n\t\tlightNode.decay = 2;\n\n\t\tassignExtrasToUserData( lightNode, lightDef );\n\n\t\tif ( lightDef.intensity !== undefined ) lightNode.intensity = lightDef.intensity;\n\n\t\tlightNode.name = parser.createUniqueName( lightDef.name || ( 'light_' + lightIndex ) );\n\n\t\tdependency = Promise.resolve( lightNode );\n\n\t\tparser.cache.add( cacheKey, dependency );\n\n\t\treturn dependency;\n\n\t}\n\n\tgetDependency( type, index ) {\n\n\t\tif ( type !== 'light' ) return;\n\n\t\treturn this._loadLight( index );\n\n\t}\n\n\tcreateNodeAttachment( nodeIndex ) {\n\n\t\tconst self = this;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\t\tconst lightDef = ( nodeDef.extensions && nodeDef.extensions[ this.name ] ) || {};\n\t\tconst lightIndex = lightDef.light;\n\n\t\tif ( lightIndex === undefined ) return null;\n\n\t\treturn this._loadLight( lightIndex ).then( function ( light ) {\n\n\t\t\treturn parser._getNodeRef( self.cache, lightIndex, light );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n\n\t}\n\n\tgetMaterialType() {\n\n\t\treturn MeshBasicMaterial;\n\n\t}\n\n\textendParams( materialParams, materialDef, parser ) {\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\tmaterialParams.opacity = 1.0;\n\n\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness;\n\n\t\tif ( metallicRoughness ) {\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.setRGB( array[ 0 ], array[ 1 ], array[ 2 ], LinearSRGBColorSpace );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst emissiveStrength = materialDef.extensions[ this.name ].emissiveStrength;\n\n\t\tif ( emissiveStrength !== undefined ) {\n\n\t\t\tmaterialParams.emissiveIntensity = emissiveStrength;\n\n\t\t}\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.clearcoatFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoat = extension.clearcoatFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatMap', extension.clearcoatTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatNormalTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture ) );\n\n\t\t\tif ( extension.clearcoatNormalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = extension.clearcoatNormalTexture.scale;\n\n\t\t\t\tmaterialParams.clearcoatNormalScale = new Vector2( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.iridescenceFactor !== undefined ) {\n\n\t\t\tmaterialParams.iridescence = extension.iridescenceFactor;\n\n\t\t}\n\n\t\tif ( extension.iridescenceTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceMap', extension.iridescenceTexture ) );\n\n\t\t}\n\n\t\tif ( extension.iridescenceIor !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceIOR = extension.iridescenceIor;\n\n\t\t}\n\n\t\tif ( materialParams.iridescenceThicknessRange === undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange = [ 100, 400 ];\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMinimum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 0 ] = extension.iridescenceThicknessMinimum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMaximum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 1 ] = extension.iridescenceThicknessMaximum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.sheenColor = new Color( 0, 0, 0 );\n\t\tmaterialParams.sheenRoughness = 0;\n\t\tmaterialParams.sheen = 1;\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.sheenColorFactor !== undefined ) {\n\n\t\t\tconst colorFactor = extension.sheenColorFactor;\n\t\t\tmaterialParams.sheenColor.setRGB( colorFactor[ 0 ], colorFactor[ 1 ], colorFactor[ 2 ], LinearSRGBColorSpace );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.sheenRoughness = extension.sheenRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.sheenColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.transmissionFactor !== undefined ) {\n\n\t\t\tmaterialParams.transmission = extension.transmissionFactor;\n\n\t\t}\n\n\t\tif ( extension.transmissionTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'transmissionMap', extension.transmissionTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n\n\t\tif ( extension.thicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'thicknessMap', extension.thicknessTexture ) );\n\n\t\t}\n\n\t\tmaterialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n\n\t\tconst colorArray = extension.attenuationColor || [ 1, 1, 1 ];\n\t\tmaterialParams.attenuationColor = new Color().setRGB( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ], LinearSRGBColorSpace );\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IOR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n\n\t\tif ( extension.specularTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularIntensityMap', extension.specularTexture ) );\n\n\t\t}\n\n\t\tconst colorArray = extension.specularColorFactor || [ 1, 1, 1 ];\n\t\tmaterialParams.specularColor = new Color().setRGB( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ], LinearSRGBColorSpace );\n\n\t\tif ( extension.specularColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n */\nclass GLTFMaterialsBumpExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_MATERIALS_BUMP;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0;\n\n\t\tif ( extension.bumpTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'bumpMap', extension.bumpTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.anisotropyStrength !== undefined ) {\n\n\t\t\tmaterialParams.anisotropy = extension.anisotropyStrength;\n\n\t\t}\n\n\t\tif ( extension.anisotropyRotation !== undefined ) {\n\n\t\t\tmaterialParams.anisotropyRotation = extension.anisotropyRotation;\n\n\t\t}\n\n\t\tif ( extension.anisotropyTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'anisotropyMap', extension.anisotropyTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ this.name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ this.name ];\n\t\tconst loader = parser.options.ktx2Loader;\n\n\t\tif ( ! loader ) {\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures' );\n\n\t\t\t} else {\n\n\t\t\t\t// Assumes that the extension is optional and that a fallback texture is present\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t}\n\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n\t\tthis.isSupported = null;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.detectSupport().then( function ( isSupported ) {\n\n\t\t\tif ( isSupported ) return parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: WebP required by asset but unsupported.' );\n\n\t\t\t}\n\n\t\t\t// Fall back to PNG or JPEG.\n\t\t\treturn parser.loadTexture( textureIndex );\n\n\t\t} );\n\n\t}\n\n\tdetectSupport() {\n\n\t\tif ( ! this.isSupported ) {\n\n\t\t\tthis.isSupported = new Promise( function ( resolve ) {\n\n\t\t\t\tconst image = new Image();\n\n\t\t\t\t// Lossy test image. Support for lossy images doesn't guarantee support for all\n\t\t\t\t// WebP images, unfortunately.\n\t\t\t\timage.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';\n\n\t\t\t\timage.onload = image.onerror = function () {\n\n\t\t\t\t\tresolve( image.height === 1 );\n\n\t\t\t\t};\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn this.isSupported;\n\n\t}\n\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n\t\tthis.isSupported = null;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.detectSupport().then( function ( isSupported ) {\n\n\t\t\tif ( isSupported ) return parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: AVIF required by asset but unsupported.' );\n\n\t\t\t}\n\n\t\t\t// Fall back to PNG or JPEG.\n\t\t\treturn parser.loadTexture( textureIndex );\n\n\t\t} );\n\n\t}\n\n\tdetectSupport() {\n\n\t\tif ( ! this.isSupported ) {\n\n\t\t\tthis.isSupported = new Promise( function ( resolve ) {\n\n\t\t\t\tconst image = new Image();\n\n\t\t\t\t// Lossy test image.\n\t\t\t\timage.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=';\n\t\t\t\timage.onload = image.onerror = function () {\n\n\t\t\t\t\tresolve( image.height === 1 );\n\n\t\t\t\t};\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn this.isSupported;\n\n\t}\n\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n\t\tthis.parser = parser;\n\n\t}\n\n\tloadBufferView( index ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst bufferView = json.bufferViews[ index ];\n\n\t\tif ( bufferView.extensions && bufferView.extensions[ this.name ] ) {\n\n\t\t\tconst extensionDef = bufferView.extensions[ this.name ];\n\n\t\t\tconst buffer = this.parser.getDependency( 'buffer', extensionDef.buffer );\n\t\t\tconst decoder = this.parser.options.meshoptDecoder;\n\n\t\t\tif ( ! decoder || ! decoder.supported ) {\n\n\t\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files' );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Assumes that the extension is optional and that fallback buffer data is present\n\t\t\t\t\treturn null;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn buffer.then( function ( res ) {\n\n\t\t\t\tconst byteOffset = extensionDef.byteOffset || 0;\n\t\t\t\tconst byteLength = extensionDef.byteLength || 0;\n\n\t\t\t\tconst count = extensionDef.count;\n\t\t\t\tconst stride = extensionDef.byteStride;\n\n\t\t\t\tconst source = new Uint8Array( res, byteOffset, byteLength );\n\n\t\t\t\tif ( decoder.decodeGltfBufferAsync ) {\n\n\t\t\t\t\treturn decoder.decodeGltfBufferAsync( count, stride, source, extensionDef.mode, extensionDef.filter ).then( function ( res ) {\n\n\t\t\t\t\t\treturn res.buffer;\n\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n\t\t\t\t\treturn decoder.ready.then( function () {\n\n\t\t\t\t\t\tconst result = new ArrayBuffer( count * stride );\n\t\t\t\t\t\tdecoder.decodeGltfBuffer( new Uint8Array( result ), count, stride, source, extensionDef.mode, extensionDef.filter );\n\t\t\t\t\t\treturn result;\n\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n\t\tthis.parser = parser;\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( ! nodeDef.extensions || ! nodeDef.extensions[ this.name ] ||\n\t\t\tnodeDef.mesh === undefined ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst meshDef = json.meshes[ nodeDef.mesh ];\n\n\t\t// No Points or Lines + Instancing support yet\n\n\t\tfor ( const primitive of meshDef.primitives ) {\n\n\t\t\tif ( primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n\t\t\t\t primitive.mode !== undefined ) {\n\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst extensionDef = nodeDef.extensions[ this.name ];\n\t\tconst attributesDef = extensionDef.attributes;\n\n\t\t// @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n\t\tconst pending = [];\n\t\tconst attributes = {};\n\n\t\tfor ( const key in attributesDef ) {\n\n\t\t\tpending.push( this.parser.getDependency( 'accessor', attributesDef[ key ] ).then( accessor => {\n\n\t\t\t\tattributes[ key ] = accessor;\n\t\t\t\treturn attributes[ key ];\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tif ( pending.length < 1 ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tpending.push( this.parser.createNodeMesh( nodeIndex ) );\n\n\t\treturn Promise.all( pending ).then( results => {\n\n\t\t\tconst nodeObject = results.pop();\n\t\t\tconst meshes = nodeObject.isGroup ? nodeObject.children : [ nodeObject ];\n\t\t\tconst count = results[ 0 ].count; // All attribute counts should be same\n\t\t\tconst instancedMeshes = [];\n\n\t\t\tfor ( const mesh of meshes ) {\n\n\t\t\t\t// Temporal variables\n\t\t\t\tconst m = new Matrix4();\n\t\t\t\tconst p = new Vector3();\n\t\t\t\tconst q = new Quaternion();\n\t\t\t\tconst s = new Vector3( 1, 1, 1 );\n\n\t\t\t\tconst instancedMesh = new InstancedMesh( mesh.geometry, mesh.material, count );\n\n\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\tif ( attributes.TRANSLATION ) {\n\n\t\t\t\t\t\tp.fromBufferAttribute( attributes.TRANSLATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.ROTATION ) {\n\n\t\t\t\t\t\tq.fromBufferAttribute( attributes.ROTATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.SCALE ) {\n\n\t\t\t\t\t\ts.fromBufferAttribute( attributes.SCALE, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tinstancedMesh.setMatrixAt( i, m.compose( p, q, s ) );\n\n\t\t\t\t}\n\n\t\t\t\t// Add instance attributes to the geometry, excluding TRS.\n\t\t\t\tfor ( const attributeName in attributes ) {\n\n\t\t\t\t\tif ( attributeName === '_COLOR_0' ) {\n\n\t\t\t\t\t\tconst attr = attributes[ attributeName ];\n\t\t\t\t\t\tinstancedMesh.instanceColor = new InstancedBufferAttribute( attr.array, attr.itemSize, attr.normalized );\n\n\t\t\t\t\t} else if ( attributeName !== 'TRANSLATION' &&\n\t\t\t\t\t\t attributeName !== 'ROTATION' &&\n\t\t\t\t\t\t attributeName !== 'SCALE' ) {\n\n\t\t\t\t\t\tmesh.geometry.setAttribute( attributeName, attributes[ attributeName ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Just in case\n\t\t\t\tObject3D.prototype.copy.call( instancedMesh, mesh );\n\n\t\t\t\tthis.parser.assignFinalMaterial( instancedMesh );\n\n\t\t\t\tinstancedMeshes.push( instancedMesh );\n\n\t\t\t}\n\n\t\t\tif ( nodeObject.isGroup ) {\n\n\t\t\t\tnodeObject.clear();\n\n\t\t\t\tnodeObject.add( ... instancedMeshes );\n\n\t\t\t\treturn nodeObject;\n\n\t\t\t}\n\n\t\t\treturn instancedMeshes[ 0 ];\n\n\t\t} );\n\n\t}\n\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4E4F534A, BIN: 0x004E4942 };\n\nclass GLTFBinaryExtension {\n\n\tconstructor( data ) {\n\n\t\tthis.name = EXTENSIONS.KHR_BINARY_GLTF;\n\t\tthis.content = null;\n\t\tthis.body = null;\n\n\t\tconst headerView = new DataView( data, 0, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tthis.header = {\n\t\t\tmagic: textDecoder.decode( new Uint8Array( data.slice( 0, 4 ) ) ),\n\t\t\tversion: headerView.getUint32( 4, true ),\n\t\t\tlength: headerView.getUint32( 8, true )\n\t\t};\n\n\t\tif ( this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported glTF-Binary header.' );\n\n\t\t} else if ( this.header.version < 2.0 ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Legacy binary file detected.' );\n\n\t\t}\n\n\t\tconst chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n\t\tconst chunkView = new DataView( data, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tlet chunkIndex = 0;\n\n\t\twhile ( chunkIndex < chunkContentsLength ) {\n\n\t\t\tconst chunkLength = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tconst chunkType = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tif ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON ) {\n\n\t\t\t\tconst contentArray = new Uint8Array( data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength );\n\t\t\t\tthis.content = textDecoder.decode( contentArray );\n\n\t\t\t} else if ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN ) {\n\n\t\t\t\tconst byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n\t\t\t\tthis.body = data.slice( byteOffset, byteOffset + chunkLength );\n\n\t\t\t}\n\n\t\t\t// Clients must ignore chunks with unknown types.\n\n\t\t\tchunkIndex += chunkLength;\n\n\t\t}\n\n\t\tif ( this.content === null ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: JSON content not found.' );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n\n\tconstructor( json, dracoLoader ) {\n\n\t\tif ( ! dracoLoader ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: No DRACOLoader instance provided.' );\n\n\t\t}\n\n\t\tthis.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n\t\tthis.json = json;\n\t\tthis.dracoLoader = dracoLoader;\n\t\tthis.dracoLoader.preload();\n\n\t}\n\n\tdecodePrimitive( primitive, parser ) {\n\n\t\tconst json = this.json;\n\t\tconst dracoLoader = this.dracoLoader;\n\t\tconst bufferViewIndex = primitive.extensions[ this.name ].bufferView;\n\t\tconst gltfAttributeMap = primitive.extensions[ this.name ].attributes;\n\t\tconst threeAttributeMap = {};\n\t\tconst attributeNormalizedMap = {};\n\t\tconst attributeTypeMap = {};\n\n\t\tfor ( const attributeName in gltfAttributeMap ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tthreeAttributeMap[ threeAttributeName ] = gltfAttributeMap[ attributeName ];\n\n\t\t}\n\n\t\tfor ( const attributeName in primitive.attributes ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tif ( gltfAttributeMap[ attributeName ] !== undefined ) {\n\n\t\t\t\tconst accessorDef = json.accessors[ primitive.attributes[ attributeName ] ];\n\t\t\t\tconst componentType = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t\tattributeTypeMap[ threeAttributeName ] = componentType.name;\n\t\t\t\tattributeNormalizedMap[ threeAttributeName ] = accessorDef.normalized === true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.getDependency( 'bufferView', bufferViewIndex ).then( function ( bufferView ) {\n\n\t\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\t\tdracoLoader.decodeDracoFile( bufferView, function ( geometry ) {\n\n\t\t\t\t\tfor ( const attributeName in geometry.attributes ) {\n\n\t\t\t\t\t\tconst attribute = geometry.attributes[ attributeName ];\n\t\t\t\t\t\tconst normalized = attributeNormalizedMap[ attributeName ];\n\n\t\t\t\t\t\tif ( normalized !== undefined ) attribute.normalized = normalized;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tresolve( geometry );\n\n\t\t\t\t}, threeAttributeMap, attributeTypeMap, LinearSRGBColorSpace, reject );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n\n\t}\n\n\textendTexture( texture, transform ) {\n\n\t\tif ( ( transform.texCoord === undefined || transform.texCoord === texture.channel )\n\t\t\t&& transform.offset === undefined\n\t\t\t&& transform.rotation === undefined\n\t\t\t&& transform.scale === undefined ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21819.\n\t\t\treturn texture;\n\n\t\t}\n\n\t\ttexture = texture.clone();\n\n\t\tif ( transform.texCoord !== undefined ) {\n\n\t\t\ttexture.channel = transform.texCoord;\n\n\t\t}\n\n\t\tif ( transform.offset !== undefined ) {\n\n\t\t\ttexture.offset.fromArray( transform.offset );\n\n\t\t}\n\n\t\tif ( transform.rotation !== undefined ) {\n\n\t\t\ttexture.rotation = transform.rotation;\n\n\t\t}\n\n\t\tif ( transform.scale !== undefined ) {\n\n\t\t\ttexture.repeat.fromArray( transform.scale );\n\n\t\t}\n\n\t\ttexture.needsUpdate = true;\n\n\t\treturn texture;\n\n\t}\n\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n\n\t}\n\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n\n\tconstructor( parameterPositions, sampleValues, sampleSize, resultBuffer ) {\n\n\t\tsuper( parameterPositions, sampleValues, sampleSize, resultBuffer );\n\n\t}\n\n\tcopySampleValue_( index ) {\n\n\t\t// Copies a sample value to the result buffer. See description of glTF\n\t\t// CUBICSPLINE values layout in interpolate_() function below.\n\n\t\tconst result = this.resultBuffer,\n\t\t\tvalues = this.sampleValues,\n\t\t\tvalueSize = this.valueSize,\n\t\t\toffset = index * valueSize * 3 + valueSize;\n\n\t\tfor ( let i = 0; i !== valueSize; i ++ ) {\n\n\t\t\tresult[ i ] = values[ offset + i ];\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = this.resultBuffer;\n\t\tconst values = this.sampleValues;\n\t\tconst stride = this.valueSize;\n\n\t\tconst stride2 = stride * 2;\n\t\tconst stride3 = stride * 3;\n\n\t\tconst td = t1 - t0;\n\n\t\tconst p = ( t - t0 ) / td;\n\t\tconst pp = p * p;\n\t\tconst ppp = pp * p;\n\n\t\tconst offset1 = i1 * stride3;\n\t\tconst offset0 = offset1 - stride3;\n\n\t\tconst s2 = - 2 * ppp + 3 * pp;\n\t\tconst s3 = ppp - pp;\n\t\tconst s0 = 1 - s2;\n\t\tconst s1 = s3 - pp + p;\n\n\t\t// Layout of keyframe output values for CUBICSPLINE animations:\n\t\t//   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n\t\tfor ( let i = 0; i !== stride; i ++ ) {\n\n\t\t\tconst p0 = values[ offset0 + i + stride ]; // splineVertex_k\n\t\t\tconst m0 = values[ offset0 + i + stride2 ] * td; // outTangent_k * (t_k+1 - t_k)\n\t\t\tconst p1 = values[ offset1 + i + stride ]; // splineVertex_k+1\n\t\t\tconst m1 = values[ offset1 + i ] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n\t\t\tresult[ i ] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n}\n\nconst _q = new Quaternion();\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = super.interpolate_( i1, t0, t, t1 );\n\n\t\t_q.fromArray( result ).normalize().toArray( result );\n\n\t\treturn result;\n\n\t}\n\n}\n\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n\tFLOAT: 5126,\n\t//FLOAT_MAT2: 35674,\n\tFLOAT_MAT3: 35675,\n\tFLOAT_MAT4: 35676,\n\tFLOAT_VEC2: 35664,\n\tFLOAT_VEC3: 35665,\n\tFLOAT_VEC4: 35666,\n\tLINEAR: 9729,\n\tREPEAT: 10497,\n\tSAMPLER_2D: 35678,\n\tPOINTS: 0,\n\tLINES: 1,\n\tLINE_LOOP: 2,\n\tLINE_STRIP: 3,\n\tTRIANGLES: 4,\n\tTRIANGLE_STRIP: 5,\n\tTRIANGLE_FAN: 6,\n\tUNSIGNED_BYTE: 5121,\n\tUNSIGNED_SHORT: 5123\n};\n\nconst WEBGL_COMPONENT_TYPES = {\n\t5120: Int8Array,\n\t5121: Uint8Array,\n\t5122: Int16Array,\n\t5123: Uint16Array,\n\t5125: Uint32Array,\n\t5126: Float32Array\n};\n\nconst WEBGL_FILTERS = {\n\t9728: NearestFilter,\n\t9729: LinearFilter,\n\t9984: NearestMipmapNearestFilter,\n\t9985: LinearMipmapNearestFilter,\n\t9986: NearestMipmapLinearFilter,\n\t9987: LinearMipmapLinearFilter\n};\n\nconst WEBGL_WRAPPINGS = {\n\t33071: ClampToEdgeWrapping,\n\t33648: MirroredRepeatWrapping,\n\t10497: RepeatWrapping\n};\n\nconst WEBGL_TYPE_SIZES = {\n\t'SCALAR': 1,\n\t'VEC2': 2,\n\t'VEC3': 3,\n\t'VEC4': 4,\n\t'MAT2': 4,\n\t'MAT3': 9,\n\t'MAT4': 16\n};\n\nconst ATTRIBUTES = {\n\tPOSITION: 'position',\n\tNORMAL: 'normal',\n\tTANGENT: 'tangent',\n\tTEXCOORD_0: 'uv',\n\tTEXCOORD_1: 'uv1',\n\tTEXCOORD_2: 'uv2',\n\tTEXCOORD_3: 'uv3',\n\tCOLOR_0: 'color',\n\tWEIGHTS_0: 'skinWeight',\n\tJOINTS_0: 'skinIndex',\n};\n\nconst PATH_PROPERTIES = {\n\tscale: 'scale',\n\ttranslation: 'position',\n\trotation: 'quaternion',\n\tweights: 'morphTargetInfluences'\n};\n\nconst INTERPOLATION = {\n\tCUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n\t\t                        // keyframe track will be initialized with a default interpolation type, then modified.\n\tLINEAR: InterpolateLinear,\n\tSTEP: InterpolateDiscrete\n};\n\nconst ALPHA_MODES = {\n\tOPAQUE: 'OPAQUE',\n\tMASK: 'MASK',\n\tBLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial( cache ) {\n\n\tif ( cache[ 'DefaultMaterial' ] === undefined ) {\n\n\t\tcache[ 'DefaultMaterial' ] = new MeshStandardMaterial( {\n\t\t\tcolor: 0xFFFFFF,\n\t\t\temissive: 0x000000,\n\t\t\tmetalness: 1,\n\t\t\troughness: 1,\n\t\t\ttransparent: false,\n\t\t\tdepthTest: true,\n\t\t\tside: FrontSide\n\t\t} );\n\n\t}\n\n\treturn cache[ 'DefaultMaterial' ];\n\n}\n\nfunction addUnknownExtensionsToUserData( knownExtensions, object, objectDef ) {\n\n\t// Add unknown glTF extensions to an object's userData.\n\n\tfor ( const name in objectDef.extensions ) {\n\n\t\tif ( knownExtensions[ name ] === undefined ) {\n\n\t\t\tobject.userData.gltfExtensions = object.userData.gltfExtensions || {};\n\t\t\tobject.userData.gltfExtensions[ name ] = objectDef.extensions[ name ];\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData( object, gltfDef ) {\n\n\tif ( gltfDef.extras !== undefined ) {\n\n\t\tif ( typeof gltfDef.extras === 'object' ) {\n\n\t\t\tObject.assign( object.userData, gltfDef.extras );\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets( geometry, targets, parser ) {\n\n\tlet hasMorphPosition = false;\n\tlet hasMorphNormal = false;\n\tlet hasMorphColor = false;\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( target.POSITION !== undefined ) hasMorphPosition = true;\n\t\tif ( target.NORMAL !== undefined ) hasMorphNormal = true;\n\t\tif ( target.COLOR_0 !== undefined ) hasMorphColor = true;\n\n\t\tif ( hasMorphPosition && hasMorphNormal && hasMorphColor ) break;\n\n\t}\n\n\tif ( ! hasMorphPosition && ! hasMorphNormal && ! hasMorphColor ) return Promise.resolve( geometry );\n\n\tconst pendingPositionAccessors = [];\n\tconst pendingNormalAccessors = [];\n\tconst pendingColorAccessors = [];\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( hasMorphPosition ) {\n\n\t\t\tconst pendingAccessor = target.POSITION !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.POSITION )\n\t\t\t\t: geometry.attributes.position;\n\n\t\t\tpendingPositionAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphNormal ) {\n\n\t\t\tconst pendingAccessor = target.NORMAL !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.NORMAL )\n\t\t\t\t: geometry.attributes.normal;\n\n\t\t\tpendingNormalAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphColor ) {\n\n\t\t\tconst pendingAccessor = target.COLOR_0 !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.COLOR_0 )\n\t\t\t\t: geometry.attributes.color;\n\n\t\t\tpendingColorAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t}\n\n\treturn Promise.all( [\n\t\tPromise.all( pendingPositionAccessors ),\n\t\tPromise.all( pendingNormalAccessors ),\n\t\tPromise.all( pendingColorAccessors )\n\t] ).then( function ( accessors ) {\n\n\t\tconst morphPositions = accessors[ 0 ];\n\t\tconst morphNormals = accessors[ 1 ];\n\t\tconst morphColors = accessors[ 2 ];\n\n\t\tif ( hasMorphPosition ) geometry.morphAttributes.position = morphPositions;\n\t\tif ( hasMorphNormal ) geometry.morphAttributes.normal = morphNormals;\n\t\tif ( hasMorphColor ) geometry.morphAttributes.color = morphColors;\n\t\tgeometry.morphTargetsRelative = true;\n\n\t\treturn geometry;\n\n\t} );\n\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets( mesh, meshDef ) {\n\n\tmesh.updateMorphTargets();\n\n\tif ( meshDef.weights !== undefined ) {\n\n\t\tfor ( let i = 0, il = meshDef.weights.length; i < il; i ++ ) {\n\n\t\t\tmesh.morphTargetInfluences[ i ] = meshDef.weights[ i ];\n\n\t\t}\n\n\t}\n\n\t// .extras has user-defined data, so check that .extras.targetNames is an array.\n\tif ( meshDef.extras && Array.isArray( meshDef.extras.targetNames ) ) {\n\n\t\tconst targetNames = meshDef.extras.targetNames;\n\n\t\tif ( mesh.morphTargetInfluences.length === targetNames.length ) {\n\n\t\t\tmesh.morphTargetDictionary = {};\n\n\t\t\tfor ( let i = 0, il = targetNames.length; i < il; i ++ ) {\n\n\t\t\t\tmesh.morphTargetDictionary[ targetNames[ i ] ] = i;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.' );\n\n\t\t}\n\n\t}\n\n}\n\nfunction createPrimitiveKey( primitiveDef ) {\n\n\tlet geometryKey;\n\n\tconst dracoExtension = primitiveDef.extensions && primitiveDef.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ];\n\n\tif ( dracoExtension ) {\n\n\t\tgeometryKey = 'draco:' + dracoExtension.bufferView\n\t\t\t\t+ ':' + dracoExtension.indices\n\t\t\t\t+ ':' + createAttributesKey( dracoExtension.attributes );\n\n\t} else {\n\n\t\tgeometryKey = primitiveDef.indices + ':' + createAttributesKey( primitiveDef.attributes ) + ':' + primitiveDef.mode;\n\n\t}\n\n\tif ( primitiveDef.targets !== undefined ) {\n\n\t\tfor ( let i = 0, il = primitiveDef.targets.length; i < il; i ++ ) {\n\n\t\t\tgeometryKey += ':' + createAttributesKey( primitiveDef.targets[ i ] );\n\n\t\t}\n\n\t}\n\n\treturn geometryKey;\n\n}\n\nfunction createAttributesKey( attributes ) {\n\n\tlet attributesKey = '';\n\n\tconst keys = Object.keys( attributes ).sort();\n\n\tfor ( let i = 0, il = keys.length; i < il; i ++ ) {\n\n\t\tattributesKey += keys[ i ] + ':' + attributes[ keys[ i ] ] + ';';\n\n\t}\n\n\treturn attributesKey;\n\n}\n\nfunction getNormalizedComponentScale( constructor ) {\n\n\t// Reference:\n\t// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n\tswitch ( constructor ) {\n\n\t\tcase Int8Array:\n\t\t\treturn 1 / 127;\n\n\t\tcase Uint8Array:\n\t\t\treturn 1 / 255;\n\n\t\tcase Int16Array:\n\t\t\treturn 1 / 32767;\n\n\t\tcase Uint16Array:\n\t\t\treturn 1 / 65535;\n\n\t\tdefault:\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported normalized accessor component type.' );\n\n\t}\n\n}\n\nfunction getImageURIMimeType( uri ) {\n\n\tif ( uri.search( /\\.jpe?g($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/jpeg/ ) === 0 ) return 'image/jpeg';\n\tif ( uri.search( /\\.webp($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/webp/ ) === 0 ) return 'image/webp';\n\n\treturn 'image/png';\n\n}\n\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n\n\tconstructor( json = {}, options = {} ) {\n\n\t\tthis.json = json;\n\t\tthis.extensions = {};\n\t\tthis.plugins = {};\n\t\tthis.options = options;\n\n\t\t// loader object cache\n\t\tthis.cache = new GLTFRegistry();\n\n\t\t// associations between Three.js objects and glTF elements\n\t\tthis.associations = new Map();\n\n\t\t// BufferGeometry caching\n\t\tthis.primitiveCache = {};\n\n\t\t// Node cache\n\t\tthis.nodeCache = {};\n\n\t\t// Object3D instance caches\n\t\tthis.meshCache = { refs: {}, uses: {} };\n\t\tthis.cameraCache = { refs: {}, uses: {} };\n\t\tthis.lightCache = { refs: {}, uses: {} };\n\n\t\tthis.sourceCache = {};\n\t\tthis.textureCache = {};\n\n\t\t// Track node names, to ensure no duplicates\n\t\tthis.nodeNamesUsed = {};\n\n\t\t// Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n\t\t// expensive work of uploading a texture to the GPU off the main thread.\n\n\t\tlet isSafari = false;\n\t\tlet isFirefox = false;\n\t\tlet firefoxVersion = - 1;\n\n\t\tif ( typeof navigator !== 'undefined' ) {\n\n\t\t\tisSafari = /^((?!chrome|android).)*safari/i.test( navigator.userAgent ) === true;\n\t\t\tisFirefox = navigator.userAgent.indexOf( 'Firefox' ) > - 1;\n\t\t\tfirefoxVersion = isFirefox ? navigator.userAgent.match( /Firefox\\/([0-9]+)\\./ )[ 1 ] : - 1;\n\n\t\t}\n\n\t\tif ( typeof createImageBitmap === 'undefined' || isSafari || ( isFirefox && firefoxVersion < 98 ) ) {\n\n\t\t\tthis.textureLoader = new TextureLoader( this.options.manager );\n\n\t\t} else {\n\n\t\t\tthis.textureLoader = new ImageBitmapLoader( this.options.manager );\n\n\t\t}\n\n\t\tthis.textureLoader.setCrossOrigin( this.options.crossOrigin );\n\t\tthis.textureLoader.setRequestHeader( this.options.requestHeader );\n\n\t\tthis.fileLoader = new FileLoader( this.options.manager );\n\t\tthis.fileLoader.setResponseType( 'arraybuffer' );\n\n\t\tif ( this.options.crossOrigin === 'use-credentials' ) {\n\n\t\t\tthis.fileLoader.setWithCredentials( true );\n\n\t\t}\n\n\t}\n\n\tsetExtensions( extensions ) {\n\n\t\tthis.extensions = extensions;\n\n\t}\n\n\tsetPlugins( plugins ) {\n\n\t\tthis.plugins = plugins;\n\n\t}\n\n\tparse( onLoad, onError ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\t// Clear the loader cache\n\t\tthis.cache.removeAll();\n\t\tthis.nodeCache = {};\n\n\t\t// Mark the special nodes/meshes in json for efficient parse\n\t\tthis._invokeAll( function ( ext ) {\n\n\t\t\treturn ext._markDefs && ext._markDefs();\n\n\t\t} );\n\n\t\tPromise.all( this._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.beforeRoot && ext.beforeRoot();\n\n\t\t} ) ).then( function () {\n\n\t\t\treturn Promise.all( [\n\n\t\t\t\tparser.getDependencies( 'scene' ),\n\t\t\t\tparser.getDependencies( 'animation' ),\n\t\t\t\tparser.getDependencies( 'camera' ),\n\n\t\t\t] );\n\n\t\t} ).then( function ( dependencies ) {\n\n\t\t\tconst result = {\n\t\t\t\tscene: dependencies[ 0 ][ json.scene || 0 ],\n\t\t\t\tscenes: dependencies[ 0 ],\n\t\t\t\tanimations: dependencies[ 1 ],\n\t\t\t\tcameras: dependencies[ 2 ],\n\t\t\t\tasset: json.asset,\n\t\t\t\tparser: parser,\n\t\t\t\tuserData: {}\n\t\t\t};\n\n\t\t\taddUnknownExtensionsToUserData( extensions, result, json );\n\n\t\t\tassignExtrasToUserData( result, json );\n\n\t\t\treturn Promise.all( parser._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.afterRoot && ext.afterRoot( result );\n\n\t\t\t} ) ).then( function () {\n\n\t\t\t\tonLoad( result );\n\n\t\t\t} );\n\n\t\t} ).catch( onError );\n\n\t}\n\n\t/**\n\t * Marks the special nodes/meshes in json for efficient parse.\n\t */\n\t_markDefs() {\n\n\t\tconst nodeDefs = this.json.nodes || [];\n\t\tconst skinDefs = this.json.skins || [];\n\t\tconst meshDefs = this.json.meshes || [];\n\n\t\t// Nothing in the node definition indicates whether it is a Bone or an\n\t\t// Object3D. Use the skins' joint references to mark bones.\n\t\tfor ( let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex ++ ) {\n\n\t\t\tconst joints = skinDefs[ skinIndex ].joints;\n\n\t\t\tfor ( let i = 0, il = joints.length; i < il; i ++ ) {\n\n\t\t\t\tnodeDefs[ joints[ i ] ].isBone = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Iterate over all nodes, marking references to shared resources,\n\t\t// as well as skeleton joints.\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.mesh !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.meshCache, nodeDef.mesh );\n\n\t\t\t\t// Nothing in the mesh definition indicates whether it is\n\t\t\t\t// a SkinnedMesh or Mesh. Use the node's mesh reference\n\t\t\t\t// to mark SkinnedMesh if node has skin.\n\t\t\t\tif ( nodeDef.skin !== undefined ) {\n\n\t\t\t\t\tmeshDefs[ nodeDef.mesh ].isSkinnedMesh = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.cameraCache, nodeDef.camera );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Counts references to shared node / Object3D resources. These resources\n\t * can be reused, or \"instantiated\", at multiple nodes in the scene\n\t * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n\t * be marked. Non-scenegraph resources (like Materials, Geometries, and\n\t * Textures) can be reused directly and are not marked here.\n\t *\n\t * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n\t */\n\t_addNodeRef( cache, index ) {\n\n\t\tif ( index === undefined ) return;\n\n\t\tif ( cache.refs[ index ] === undefined ) {\n\n\t\t\tcache.refs[ index ] = cache.uses[ index ] = 0;\n\n\t\t}\n\n\t\tcache.refs[ index ] ++;\n\n\t}\n\n\t/** Returns a reference to a shared resource, cloning it if necessary. */\n\t_getNodeRef( cache, index, object ) {\n\n\t\tif ( cache.refs[ index ] <= 1 ) return object;\n\n\t\tconst ref = object.clone();\n\n\t\t// Propagates mappings to the cloned object, prevents mappings on the\n\t\t// original object from being lost.\n\t\tconst updateMappings = ( original, clone ) => {\n\n\t\t\tconst mappings = this.associations.get( original );\n\t\t\tif ( mappings != null ) {\n\n\t\t\t\tthis.associations.set( clone, mappings );\n\n\t\t\t}\n\n\t\t\tfor ( const [ i, child ] of original.children.entries() ) {\n\n\t\t\t\tupdateMappings( child, clone.children[ i ] );\n\n\t\t\t}\n\n\t\t};\n\n\t\tupdateMappings( object, ref );\n\n\t\tref.name += '_instance_' + ( cache.uses[ index ] ++ );\n\n\t\treturn ref;\n\n\t}\n\n\t_invokeOne( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.push( this );\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) return result;\n\n\t\t}\n\n\t\treturn null;\n\n\t}\n\n\t_invokeAll( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.unshift( this );\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) pending.push( result );\n\n\t\t}\n\n\t\treturn pending;\n\n\t}\n\n\t/**\n\t * Requests the specified dependency asynchronously, with caching.\n\t * @param {string} type\n\t * @param {number} index\n\t * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n\t */\n\tgetDependency( type, index ) {\n\n\t\tconst cacheKey = type + ':' + index;\n\t\tlet dependency = this.cache.get( cacheKey );\n\n\t\tif ( ! dependency ) {\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 'scene':\n\t\t\t\t\tdependency = this.loadScene( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'node':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadNode && ext.loadNode( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'mesh':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMesh && ext.loadMesh( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'accessor':\n\t\t\t\t\tdependency = this.loadAccessor( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'bufferView':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadBufferView && ext.loadBufferView( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'buffer':\n\t\t\t\t\tdependency = this.loadBuffer( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'material':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMaterial && ext.loadMaterial( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'texture':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadTexture && ext.loadTexture( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'skin':\n\t\t\t\t\tdependency = this.loadSkin( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'animation':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadAnimation && ext.loadAnimation( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'camera':\n\t\t\t\t\tdependency = this.loadCamera( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext != this && ext.getDependency && ext.getDependency( type, index );\n\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( ! dependency ) {\n\n\t\t\t\t\t\tthrow new Error( 'Unknown type: ' + type );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tthis.cache.add( cacheKey, dependency );\n\n\t\t}\n\n\t\treturn dependency;\n\n\t}\n\n\t/**\n\t * Requests all dependencies of the specified type asynchronously, with caching.\n\t * @param {string} type\n\t * @return {Promise<Array<Object>>}\n\t */\n\tgetDependencies( type ) {\n\n\t\tlet dependencies = this.cache.get( type );\n\n\t\tif ( ! dependencies ) {\n\n\t\t\tconst parser = this;\n\t\t\tconst defs = this.json[ type + ( type === 'mesh' ? 'es' : 's' ) ] || [];\n\n\t\t\tdependencies = Promise.all( defs.map( function ( def, index ) {\n\n\t\t\t\treturn parser.getDependency( type, index );\n\n\t\t\t} ) );\n\n\t\t\tthis.cache.add( type, dependencies );\n\n\t\t}\n\n\t\treturn dependencies;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t * @param {number} bufferIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBuffer( bufferIndex ) {\n\n\t\tconst bufferDef = this.json.buffers[ bufferIndex ];\n\t\tconst loader = this.fileLoader;\n\n\t\tif ( bufferDef.type && bufferDef.type !== 'arraybuffer' ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.' );\n\n\t\t}\n\n\t\t// If present, GLB container is required to be the first buffer.\n\t\tif ( bufferDef.uri === undefined && bufferIndex === 0 ) {\n\n\t\t\treturn Promise.resolve( this.extensions[ EXTENSIONS.KHR_BINARY_GLTF ].body );\n\n\t\t}\n\n\t\tconst options = this.options;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tloader.load( LoaderUtils.resolveURL( bufferDef.uri, options.path ), resolve, undefined, function () {\n\n\t\t\t\treject( new Error( 'THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".' ) );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t * @param {number} bufferViewIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBufferView( bufferViewIndex ) {\n\n\t\tconst bufferViewDef = this.json.bufferViews[ bufferViewIndex ];\n\n\t\treturn this.getDependency( 'buffer', bufferViewDef.buffer ).then( function ( buffer ) {\n\n\t\t\tconst byteLength = bufferViewDef.byteLength || 0;\n\t\t\tconst byteOffset = bufferViewDef.byteOffset || 0;\n\t\t\treturn buffer.slice( byteOffset, byteOffset + byteLength );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n\t * @param {number} accessorIndex\n\t * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n\t */\n\tloadAccessor( accessorIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst accessorDef = this.json.accessors[ accessorIndex ];\n\n\t\tif ( accessorDef.bufferView === undefined && accessorDef.sparse === undefined ) {\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\t\t\tconst normalized = accessorDef.normalized === true;\n\n\t\t\tconst array = new TypedArray( accessorDef.count * itemSize );\n\t\t\treturn Promise.resolve( new BufferAttribute( array, itemSize, normalized ) );\n\n\t\t}\n\n\t\tconst pendingBufferViews = [];\n\n\t\tif ( accessorDef.bufferView !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.bufferView ) );\n\n\t\t} else {\n\n\t\t\tpendingBufferViews.push( null );\n\n\t\t}\n\n\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.indices.bufferView ) );\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.values.bufferView ) );\n\n\t\t}\n\n\t\treturn Promise.all( pendingBufferViews ).then( function ( bufferViews ) {\n\n\t\t\tconst bufferView = bufferViews[ 0 ];\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t// For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n\t\t\tconst elementBytes = TypedArray.BYTES_PER_ELEMENT;\n\t\t\tconst itemBytes = elementBytes * itemSize;\n\t\t\tconst byteOffset = accessorDef.byteOffset || 0;\n\t\t\tconst byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[ accessorDef.bufferView ].byteStride : undefined;\n\t\t\tconst normalized = accessorDef.normalized === true;\n\t\t\tlet array, bufferAttribute;\n\n\t\t\t// The buffer is not interleaved if the stride is the item size in bytes.\n\t\t\tif ( byteStride && byteStride !== itemBytes ) {\n\n\t\t\t\t// Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n\t\t\t\t// This makes sure that IBA.count reflects accessor.count properly\n\t\t\t\tconst ibSlice = Math.floor( byteOffset / byteStride );\n\t\t\t\tconst ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n\t\t\t\tlet ib = parser.cache.get( ibCacheKey );\n\n\t\t\t\tif ( ! ib ) {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes );\n\n\t\t\t\t\t// Integer parameters to IB/IBA are in array elements, not bytes.\n\t\t\t\t\tib = new InterleavedBuffer( array, byteStride / elementBytes );\n\n\t\t\t\t\tparser.cache.add( ibCacheKey, ib );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new InterleavedBufferAttribute( ib, itemSize, ( byteOffset % byteStride ) / elementBytes, normalized );\n\n\t\t\t} else {\n\n\t\t\t\tif ( bufferView === null ) {\n\n\t\t\t\t\tarray = new TypedArray( accessorDef.count * itemSize );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, byteOffset, accessorDef.count * itemSize );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t\t\t}\n\n\t\t\t// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n\t\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\t\tconst itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n\t\t\t\tconst TypedArrayIndices = WEBGL_COMPONENT_TYPES[ accessorDef.sparse.indices.componentType ];\n\n\t\t\t\tconst byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n\t\t\t\tconst byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n\n\t\t\t\tconst sparseIndices = new TypedArrayIndices( bufferViews[ 1 ], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices );\n\t\t\t\tconst sparseValues = new TypedArray( bufferViews[ 2 ], byteOffsetValues, accessorDef.sparse.count * itemSize );\n\n\t\t\t\tif ( bufferView !== null ) {\n\n\t\t\t\t\t// Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n\t\t\t\t\tbufferAttribute = new BufferAttribute( bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized );\n\n\t\t\t\t}\n\n\t\t\t\tfor ( let i = 0, il = sparseIndices.length; i < il; i ++ ) {\n\n\t\t\t\t\tconst index = sparseIndices[ i ];\n\n\t\t\t\t\tbufferAttribute.setX( index, sparseValues[ i * itemSize ] );\n\t\t\t\t\tif ( itemSize >= 2 ) bufferAttribute.setY( index, sparseValues[ i * itemSize + 1 ] );\n\t\t\t\t\tif ( itemSize >= 3 ) bufferAttribute.setZ( index, sparseValues[ i * itemSize + 2 ] );\n\t\t\t\t\tif ( itemSize >= 4 ) bufferAttribute.setW( index, sparseValues[ i * itemSize + 3 ] );\n\t\t\t\t\tif ( itemSize >= 5 ) throw new Error( 'THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.' );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn bufferAttribute;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n\t * @param {number} textureIndex\n\t * @return {Promise<THREE.Texture|null>}\n\t */\n\tloadTexture( textureIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceIndex = textureDef.source;\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tlet loader = this.textureLoader;\n\n\t\tif ( sourceDef.uri ) {\n\n\t\t\tconst handler = options.manager.getHandler( sourceDef.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.loadTextureImage( textureIndex, sourceIndex, loader );\n\n\t}\n\n\tloadTextureImage( textureIndex, sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst cacheKey = ( sourceDef.uri || sourceDef.bufferView ) + ':' + textureDef.sampler;\n\n\t\tif ( this.textureCache[ cacheKey ] ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21559.\n\t\t\treturn this.textureCache[ cacheKey ];\n\n\t\t}\n\n\t\tconst promise = this.loadImageSource( sourceIndex, loader ).then( function ( texture ) {\n\n\t\t\ttexture.flipY = false;\n\n\t\t\ttexture.name = textureDef.name || sourceDef.name || '';\n\n\t\t\tif ( texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith( 'data:image/' ) === false ) {\n\n\t\t\t\ttexture.name = sourceDef.uri;\n\n\t\t\t}\n\n\t\t\tconst samplers = json.samplers || {};\n\t\t\tconst sampler = samplers[ textureDef.sampler ] || {};\n\n\t\t\ttexture.magFilter = WEBGL_FILTERS[ sampler.magFilter ] || LinearFilter;\n\t\t\ttexture.minFilter = WEBGL_FILTERS[ sampler.minFilter ] || LinearMipmapLinearFilter;\n\t\t\ttexture.wrapS = WEBGL_WRAPPINGS[ sampler.wrapS ] || RepeatWrapping;\n\t\t\ttexture.wrapT = WEBGL_WRAPPINGS[ sampler.wrapT ] || RepeatWrapping;\n\n\t\t\tparser.associations.set( texture, { textures: textureIndex } );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function () {\n\n\t\t\treturn null;\n\n\t\t} );\n\n\t\tthis.textureCache[ cacheKey ] = promise;\n\n\t\treturn promise;\n\n\t}\n\n\tloadImageSource( sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\n\t\tif ( this.sourceCache[ sourceIndex ] !== undefined ) {\n\n\t\t\treturn this.sourceCache[ sourceIndex ].then( ( texture ) => texture.clone() );\n\n\t\t}\n\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst URL = self.URL || self.webkitURL;\n\n\t\tlet sourceURI = sourceDef.uri || '';\n\t\tlet isObjectURL = false;\n\n\t\tif ( sourceDef.bufferView !== undefined ) {\n\n\t\t\t// Load binary image data from bufferView, if provided.\n\n\t\t\tsourceURI = parser.getDependency( 'bufferView', sourceDef.bufferView ).then( function ( bufferView ) {\n\n\t\t\t\tisObjectURL = true;\n\t\t\t\tconst blob = new Blob( [ bufferView ], { type: sourceDef.mimeType } );\n\t\t\t\tsourceURI = URL.createObjectURL( blob );\n\t\t\t\treturn sourceURI;\n\n\t\t\t} );\n\n\t\t} else if ( sourceDef.uri === undefined ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView' );\n\n\t\t}\n\n\t\tconst promise = Promise.resolve( sourceURI ).then( function ( sourceURI ) {\n\n\t\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\t\tlet onLoad = resolve;\n\n\t\t\t\tif ( loader.isImageBitmapLoader === true ) {\n\n\t\t\t\t\tonLoad = function ( imageBitmap ) {\n\n\t\t\t\t\t\tconst texture = new Texture( imageBitmap );\n\t\t\t\t\t\ttexture.needsUpdate = true;\n\n\t\t\t\t\t\tresolve( texture );\n\n\t\t\t\t\t};\n\n\t\t\t\t}\n\n\t\t\t\tloader.load( LoaderUtils.resolveURL( sourceURI, options.path ), onLoad, undefined, reject );\n\n\t\t\t} );\n\n\t\t} ).then( function ( texture ) {\n\n\t\t\t// Clean up resources and configure Texture.\n\n\t\t\tif ( isObjectURL === true ) {\n\n\t\t\t\tURL.revokeObjectURL( sourceURI );\n\n\t\t\t}\n\n\t\t\ttexture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType( sourceDef.uri );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function ( error ) {\n\n\t\t\tconsole.error( 'THREE.GLTFLoader: Couldn\\'t load texture', sourceURI );\n\t\t\tthrow error;\n\n\t\t} );\n\n\t\tthis.sourceCache[ sourceIndex ] = promise;\n\t\treturn promise;\n\n\t}\n\n\t/**\n\t * Asynchronously assigns a texture to the given material parameters.\n\t * @param {Object} materialParams\n\t * @param {string} mapName\n\t * @param {Object} mapDef\n\t * @return {Promise<Texture>}\n\t */\n\tassignTexture( materialParams, mapName, mapDef, colorSpace ) {\n\n\t\tconst parser = this;\n\n\t\treturn this.getDependency( 'texture', mapDef.index ).then( function ( texture ) {\n\n\t\t\tif ( ! texture ) return null;\n\n\t\t\tif ( mapDef.texCoord !== undefined && mapDef.texCoord > 0 ) {\n\n\t\t\t\ttexture = texture.clone();\n\t\t\t\ttexture.channel = mapDef.texCoord;\n\n\t\t\t}\n\n\t\t\tif ( parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] ) {\n\n\t\t\t\tconst transform = mapDef.extensions !== undefined ? mapDef.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] : undefined;\n\n\t\t\t\tif ( transform ) {\n\n\t\t\t\t\tconst gltfReference = parser.associations.get( texture );\n\t\t\t\t\ttexture = parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ].extendTexture( texture, transform );\n\t\t\t\t\tparser.associations.set( texture, gltfReference );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( colorSpace !== undefined ) {\n\n\t\t\t\ttexture.colorSpace = colorSpace;\n\n\t\t\t}\n\n\t\t\tmaterialParams[ mapName ] = texture;\n\n\t\t\treturn texture;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Assigns final material to a Mesh, Line, or Points instance. The instance\n\t * already has a material (generated from the glTF material options alone)\n\t * but reuse of the same glTF material may require multiple threejs materials\n\t * to accommodate different primitive types, defines, etc. New materials will\n\t * be created if necessary, and reused from a cache.\n\t * @param  {Object3D} mesh Mesh, Line, or Points instance.\n\t */\n\tassignFinalMaterial( mesh ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tlet material = mesh.material;\n\n\t\tconst useDerivativeTangents = geometry.attributes.tangent === undefined;\n\t\tconst useVertexColors = geometry.attributes.color !== undefined;\n\t\tconst useFlatShading = geometry.attributes.normal === undefined;\n\n\t\tif ( mesh.isPoints ) {\n\n\t\t\tconst cacheKey = 'PointsMaterial:' + material.uuid;\n\n\t\t\tlet pointsMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! pointsMaterial ) {\n\n\t\t\t\tpointsMaterial = new PointsMaterial();\n\t\t\t\tMaterial.prototype.copy.call( pointsMaterial, material );\n\t\t\t\tpointsMaterial.color.copy( material.color );\n\t\t\t\tpointsMaterial.map = material.map;\n\t\t\t\tpointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n\t\t\t\tthis.cache.add( cacheKey, pointsMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = pointsMaterial;\n\n\t\t} else if ( mesh.isLine ) {\n\n\t\t\tconst cacheKey = 'LineBasicMaterial:' + material.uuid;\n\n\t\t\tlet lineMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! lineMaterial ) {\n\n\t\t\t\tlineMaterial = new LineBasicMaterial();\n\t\t\t\tMaterial.prototype.copy.call( lineMaterial, material );\n\t\t\t\tlineMaterial.color.copy( material.color );\n\t\t\t\tlineMaterial.map = material.map;\n\n\t\t\t\tthis.cache.add( cacheKey, lineMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = lineMaterial;\n\n\t\t}\n\n\t\t// Clone the material if it will be modified\n\t\tif ( useDerivativeTangents || useVertexColors || useFlatShading ) {\n\n\t\t\tlet cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n\n\t\t\tif ( useDerivativeTangents ) cacheKey += 'derivative-tangents:';\n\t\t\tif ( useVertexColors ) cacheKey += 'vertex-colors:';\n\t\t\tif ( useFlatShading ) cacheKey += 'flat-shading:';\n\n\t\t\tlet cachedMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! cachedMaterial ) {\n\n\t\t\t\tcachedMaterial = material.clone();\n\n\t\t\t\tif ( useVertexColors ) cachedMaterial.vertexColors = true;\n\t\t\t\tif ( useFlatShading ) cachedMaterial.flatShading = true;\n\n\t\t\t\tif ( useDerivativeTangents ) {\n\n\t\t\t\t\t// https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n\t\t\t\t\tif ( cachedMaterial.normalScale ) cachedMaterial.normalScale.y *= - 1;\n\t\t\t\t\tif ( cachedMaterial.clearcoatNormalScale ) cachedMaterial.clearcoatNormalScale.y *= - 1;\n\n\t\t\t\t}\n\n\t\t\t\tthis.cache.add( cacheKey, cachedMaterial );\n\n\t\t\t\tthis.associations.set( cachedMaterial, this.associations.get( material ) );\n\n\t\t\t}\n\n\t\t\tmaterial = cachedMaterial;\n\n\t\t}\n\n\t\tmesh.material = material;\n\n\t}\n\n\tgetMaterialType( /* materialIndex */ ) {\n\n\t\treturn MeshStandardMaterial;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n\t * @param {number} materialIndex\n\t * @return {Promise<Material>}\n\t */\n\tloadMaterial( materialIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst materialDef = json.materials[ materialIndex ];\n\n\t\tlet materialType;\n\t\tconst materialParams = {};\n\t\tconst materialExtensions = materialDef.extensions || {};\n\n\t\tconst pending = [];\n\n\t\tif ( materialExtensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ] ) {\n\n\t\t\tconst kmuExtension = extensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ];\n\t\t\tmaterialType = kmuExtension.getMaterialType();\n\t\t\tpending.push( kmuExtension.extendParams( materialParams, materialDef, parser ) );\n\n\t\t} else {\n\n\t\t\t// Specification:\n\t\t\t// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n\t\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness || {};\n\n\t\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\t\tmaterialParams.opacity = 1.0;\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.setRGB( array[ 0 ], array[ 1 ], array[ 2 ], LinearSRGBColorSpace );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t\tmaterialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n\t\t\tmaterialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n\n\t\t\tif ( metallicRoughness.metallicRoughnessTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\n\t\t\t}\n\n\t\t\tmaterialType = this._invokeOne( function ( ext ) {\n\n\t\t\t\treturn ext.getMaterialType && ext.getMaterialType( materialIndex );\n\n\t\t\t} );\n\n\t\t\tpending.push( Promise.all( this._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.extendMaterialParams && ext.extendMaterialParams( materialIndex, materialParams );\n\n\t\t\t} ) ) );\n\n\t\t}\n\n\t\tif ( materialDef.doubleSided === true ) {\n\n\t\t\tmaterialParams.side = DoubleSide;\n\n\t\t}\n\n\t\tconst alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n\n\t\tif ( alphaMode === ALPHA_MODES.BLEND ) {\n\n\t\t\tmaterialParams.transparent = true;\n\n\t\t\t// See: https://github.com/mrdoob/three.js/issues/17706\n\t\t\tmaterialParams.depthWrite = false;\n\n\t\t} else {\n\n\t\t\tmaterialParams.transparent = false;\n\n\t\t\tif ( alphaMode === ALPHA_MODES.MASK ) {\n\n\t\t\t\tmaterialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'normalMap', materialDef.normalTexture ) );\n\n\t\t\tmaterialParams.normalScale = new Vector2( 1, 1 );\n\n\t\t\tif ( materialDef.normalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = materialDef.normalTexture.scale;\n\n\t\t\t\tmaterialParams.normalScale.set( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'aoMap', materialDef.occlusionTexture ) );\n\n\t\t\tif ( materialDef.occlusionTexture.strength !== undefined ) {\n\n\t\t\t\tmaterialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tconst emissiveFactor = materialDef.emissiveFactor;\n\t\t\tmaterialParams.emissive = new Color().setRGB( emissiveFactor[ 0 ], emissiveFactor[ 1 ], emissiveFactor[ 2 ], LinearSRGBColorSpace );\n\n\t\t}\n\n\t\tif ( materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function () {\n\n\t\t\tconst material = new materialType( materialParams );\n\n\t\t\tif ( materialDef.name ) material.name = materialDef.name;\n\n\t\t\tassignExtrasToUserData( material, materialDef );\n\n\t\t\tparser.associations.set( material, { materials: materialIndex } );\n\n\t\t\tif ( materialDef.extensions ) addUnknownExtensionsToUserData( extensions, material, materialDef );\n\n\t\t\treturn material;\n\n\t\t} );\n\n\t}\n\n\t/** When Object3D instances are targeted by animation, they need unique names. */\n\tcreateUniqueName( originalName ) {\n\n\t\tconst sanitizedName = PropertyBinding.sanitizeNodeName( originalName || '' );\n\n\t\tif ( sanitizedName in this.nodeNamesUsed ) {\n\n\t\t\treturn sanitizedName + '_' + ( ++ this.nodeNamesUsed[ sanitizedName ] );\n\n\t\t} else {\n\n\t\t\tthis.nodeNamesUsed[ sanitizedName ] = 0;\n\n\t\t\treturn sanitizedName;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n\t *\n\t * Creates BufferGeometries from primitives.\n\t *\n\t * @param {Array<GLTF.Primitive>} primitives\n\t * @return {Promise<Array<BufferGeometry>>}\n\t */\n\tloadGeometries( primitives ) {\n\n\t\tconst parser = this;\n\t\tconst extensions = this.extensions;\n\t\tconst cache = this.primitiveCache;\n\n\t\tfunction createDracoPrimitive( primitive ) {\n\n\t\t\treturn extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ]\n\t\t\t\t.decodePrimitive( primitive, parser )\n\t\t\t\t.then( function ( geometry ) {\n\n\t\t\t\t\treturn addPrimitiveAttributes( geometry, primitive, parser );\n\n\t\t\t\t} );\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst primitive = primitives[ i ];\n\t\t\tconst cacheKey = createPrimitiveKey( primitive );\n\n\t\t\t// See if we've already created this geometry\n\t\t\tconst cached = cache[ cacheKey ];\n\n\t\t\tif ( cached ) {\n\n\t\t\t\t// Use the cached geometry if it exists\n\t\t\t\tpending.push( cached.promise );\n\n\t\t\t} else {\n\n\t\t\t\tlet geometryPromise;\n\n\t\t\t\tif ( primitive.extensions && primitive.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ] ) {\n\n\t\t\t\t\t// Use DRACO geometry if available\n\t\t\t\t\tgeometryPromise = createDracoPrimitive( primitive );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Otherwise create a new geometry\n\t\t\t\t\tgeometryPromise = addPrimitiveAttributes( new BufferGeometry(), primitive, parser );\n\n\t\t\t\t}\n\n\t\t\t\t// Cache this geometry\n\t\t\t\tcache[ cacheKey ] = { primitive: primitive, promise: geometryPromise };\n\n\t\t\t\tpending.push( geometryPromise );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n\t * @param {number} meshIndex\n\t * @return {Promise<Group|Mesh|SkinnedMesh>}\n\t */\n\tloadMesh( meshIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\tconst meshDef = json.meshes[ meshIndex ];\n\t\tconst primitives = meshDef.primitives;\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst material = primitives[ i ].material === undefined\n\t\t\t\t? createDefaultMaterial( this.cache )\n\t\t\t\t: this.getDependency( 'material', primitives[ i ].material );\n\n\t\t\tpending.push( material );\n\n\t\t}\n\n\t\tpending.push( parser.loadGeometries( primitives ) );\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst materials = results.slice( 0, results.length - 1 );\n\t\t\tconst geometries = results[ results.length - 1 ];\n\n\t\t\tconst meshes = [];\n\n\t\t\tfor ( let i = 0, il = geometries.length; i < il; i ++ ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst primitive = primitives[ i ];\n\n\t\t\t\t// 1. create Mesh\n\n\t\t\t\tlet mesh;\n\n\t\t\t\tconst material = materials[ i ];\n\n\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n\t\t\t\t\t\tprimitive.mode === undefined ) {\n\n\t\t\t\t\t// .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n\t\t\t\t\tmesh = meshDef.isSkinnedMesh === true\n\t\t\t\t\t\t? new SkinnedMesh( geometry, material )\n\t\t\t\t\t\t: new Mesh( geometry, material );\n\n\t\t\t\t\tif ( mesh.isSkinnedMesh === true ) {\n\n\t\t\t\t\t\t// normalize skin weights to fix malformed assets (see #15319)\n\t\t\t\t\t\tmesh.normalizeSkinWeights();\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleStripDrawMode );\n\n\t\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleFanDrawMode );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINES ) {\n\n\t\t\t\t\tmesh = new LineSegments( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_STRIP ) {\n\n\t\t\t\t\tmesh = new Line( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_LOOP ) {\n\n\t\t\t\t\tmesh = new LineLoop( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.POINTS ) {\n\n\t\t\t\t\tmesh = new Points( geometry, material );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode );\n\n\t\t\t\t}\n\n\t\t\t\tif ( Object.keys( mesh.geometry.morphAttributes ).length > 0 ) {\n\n\t\t\t\t\tupdateMorphTargets( mesh, meshDef );\n\n\t\t\t\t}\n\n\t\t\t\tmesh.name = parser.createUniqueName( meshDef.name || ( 'mesh_' + meshIndex ) );\n\n\t\t\t\tassignExtrasToUserData( mesh, meshDef );\n\n\t\t\t\tif ( primitive.extensions ) addUnknownExtensionsToUserData( extensions, mesh, primitive );\n\n\t\t\t\tparser.assignFinalMaterial( mesh );\n\n\t\t\t\tmeshes.push( mesh );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tparser.associations.set( meshes[ i ], {\n\t\t\t\t\tmeshes: meshIndex,\n\t\t\t\t\tprimitives: i\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tif ( meshes.length === 1 ) {\n\n\t\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, meshes[ 0 ], meshDef );\n\n\t\t\t\treturn meshes[ 0 ];\n\n\t\t\t}\n\n\t\t\tconst group = new Group();\n\n\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, group, meshDef );\n\n\t\t\tparser.associations.set( group, { meshes: meshIndex } );\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tgroup.add( meshes[ i ] );\n\n\t\t\t}\n\n\t\t\treturn group;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n\t * @param {number} cameraIndex\n\t * @return {Promise<THREE.Camera>}\n\t */\n\tloadCamera( cameraIndex ) {\n\n\t\tlet camera;\n\t\tconst cameraDef = this.json.cameras[ cameraIndex ];\n\t\tconst params = cameraDef[ cameraDef.type ];\n\n\t\tif ( ! params ) {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing camera parameters.' );\n\t\t\treturn;\n\n\t\t}\n\n\t\tif ( cameraDef.type === 'perspective' ) {\n\n\t\t\tcamera = new PerspectiveCamera( MathUtils.radToDeg( params.yfov ), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6 );\n\n\t\t} else if ( cameraDef.type === 'orthographic' ) {\n\n\t\t\tcamera = new OrthographicCamera( - params.xmag, params.xmag, params.ymag, - params.ymag, params.znear, params.zfar );\n\n\t\t}\n\n\t\tif ( cameraDef.name ) camera.name = this.createUniqueName( cameraDef.name );\n\n\t\tassignExtrasToUserData( camera, cameraDef );\n\n\t\treturn Promise.resolve( camera );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n\t * @param {number} skinIndex\n\t * @return {Promise<Skeleton>}\n\t */\n\tloadSkin( skinIndex ) {\n\n\t\tconst skinDef = this.json.skins[ skinIndex ];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = skinDef.joints.length; i < il; i ++ ) {\n\n\t\t\tpending.push( this._loadNodeShallow( skinDef.joints[ i ] ) );\n\n\t\t}\n\n\t\tif ( skinDef.inverseBindMatrices !== undefined ) {\n\n\t\t\tpending.push( this.getDependency( 'accessor', skinDef.inverseBindMatrices ) );\n\n\t\t} else {\n\n\t\t\tpending.push( null );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst inverseBindMatrices = results.pop();\n\t\t\tconst jointNodes = results;\n\n\t\t\t// Note that bones (joint nodes) may or may not be in the\n\t\t\t// scene graph at this time.\n\n\t\t\tconst bones = [];\n\t\t\tconst boneInverses = [];\n\n\t\t\tfor ( let i = 0, il = jointNodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst jointNode = jointNodes[ i ];\n\n\t\t\t\tif ( jointNode ) {\n\n\t\t\t\t\tbones.push( jointNode );\n\n\t\t\t\t\tconst mat = new Matrix4();\n\n\t\t\t\t\tif ( inverseBindMatrices !== null ) {\n\n\t\t\t\t\t\tmat.fromArray( inverseBindMatrices.array, i * 16 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tboneInverses.push( mat );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new Skeleton( bones, boneInverses );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n\t * @param {number} animationIndex\n\t * @return {Promise<AnimationClip>}\n\t */\n\tloadAnimation( animationIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst animationDef = json.animations[ animationIndex ];\n\t\tconst animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n\n\t\tconst pendingNodes = [];\n\t\tconst pendingInputAccessors = [];\n\t\tconst pendingOutputAccessors = [];\n\t\tconst pendingSamplers = [];\n\t\tconst pendingTargets = [];\n\n\t\tfor ( let i = 0, il = animationDef.channels.length; i < il; i ++ ) {\n\n\t\t\tconst channel = animationDef.channels[ i ];\n\t\t\tconst sampler = animationDef.samplers[ channel.sampler ];\n\t\t\tconst target = channel.target;\n\t\t\tconst name = target.node;\n\t\t\tconst input = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.input ] : sampler.input;\n\t\t\tconst output = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.output ] : sampler.output;\n\n\t\t\tif ( target.node === undefined ) continue;\n\n\t\t\tpendingNodes.push( this.getDependency( 'node', name ) );\n\t\t\tpendingInputAccessors.push( this.getDependency( 'accessor', input ) );\n\t\t\tpendingOutputAccessors.push( this.getDependency( 'accessor', output ) );\n\t\t\tpendingSamplers.push( sampler );\n\t\t\tpendingTargets.push( target );\n\n\t\t}\n\n\t\treturn Promise.all( [\n\n\t\t\tPromise.all( pendingNodes ),\n\t\t\tPromise.all( pendingInputAccessors ),\n\t\t\tPromise.all( pendingOutputAccessors ),\n\t\t\tPromise.all( pendingSamplers ),\n\t\t\tPromise.all( pendingTargets )\n\n\t\t] ).then( function ( dependencies ) {\n\n\t\t\tconst nodes = dependencies[ 0 ];\n\t\t\tconst inputAccessors = dependencies[ 1 ];\n\t\t\tconst outputAccessors = dependencies[ 2 ];\n\t\t\tconst samplers = dependencies[ 3 ];\n\t\t\tconst targets = dependencies[ 4 ];\n\n\t\t\tconst tracks = [];\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst node = nodes[ i ];\n\t\t\t\tconst inputAccessor = inputAccessors[ i ];\n\t\t\t\tconst outputAccessor = outputAccessors[ i ];\n\t\t\t\tconst sampler = samplers[ i ];\n\t\t\t\tconst target = targets[ i ];\n\n\t\t\t\tif ( node === undefined ) continue;\n\n\t\t\t\tif ( node.updateMatrix ) {\n\n\t\t\t\t\tnode.updateMatrix();\n\n\t\t\t\t}\n\n\t\t\t\tconst createdTracks = parser._createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target );\n\n\t\t\t\tif ( createdTracks ) {\n\n\t\t\t\t\tfor ( let k = 0; k < createdTracks.length; k ++ ) {\n\n\t\t\t\t\t\ttracks.push( createdTracks[ k ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new AnimationClip( animationName, undefined, tracks );\n\n\t\t} );\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( nodeDef.mesh === undefined ) return null;\n\n\t\treturn parser.getDependency( 'mesh', nodeDef.mesh ).then( function ( mesh ) {\n\n\t\t\tconst node = parser._getNodeRef( parser.meshCache, nodeDef.mesh, mesh );\n\n\t\t\t// if weights are provided on the node, override weights on the mesh.\n\t\t\tif ( nodeDef.weights !== undefined ) {\n\n\t\t\t\tnode.traverse( function ( o ) {\n\n\t\t\t\t\tif ( ! o.isMesh ) return;\n\n\t\t\t\t\tfor ( let i = 0, il = nodeDef.weights.length; i < il; i ++ ) {\n\n\t\t\t\t\t\to.morphTargetInfluences[ i ] = nodeDef.weights[ i ];\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n\t * @param {number} nodeIndex\n\t * @return {Promise<Object3D>}\n\t */\n\tloadNode( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tconst nodePending = parser._loadNodeShallow( nodeIndex );\n\n\t\tconst childPending = [];\n\t\tconst childrenDef = nodeDef.children || [];\n\n\t\tfor ( let i = 0, il = childrenDef.length; i < il; i ++ ) {\n\n\t\t\tchildPending.push( parser.getDependency( 'node', childrenDef[ i ] ) );\n\n\t\t}\n\n\t\tconst skeletonPending = nodeDef.skin === undefined\n\t\t\t? Promise.resolve( null )\n\t\t\t: parser.getDependency( 'skin', nodeDef.skin );\n\n\t\treturn Promise.all( [\n\t\t\tnodePending,\n\t\t\tPromise.all( childPending ),\n\t\t\tskeletonPending\n\t\t] ).then( function ( results ) {\n\n\t\t\tconst node = results[ 0 ];\n\t\t\tconst children = results[ 1 ];\n\t\t\tconst skeleton = results[ 2 ];\n\n\t\t\tif ( skeleton !== null ) {\n\n\t\t\t\t// This full traverse should be fine because\n\t\t\t\t// child glTF nodes have not been added to this node yet.\n\t\t\t\tnode.traverse( function ( mesh ) {\n\n\t\t\t\t\tif ( ! mesh.isSkinnedMesh ) return;\n\n\t\t\t\t\tmesh.bind( skeleton, _identityMatrix );\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = children.length; i < il; i ++ ) {\n\n\t\t\t\tnode.add( children[ i ] );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t// ._loadNodeShallow() parses a single node.\n\t// skin and child nodes are created and added in .loadNode() (no '_' prefix).\n\t_loadNodeShallow( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst parser = this;\n\n\t\t// This method is called from .loadNode() and .loadSkin().\n\t\t// Cache a node to avoid duplication.\n\n\t\tif ( this.nodeCache[ nodeIndex ] !== undefined ) {\n\n\t\t\treturn this.nodeCache[ nodeIndex ];\n\n\t\t}\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\t// reserve node's name before its dependencies, so the root has the intended name.\n\t\tconst nodeName = nodeDef.name ? parser.createUniqueName( nodeDef.name ) : '';\n\n\t\tconst pending = [];\n\n\t\tconst meshPromise = parser._invokeOne( function ( ext ) {\n\n\t\t\treturn ext.createNodeMesh && ext.createNodeMesh( nodeIndex );\n\n\t\t} );\n\n\t\tif ( meshPromise ) {\n\n\t\t\tpending.push( meshPromise );\n\n\t\t}\n\n\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\tpending.push( parser.getDependency( 'camera', nodeDef.camera ).then( function ( camera ) {\n\n\t\t\t\treturn parser._getNodeRef( parser.cameraCache, nodeDef.camera, camera );\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tparser._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.createNodeAttachment && ext.createNodeAttachment( nodeIndex );\n\n\t\t} ).forEach( function ( promise ) {\n\n\t\t\tpending.push( promise );\n\n\t\t} );\n\n\t\tthis.nodeCache[ nodeIndex ] = Promise.all( pending ).then( function ( objects ) {\n\n\t\t\tlet node;\n\n\t\t\t// .isBone isn't in glTF spec. See ._markDefs\n\t\t\tif ( nodeDef.isBone === true ) {\n\n\t\t\t\tnode = new Bone();\n\n\t\t\t} else if ( objects.length > 1 ) {\n\n\t\t\t\tnode = new Group();\n\n\t\t\t} else if ( objects.length === 1 ) {\n\n\t\t\t\tnode = objects[ 0 ];\n\n\t\t\t} else {\n\n\t\t\t\tnode = new Object3D();\n\n\t\t\t}\n\n\t\t\tif ( node !== objects[ 0 ] ) {\n\n\t\t\t\tfor ( let i = 0, il = objects.length; i < il; i ++ ) {\n\n\t\t\t\t\tnode.add( objects[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.name ) {\n\n\t\t\t\tnode.userData.name = nodeDef.name;\n\t\t\t\tnode.name = nodeName;\n\n\t\t\t}\n\n\t\t\tassignExtrasToUserData( node, nodeDef );\n\n\t\t\tif ( nodeDef.extensions ) addUnknownExtensionsToUserData( extensions, node, nodeDef );\n\n\t\t\tif ( nodeDef.matrix !== undefined ) {\n\n\t\t\t\tconst matrix = new Matrix4();\n\t\t\t\tmatrix.fromArray( nodeDef.matrix );\n\t\t\t\tnode.applyMatrix4( matrix );\n\n\t\t\t} else {\n\n\t\t\t\tif ( nodeDef.translation !== undefined ) {\n\n\t\t\t\t\tnode.position.fromArray( nodeDef.translation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.rotation !== undefined ) {\n\n\t\t\t\t\tnode.quaternion.fromArray( nodeDef.rotation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.scale !== undefined ) {\n\n\t\t\t\t\tnode.scale.fromArray( nodeDef.scale );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( ! parser.associations.has( node ) ) {\n\n\t\t\t\tparser.associations.set( node, {} );\n\n\t\t\t}\n\n\t\t\tparser.associations.get( node ).nodes = nodeIndex;\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t\treturn this.nodeCache[ nodeIndex ];\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n\t * @param {number} sceneIndex\n\t * @return {Promise<Group>}\n\t */\n\tloadScene( sceneIndex ) {\n\n\t\tconst extensions = this.extensions;\n\t\tconst sceneDef = this.json.scenes[ sceneIndex ];\n\t\tconst parser = this;\n\n\t\t// Loader returns Group, not Scene.\n\t\t// See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n\t\tconst scene = new Group();\n\t\tif ( sceneDef.name ) scene.name = parser.createUniqueName( sceneDef.name );\n\n\t\tassignExtrasToUserData( scene, sceneDef );\n\n\t\tif ( sceneDef.extensions ) addUnknownExtensionsToUserData( extensions, scene, sceneDef );\n\n\t\tconst nodeIds = sceneDef.nodes || [];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = nodeIds.length; i < il; i ++ ) {\n\n\t\t\tpending.push( parser.getDependency( 'node', nodeIds[ i ] ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( nodes ) {\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tscene.add( nodes[ i ] );\n\n\t\t\t}\n\n\t\t\t// Removes dangling associations, associations that reference a node that\n\t\t\t// didn't make it into the scene.\n\t\t\tconst reduceAssociations = ( node ) => {\n\n\t\t\t\tconst reducedAssociations = new Map();\n\n\t\t\t\tfor ( const [ key, value ] of parser.associations ) {\n\n\t\t\t\t\tif ( key instanceof Material || key instanceof Texture ) {\n\n\t\t\t\t\t\treducedAssociations.set( key, value );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tnode.traverse( ( node ) => {\n\n\t\t\t\t\tconst mappings = parser.associations.get( node );\n\n\t\t\t\t\tif ( mappings != null ) {\n\n\t\t\t\t\t\treducedAssociations.set( node, mappings );\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn reducedAssociations;\n\n\t\t\t};\n\n\t\t\tparser.associations = reduceAssociations( scene );\n\n\t\t\treturn scene;\n\n\t\t} );\n\n\t}\n\n\t_createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target ) {\n\n\t\tconst tracks = [];\n\n\t\tconst targetName = node.name ? node.name : node.uuid;\n\t\tconst targetNames = [];\n\n\t\tif ( PATH_PROPERTIES[ target.path ] === PATH_PROPERTIES.weights ) {\n\n\t\t\tnode.traverse( function ( object ) {\n\n\t\t\t\tif ( object.morphTargetInfluences ) {\n\n\t\t\t\t\ttargetNames.push( object.name ? object.name : object.uuid );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\ttargetNames.push( targetName );\n\n\t\t}\n\n\t\tlet TypedKeyframeTrack;\n\n\t\tswitch ( PATH_PROPERTIES[ target.path ] ) {\n\n\t\t\tcase PATH_PROPERTIES.weights:\n\n\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.rotation:\n\n\t\t\t\tTypedKeyframeTrack = QuaternionKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.position:\n\t\t\tcase PATH_PROPERTIES.scale:\n\n\t\t\t\tTypedKeyframeTrack = VectorKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tswitch ( outputAccessor.itemSize ) {\n\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 2:\n\t\t\t\t\tcase 3:\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tTypedKeyframeTrack = VectorKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tconst interpolation = sampler.interpolation !== undefined ? INTERPOLATION[ sampler.interpolation ] : InterpolateLinear;\n\n\n\t\tconst outputArray = this._getArrayFromAccessor( outputAccessor );\n\n\t\tfor ( let j = 0, jl = targetNames.length; j < jl; j ++ ) {\n\n\t\t\tconst track = new TypedKeyframeTrack(\n\t\t\t\ttargetNames[ j ] + '.' + PATH_PROPERTIES[ target.path ],\n\t\t\t\tinputAccessor.array,\n\t\t\t\toutputArray,\n\t\t\t\tinterpolation\n\t\t\t);\n\n\t\t\t// Override interpolation with custom factory method.\n\t\t\tif ( sampler.interpolation === 'CUBICSPLINE' ) {\n\n\t\t\t\tthis._createCubicSplineTrackInterpolant( track );\n\n\t\t\t}\n\n\t\t\ttracks.push( track );\n\n\t\t}\n\n\t\treturn tracks;\n\n\t}\n\n\t_getArrayFromAccessor( accessor ) {\n\n\t\tlet outputArray = accessor.array;\n\n\t\tif ( accessor.normalized ) {\n\n\t\t\tconst scale = getNormalizedComponentScale( outputArray.constructor );\n\t\t\tconst scaled = new Float32Array( outputArray.length );\n\n\t\t\tfor ( let j = 0, jl = outputArray.length; j < jl; j ++ ) {\n\n\t\t\t\tscaled[ j ] = outputArray[ j ] * scale;\n\n\t\t\t}\n\n\t\t\toutputArray = scaled;\n\n\t\t}\n\n\t\treturn outputArray;\n\n\t}\n\n\t_createCubicSplineTrackInterpolant( track ) {\n\n\t\ttrack.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline( result ) {\n\n\t\t\t// A CUBICSPLINE keyframe in glTF has three output values for each input value,\n\t\t\t// representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n\t\t\t// must be divided by three to get the interpolant's sampleSize argument.\n\n\t\t\tconst interpolantType = ( this instanceof QuaternionKeyframeTrack ) ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n\n\t\t\treturn new interpolantType( this.times, this.values, this.getValueSize() / 3, result );\n\n\t\t};\n\n\t\t// Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n\t\ttrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n\n\t}\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst box = new Box3();\n\n\tif ( attributes.POSITION !== undefined ) {\n\n\t\tconst accessor = parser.json.accessors[ attributes.POSITION ];\n\n\t\tconst min = accessor.min;\n\t\tconst max = accessor.max;\n\n\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\tbox.set(\n\t\t\t\tnew Vector3( min[ 0 ], min[ 1 ], min[ 2 ] ),\n\t\t\t\tnew Vector3( max[ 0 ], max[ 1 ], max[ 2 ] )\n\t\t\t);\n\n\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\tbox.min.multiplyScalar( boxScale );\n\t\t\t\tbox.max.multiplyScalar( boxScale );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\treturn;\n\n\t\t}\n\n\t} else {\n\n\t\treturn;\n\n\t}\n\n\tconst targets = primitiveDef.targets;\n\n\tif ( targets !== undefined ) {\n\n\t\tconst maxDisplacement = new Vector3();\n\t\tconst vector = new Vector3();\n\n\t\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\t\tconst target = targets[ i ];\n\n\t\t\tif ( target.POSITION !== undefined ) {\n\n\t\t\t\tconst accessor = parser.json.accessors[ target.POSITION ];\n\t\t\t\tconst min = accessor.min;\n\t\t\t\tconst max = accessor.max;\n\n\t\t\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\t\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\t\t\t// we need to get max of absolute components because target weight is [-1,1]\n\t\t\t\t\tvector.setX( Math.max( Math.abs( min[ 0 ] ), Math.abs( max[ 0 ] ) ) );\n\t\t\t\t\tvector.setY( Math.max( Math.abs( min[ 1 ] ), Math.abs( max[ 1 ] ) ) );\n\t\t\t\t\tvector.setZ( Math.max( Math.abs( min[ 2 ] ), Math.abs( max[ 2 ] ) ) );\n\n\n\t\t\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\t\t\tvector.multiplyScalar( boxScale );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n\t\t\t\t\t// to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n\t\t\t\t\t// are used to implement key-frame animations and as such only two are active at a time - this results in very large\n\t\t\t\t\t// boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n\t\t\t\t\tmaxDisplacement.max( vector );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n\t\tbox.expandByVector( maxDisplacement );\n\n\t}\n\n\tgeometry.boundingBox = box;\n\n\tconst sphere = new Sphere();\n\n\tbox.getCenter( sphere.center );\n\tsphere.radius = box.min.distanceTo( box.max ) / 2;\n\n\tgeometry.boundingSphere = sphere;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst pending = [];\n\n\tfunction assignAttributeAccessor( accessorIndex, attributeName ) {\n\n\t\treturn parser.getDependency( 'accessor', accessorIndex )\n\t\t\t.then( function ( accessor ) {\n\n\t\t\t\tgeometry.setAttribute( attributeName, accessor );\n\n\t\t\t} );\n\n\t}\n\n\tfor ( const gltfAttributeName in attributes ) {\n\n\t\tconst threeAttributeName = ATTRIBUTES[ gltfAttributeName ] || gltfAttributeName.toLowerCase();\n\n\t\t// Skip attributes already provided by e.g. Draco extension.\n\t\tif ( threeAttributeName in geometry.attributes ) continue;\n\n\t\tpending.push( assignAttributeAccessor( attributes[ gltfAttributeName ], threeAttributeName ) );\n\n\t}\n\n\tif ( primitiveDef.indices !== undefined && ! geometry.index ) {\n\n\t\tconst accessor = parser.getDependency( 'accessor', primitiveDef.indices ).then( function ( accessor ) {\n\n\t\t\tgeometry.setIndex( accessor );\n\n\t\t} );\n\n\t\tpending.push( accessor );\n\n\t}\n\n\tif ( ColorManagement.workingColorSpace !== LinearSRGBColorSpace && 'COLOR_0' in attributes ) {\n\n\t\tconsole.warn( `THREE.GLTFLoader: Converting vertex colors from \"srgb-linear\" to \"${ColorManagement.workingColorSpace}\" not supported.` );\n\n\t}\n\n\tassignExtrasToUserData( geometry, primitiveDef );\n\n\tcomputeBounds( geometry, primitiveDef, parser );\n\n\treturn Promise.all( pending ).then( function () {\n\n\t\treturn primitiveDef.targets !== undefined\n\t\t\t? addMorphTargets( geometry, primitiveDef.targets, parser )\n\t\t\t: geometry;\n\n\t} );\n\n}\n\nexport { GLTFLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyuBA,SAAS,oBAAqB,UAAU,UAAW;AAElD,MAAK,aAAa,mBAAoB;AAErC,YAAQ,KAAM,yFAA0F;AACxG,WAAO;AAAA,EAER;AAEA,MAAK,aAAa,uBAAuB,aAAa,uBAAwB;AAE7E,QAAI,QAAQ,SAAS,SAAS;AAI9B,QAAK,UAAU,MAAO;AAErB,YAAM,UAAU,CAAC;AAEjB,YAAM,WAAW,SAAS,aAAc,UAAW;AAEnD,UAAK,aAAa,QAAY;AAE7B,iBAAU,IAAI,GAAG,IAAI,SAAS,OAAO,KAAO;AAE3C,kBAAQ,KAAM,CAAE;AAAA,QAEjB;AAEA,iBAAS,SAAU,OAAQ;AAC3B,gBAAQ,SAAS,SAAS;AAAA,MAE3B,OAAO;AAEN,gBAAQ,MAAO,yGAA0G;AACzH,eAAO;AAAA,MAER;AAAA,IAED;AAIA,UAAM,oBAAoB,MAAM,QAAQ;AACxC,UAAM,aAAa,CAAC;AAEpB,QAAK,aAAa,qBAAsB;AAIvC,eAAU,IAAI,GAAG,KAAK,mBAAmB,KAAO;AAE/C,mBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,mBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,mBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AAAA,MAEtC;AAAA,IAED,OAAO;AAIN,eAAU,IAAI,GAAG,IAAI,mBAAmB,KAAO;AAE9C,YAAK,IAAI,MAAM,GAAI;AAElB,qBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AAAA,QAEtC,OAAO;AAEN,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AAAA,QAElC;AAAA,MAED;AAAA,IAED;AAEA,QAAO,WAAW,SAAS,MAAQ,mBAAoB;AAEtD,cAAQ,MAAO,kGAAmG;AAAA,IAEnH;AAIA,UAAM,cAAc,SAAS,MAAM;AACnC,gBAAY,SAAU,UAAW;AACjC,gBAAY,YAAY;AAExB,WAAO;AAAA,EAER,OAAO;AAEN,YAAQ,MAAO,uEAAuE,QAAS;AAC/F,WAAO;AAAA,EAER;AAED;;;AC3wBA,IAAM,aAAN,cAAyB,OAAO;AAAA,EAE/B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,CAAC;AAExB,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,gCAAiC,MAAO;AAAA,IAEpD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,2BAA4B,MAAO;AAAA,IAE/C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,yBAA0B,MAAO;AAAA,IAE7C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,yBAA0B,MAAO;AAAA,IAE7C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,4BAA6B,MAAO;AAAA,IAEhD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,mCAAoC,MAAO;AAAA,IAEvD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,6BAA8B,MAAO;AAAA,IAEjD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,0BAA2B,MAAO;AAAA,IAE9C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,uCAAwC,MAAO;AAAA,IAE3D,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,+BAAgC,MAAO;AAAA,IAEnD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,kCAAmC,MAAO;AAAA,IAEtD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,iCAAkC,MAAO;AAAA,IAErD,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,2BAA4B,MAAO;AAAA,IAE/C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,oBAAqB,MAAO;AAAA,IAExC,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,uBAAwB,MAAO;AAAA,IAE3C,CAAE;AAEF,SAAK,SAAU,SAAW,QAAS;AAElC,aAAO,IAAI,sBAAuB,MAAO;AAAA,IAE1C,CAAE;AAAA,EAEH;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,QAAI;AAEJ,QAAK,KAAK,iBAAiB,IAAK;AAE/B,qBAAe,KAAK;AAAA,IAErB,WAAY,KAAK,SAAS,IAAK;AAO9B,YAAM,cAAc,YAAY,eAAgB,GAAI;AACpD,qBAAe,YAAY,WAAY,aAAa,KAAK,IAAK;AAAA,IAE/D,OAAO;AAEN,qBAAe,YAAY,eAAgB,GAAI;AAAA,IAEhD;AAKA,SAAK,QAAQ,UAAW,GAAI;AAE5B,UAAM,WAAW,SAAW,GAAI;AAE/B,UAAK,SAAU;AAEd,gBAAS,CAAE;AAAA,MAEZ,OAAO;AAEN,gBAAQ,MAAO,CAAE;AAAA,MAElB;AAEA,YAAM,QAAQ,UAAW,GAAI;AAC7B,YAAM,QAAQ,QAAS,GAAI;AAAA,IAE5B;AAEA,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAE5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAEhD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,cAAM,MAAO,MAAM,cAAc,SAAW,MAAO;AAElD,iBAAQ,IAAK;AAEb,gBAAM,QAAQ,QAAS,GAAI;AAAA,QAE5B,GAAG,QAAS;AAAA,MAEb,SAAU,GAAI;AAEb,iBAAU,CAAE;AAAA,MAEb;AAAA,IAED,GAAG,YAAY,QAAS;AAAA,EAEzB;AAAA,EAEA,eAAgB,aAAc;AAE7B,SAAK,cAAc;AACnB,WAAO;AAAA,EAER;AAAA,EAEA,eAAe;AAEd,UAAM,IAAI;AAAA,MAET;AAAA,IAED;AAAA,EAED;AAAA,EAEA,cAAe,YAAa;AAE3B,SAAK,aAAa;AAClB,WAAO;AAAA,EAER;AAAA,EAEA,kBAAmB,gBAAiB;AAEnC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EAER;AAAA,EAEA,SAAU,UAAW;AAEpB,QAAK,KAAK,gBAAgB,QAAS,QAAS,MAAM,IAAM;AAEvD,WAAK,gBAAgB,KAAM,QAAS;AAAA,IAErC;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,WAAY,UAAW;AAEtB,QAAK,KAAK,gBAAgB,QAAS,QAAS,MAAM,IAAM;AAEvD,WAAK,gBAAgB,OAAQ,KAAK,gBAAgB,QAAS,QAAS,GAAG,CAAE;AAAA,IAE1E;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,MAAO,MAAM,MAAM,QAAQ,SAAU;AAEpC,QAAI;AACJ,UAAM,aAAa,CAAC;AACpB,UAAM,UAAU,CAAC;AACjB,UAAM,cAAc,IAAI,YAAY;AAEpC,QAAK,OAAO,SAAS,UAAW;AAE/B,aAAO,KAAK,MAAO,IAAK;AAAA,IAEzB,WAAY,gBAAgB,aAAc;AAEzC,YAAM,QAAQ,YAAY,OAAQ,IAAI,WAAY,MAAM,GAAG,CAAE,CAAE;AAE/D,UAAK,UAAU,+BAAgC;AAE9C,YAAI;AAEH,qBAAY,WAAW,eAAgB,IAAI,IAAI,oBAAqB,IAAK;AAAA,QAE1E,SAAU,OAAQ;AAEjB,cAAK,QAAU,SAAS,KAAM;AAC9B;AAAA,QAED;AAEA,eAAO,KAAK,MAAO,WAAY,WAAW,eAAgB,EAAE,OAAQ;AAAA,MAErE,OAAO;AAEN,eAAO,KAAK,MAAO,YAAY,OAAQ,IAAK,CAAE;AAAA,MAE/C;AAAA,IAED,OAAO;AAEN,aAAO;AAAA,IAER;AAEA,QAAK,KAAK,UAAU,UAAa,KAAK,MAAM,QAAS,CAAE,IAAI,GAAI;AAE9D,UAAK,QAAU,SAAS,IAAI,MAAO,yEAA0E,CAAE;AAC/G;AAAA,IAED;AAEA,UAAM,SAAS,IAAI,WAAY,MAAM;AAAA,MAEpC,MAAM,QAAQ,KAAK,gBAAgB;AAAA,MACnC,aAAa,KAAK;AAAA,MAClB,eAAe,KAAK;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,IAEtB,CAAE;AAEF,WAAO,WAAW,iBAAkB,KAAK,aAAc;AAEvD,aAAU,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAO;AAExD,YAAM,SAAS,KAAK,gBAAiB,CAAE,EAAG,MAAO;AAEjD,UAAK,CAAE,OAAO,KAAO,SAAQ,MAAO,sDAAuD;AAE3F,cAAS,OAAO,IAAK,IAAI;AAMzB,iBAAY,OAAO,IAAK,IAAI;AAAA,IAE7B;AAEA,QAAK,KAAK,gBAAiB;AAE1B,eAAU,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,EAAG,GAAI;AAEvD,cAAM,gBAAgB,KAAK,eAAgB,CAAE;AAC7C,cAAM,qBAAqB,KAAK,sBAAsB,CAAC;AAEvD,gBAAS,eAAgB;AAAA,UAExB,KAAK,WAAW;AACf,uBAAY,aAAc,IAAI,IAAI,4BAA4B;AAC9D;AAAA,UAED,KAAK,WAAW;AACf,uBAAY,aAAc,IAAI,IAAI,kCAAmC,MAAM,KAAK,WAAY;AAC5F;AAAA,UAED,KAAK,WAAW;AACf,uBAAY,aAAc,IAAI,IAAI,8BAA8B;AAChE;AAAA,UAED,KAAK,WAAW;AACf,uBAAY,aAAc,IAAI,IAAI,8BAA8B;AAChE;AAAA,UAED;AAEC,gBAAK,mBAAmB,QAAS,aAAc,KAAK,KAAK,QAAS,aAAc,MAAM,QAAY;AAEjG,sBAAQ,KAAM,0CAA0C,gBAAgB,IAAK;AAAA,YAE9E;AAAA,QAEF;AAAA,MAED;AAAA,IAED;AAEA,WAAO,cAAe,UAAW;AACjC,WAAO,WAAY,OAAQ;AAC3B,WAAO,MAAO,QAAQ,OAAQ;AAAA,EAE/B;AAAA,EAEA,WAAY,MAAM,MAAO;AAExB,UAAM,QAAQ;AAEd,WAAO,IAAI,QAAS,SAAW,SAAS,QAAS;AAEhD,YAAM,MAAO,MAAM,MAAM,SAAS,MAAO;AAAA,IAE1C,CAAE;AAAA,EAEH;AAED;AAIA,SAAS,eAAe;AAEvB,MAAI,UAAU,CAAC;AAEf,SAAO;AAAA,IAEN,KAAK,SAAW,KAAM;AAErB,aAAO,QAAS,GAAI;AAAA,IAErB;AAAA,IAEA,KAAK,SAAW,KAAK,QAAS;AAE7B,cAAS,GAAI,IAAI;AAAA,IAElB;AAAA,IAEA,QAAQ,SAAW,KAAM;AAExB,aAAO,QAAS,GAAI;AAAA,IAErB;AAAA,IAEA,WAAW,WAAY;AAEtB,gBAAU,CAAC;AAAA,IAEZ;AAAA,EAED;AAED;AAMA,IAAM,aAAa;AAAA,EAClB,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAC1B;AAOA,IAAM,sBAAN,MAA0B;AAAA,EAEzB,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAGvB,SAAK,QAAQ,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE;AAAA,EAEnC;AAAA,EAEA,YAAY;AAEX,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,OAAO,KAAK,SAAS,CAAC;AAE5C,aAAU,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAe;AAE7F,YAAM,UAAU,SAAU,SAAU;AAEpC,UAAK,QAAQ,cACR,QAAQ,WAAY,KAAK,IAAK,KAC9B,QAAQ,WAAY,KAAK,IAAK,EAAE,UAAU,QAAY;AAE1D,eAAO,YAAa,KAAK,OAAO,QAAQ,WAAY,KAAK,IAAK,EAAE,KAAM;AAAA,MAEvE;AAAA,IAED;AAAA,EAED;AAAA,EAEA,WAAY,YAAa;AAExB,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,WAAW;AAC5B,QAAI,aAAa,OAAO,MAAM,IAAK,QAAS;AAE5C,QAAK,WAAa,QAAO;AAEzB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAe,KAAK,cAAc,KAAK,WAAY,KAAK,IAAK,KAAO,CAAC;AAC3E,UAAM,YAAY,WAAW,UAAU,CAAC;AACxC,UAAM,WAAW,UAAW,UAAW;AACvC,QAAI;AAEJ,UAAM,QAAQ,IAAI,MAAO,QAAS;AAElC,QAAK,SAAS,UAAU,OAAY,OAAM,OAAQ,SAAS,MAAO,CAAE,GAAG,SAAS,MAAO,CAAE,GAAG,SAAS,MAAO,CAAE,GAAG,oBAAqB;AAEtI,UAAM,QAAQ,SAAS,UAAU,SAAY,SAAS,QAAQ;AAE9D,YAAS,SAAS,MAAO;AAAA,MAExB,KAAK;AACJ,oBAAY,IAAI,iBAAkB,KAAM;AACxC,kBAAU,OAAO,SAAS,IAAK,GAAG,GAAG,EAAI;AACzC,kBAAU,IAAK,UAAU,MAAO;AAChC;AAAA,MAED,KAAK;AACJ,oBAAY,IAAI,WAAY,KAAM;AAClC,kBAAU,WAAW;AACrB;AAAA,MAED,KAAK;AACJ,oBAAY,IAAI,UAAW,KAAM;AACjC,kBAAU,WAAW;AAErB,iBAAS,OAAO,SAAS,QAAQ,CAAC;AAClC,iBAAS,KAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB;AAC3G,iBAAS,KAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB,KAAK,KAAK;AACrH,kBAAU,QAAQ,SAAS,KAAK;AAChC,kBAAU,WAAW,IAAM,SAAS,KAAK,iBAAiB,SAAS,KAAK;AACxE,kBAAU,OAAO,SAAS,IAAK,GAAG,GAAG,EAAI;AACzC,kBAAU,IAAK,UAAU,MAAO;AAChC;AAAA,MAED;AACC,cAAM,IAAI,MAAO,8CAA8C,SAAS,IAAK;AAAA,IAE/E;AAIA,cAAU,SAAS,IAAK,GAAG,GAAG,CAAE;AAEhC,cAAU,QAAQ;AAElB,2BAAwB,WAAW,QAAS;AAE5C,QAAK,SAAS,cAAc,OAAY,WAAU,YAAY,SAAS;AAEvE,cAAU,OAAO,OAAO,iBAAkB,SAAS,QAAU,WAAW,UAAa;AAErF,iBAAa,QAAQ,QAAS,SAAU;AAExC,WAAO,MAAM,IAAK,UAAU,UAAW;AAEvC,WAAO;AAAA,EAER;AAAA,EAEA,cAAe,MAAM,OAAQ;AAE5B,QAAK,SAAS,QAAU;AAExB,WAAO,KAAK,WAAY,KAAM;AAAA,EAE/B;AAAA,EAEA,qBAAsB,WAAY;AAEjC,UAAMA,QAAO;AACb,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,UAAU,KAAK,MAAO,SAAU;AACtC,UAAM,WAAa,QAAQ,cAAc,QAAQ,WAAY,KAAK,IAAK,KAAO,CAAC;AAC/E,UAAM,aAAa,SAAS;AAE5B,QAAK,eAAe,OAAY,QAAO;AAEvC,WAAO,KAAK,WAAY,UAAW,EAAE,KAAM,SAAW,OAAQ;AAE7D,aAAO,OAAO,YAAaA,MAAK,OAAO,YAAY,KAAM;AAAA,IAE1D,CAAE;AAAA,EAEH;AAED;AAOA,IAAM,8BAAN,MAAkC;AAAA,EAEjC,cAAc;AAEb,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,kBAAkB;AAEjB,WAAO;AAAA,EAER;AAAA,EAEA,aAAc,gBAAgB,aAAa,QAAS;AAEnD,UAAM,UAAU,CAAC;AAEjB,mBAAe,QAAQ,IAAI,MAAO,GAAK,GAAK,CAAI;AAChD,mBAAe,UAAU;AAEzB,UAAM,oBAAoB,YAAY;AAEtC,QAAK,mBAAoB;AAExB,UAAK,MAAM,QAAS,kBAAkB,eAAgB,GAAI;AAEzD,cAAM,QAAQ,kBAAkB;AAEhC,uBAAe,MAAM,OAAQ,MAAO,CAAE,GAAG,MAAO,CAAE,GAAG,MAAO,CAAE,GAAG,oBAAqB;AACtF,uBAAe,UAAU,MAAO,CAAE;AAAA,MAEnC;AAEA,UAAK,kBAAkB,qBAAqB,QAAY;AAEvD,gBAAQ,KAAM,OAAO,cAAe,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAe,CAAE;AAAA,MAEjH;AAAA,IAED;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,yCAAN,MAA6C;AAAA,EAE5C,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,mBAAmB,YAAY,WAAY,KAAK,IAAK,EAAE;AAE7D,QAAK,qBAAqB,QAAY;AAErC,qBAAe,oBAAoB;AAAA,IAEpC;AAEA,WAAO,QAAQ,QAAQ;AAAA,EAExB;AAED;AAOA,IAAM,kCAAN,MAAsC;AAAA,EAErC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,QAAK,UAAU,oBAAoB,QAAY;AAE9C,qBAAe,YAAY,UAAU;AAAA,IAEtC;AAEA,QAAK,UAAU,qBAAqB,QAAY;AAE/C,cAAQ,KAAM,OAAO,cAAe,gBAAgB,gBAAgB,UAAU,gBAAiB,CAAE;AAAA,IAElG;AAEA,QAAK,UAAU,6BAA6B,QAAY;AAEvD,qBAAe,qBAAqB,UAAU;AAAA,IAE/C;AAEA,QAAK,UAAU,8BAA8B,QAAY;AAExD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,yBAAyB,UAAU,yBAA0B,CAAE;AAAA,IAEpH;AAEA,QAAK,UAAU,2BAA2B,QAAY;AAErD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,sBAAsB,UAAU,sBAAuB,CAAE;AAE7G,UAAK,UAAU,uBAAuB,UAAU,QAAY;AAE3D,cAAM,QAAQ,UAAU,uBAAuB;AAE/C,uBAAe,uBAAuB,IAAI,QAAS,OAAO,KAAM;AAAA,MAEjE;AAAA,IAED;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,oCAAN,MAAwC;AAAA,EAEvC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,QAAK,UAAU,sBAAsB,QAAY;AAEhD,qBAAe,cAAc,UAAU;AAAA,IAExC;AAEA,QAAK,UAAU,uBAAuB,QAAY;AAEjD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,kBAAkB,UAAU,kBAAmB,CAAE;AAAA,IAEtG;AAEA,QAAK,UAAU,mBAAmB,QAAY;AAE7C,qBAAe,iBAAiB,UAAU;AAAA,IAE3C;AAEA,QAAK,eAAe,8BAA8B,QAAY;AAE7D,qBAAe,4BAA4B,CAAE,KAAK,GAAI;AAAA,IAEvD;AAEA,QAAK,UAAU,gCAAgC,QAAY;AAE1D,qBAAe,0BAA2B,CAAE,IAAI,UAAU;AAAA,IAE3D;AAEA,QAAK,UAAU,gCAAgC,QAAY;AAE1D,qBAAe,0BAA2B,CAAE,IAAI,UAAU;AAAA,IAE3D;AAEA,QAAK,UAAU,gCAAgC,QAAY;AAE1D,cAAQ,KAAM,OAAO,cAAe,gBAAgB,2BAA2B,UAAU,2BAA4B,CAAE;AAAA,IAExH;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,8BAAN,MAAkC;AAAA,EAEjC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,mBAAe,aAAa,IAAI,MAAO,GAAG,GAAG,CAAE;AAC/C,mBAAe,iBAAiB;AAChC,mBAAe,QAAQ;AAEvB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,QAAK,UAAU,qBAAqB,QAAY;AAE/C,YAAM,cAAc,UAAU;AAC9B,qBAAe,WAAW,OAAQ,YAAa,CAAE,GAAG,YAAa,CAAE,GAAG,YAAa,CAAE,GAAG,oBAAqB;AAAA,IAE9G;AAEA,QAAK,UAAU,yBAAyB,QAAY;AAEnD,qBAAe,iBAAiB,UAAU;AAAA,IAE3C;AAEA,QAAK,UAAU,sBAAsB,QAAY;AAEhD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,iBAAiB,UAAU,mBAAmB,cAAe,CAAE;AAAA,IAEpH;AAEA,QAAK,UAAU,0BAA0B,QAAY;AAEpD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,qBAAqB,UAAU,qBAAsB,CAAE;AAAA,IAE5G;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAQA,IAAM,qCAAN,MAAyC;AAAA,EAExC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,QAAK,UAAU,uBAAuB,QAAY;AAEjD,qBAAe,eAAe,UAAU;AAAA,IAEzC;AAEA,QAAK,UAAU,wBAAwB,QAAY;AAElD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,mBAAmB,UAAU,mBAAoB,CAAE;AAAA,IAExG;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,+BAAN,MAAmC;AAAA,EAElC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,mBAAe,YAAY,UAAU,oBAAoB,SAAY,UAAU,kBAAkB;AAEjG,QAAK,UAAU,qBAAqB,QAAY;AAE/C,cAAQ,KAAM,OAAO,cAAe,gBAAgB,gBAAgB,UAAU,gBAAiB,CAAE;AAAA,IAElG;AAEA,mBAAe,sBAAsB,UAAU,uBAAuB;AAEtE,UAAM,aAAa,UAAU,oBAAoB,CAAE,GAAG,GAAG,CAAE;AAC3D,mBAAe,mBAAmB,IAAI,MAAM,EAAE,OAAQ,WAAY,CAAE,GAAG,WAAY,CAAE,GAAG,WAAY,CAAE,GAAG,oBAAqB;AAE9H,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,4BAAN,MAAgC;AAAA,EAE/B,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,mBAAe,MAAM,UAAU,QAAQ,SAAY,UAAU,MAAM;AAEnE,WAAO,QAAQ,QAAQ;AAAA,EAExB;AAED;AAOA,IAAM,iCAAN,MAAqC;AAAA,EAEpC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,mBAAe,oBAAoB,UAAU,mBAAmB,SAAY,UAAU,iBAAiB;AAEvG,QAAK,UAAU,oBAAoB,QAAY;AAE9C,cAAQ,KAAM,OAAO,cAAe,gBAAgB,wBAAwB,UAAU,eAAgB,CAAE;AAAA,IAEzG;AAEA,UAAM,aAAa,UAAU,uBAAuB,CAAE,GAAG,GAAG,CAAE;AAC9D,mBAAe,gBAAgB,IAAI,MAAM,EAAE,OAAQ,WAAY,CAAE,GAAG,WAAY,CAAE,GAAG,WAAY,CAAE,GAAG,oBAAqB;AAE3H,QAAK,UAAU,yBAAyB,QAAY;AAEnD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,oBAAoB,UAAU,sBAAsB,cAAe,CAAE;AAAA,IAE1H;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAQA,IAAM,6BAAN,MAAiC;AAAA,EAEhC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,mBAAe,YAAY,UAAU,eAAe,SAAY,UAAU,aAAa;AAEvF,QAAK,UAAU,gBAAgB,QAAY;AAE1C,cAAQ,KAAM,OAAO,cAAe,gBAAgB,WAAW,UAAU,WAAY,CAAE;AAAA,IAExF;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,mCAAN,MAAuC;AAAA,EAEtC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,gBAAiB,eAAgB;AAEhC,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,EAAI,QAAO;AAEhF,WAAO;AAAA,EAER;AAAA,EAEA,qBAAsB,eAAe,gBAAiB;AAErD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAW,aAAc;AAEzD,QAAK,CAAE,YAAY,cAAc,CAAE,YAAY,WAAY,KAAK,IAAK,GAAI;AAExE,aAAO,QAAQ,QAAQ;AAAA,IAExB;AAEA,UAAM,UAAU,CAAC;AAEjB,UAAM,YAAY,YAAY,WAAY,KAAK,IAAK;AAEpD,QAAK,UAAU,uBAAuB,QAAY;AAEjD,qBAAe,aAAa,UAAU;AAAA,IAEvC;AAEA,QAAK,UAAU,uBAAuB,QAAY;AAEjD,qBAAe,qBAAqB,UAAU;AAAA,IAE/C;AAEA,QAAK,UAAU,sBAAsB,QAAY;AAEhD,cAAQ,KAAM,OAAO,cAAe,gBAAgB,iBAAiB,UAAU,iBAAkB,CAAE;AAAA,IAEpG;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAED;AAOA,IAAM,6BAAN,MAAiC;AAAA,EAEhC,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,YAAa,cAAe;AAE3B,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAU,YAAa;AAE/C,QAAK,CAAE,WAAW,cAAc,CAAE,WAAW,WAAY,KAAK,IAAK,GAAI;AAEtE,aAAO;AAAA,IAER;AAEA,UAAM,YAAY,WAAW,WAAY,KAAK,IAAK;AACnD,UAAM,SAAS,OAAO,QAAQ;AAE9B,QAAK,CAAE,QAAS;AAEf,UAAK,KAAK,sBAAsB,KAAK,mBAAmB,QAAS,KAAK,IAAK,KAAK,GAAI;AAEnF,cAAM,IAAI,MAAO,6EAA8E;AAAA,MAEhG,OAAO;AAGN,eAAO;AAAA,MAER;AAAA,IAED;AAEA,WAAO,OAAO,iBAAkB,cAAc,UAAU,QAAQ,MAAO;AAAA,EAExE;AAED;AAOA,IAAM,2BAAN,MAA+B;AAAA,EAE9B,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EAEpB;AAAA,EAEA,YAAa,cAAe;AAE3B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAU,YAAa;AAE/C,QAAK,CAAE,WAAW,cAAc,CAAE,WAAW,WAAY,IAAK,GAAI;AAEjE,aAAO;AAAA,IAER;AAEA,UAAM,YAAY,WAAW,WAAY,IAAK;AAC9C,UAAM,SAAS,KAAK,OAAQ,UAAU,MAAO;AAE7C,QAAI,SAAS,OAAO;AACpB,QAAK,OAAO,KAAM;AAEjB,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAY,OAAO,GAAI;AAC9D,UAAK,YAAY,KAAO,UAAS;AAAA,IAElC;AAEA,WAAO,KAAK,cAAc,EAAE,KAAM,SAAW,aAAc;AAE1D,UAAK,YAAc,QAAO,OAAO,iBAAkB,cAAc,UAAU,QAAQ,MAAO;AAE1F,UAAK,KAAK,sBAAsB,KAAK,mBAAmB,QAAS,IAAK,KAAK,GAAI;AAE9E,cAAM,IAAI,MAAO,2DAA4D;AAAA,MAE9E;AAGA,aAAO,OAAO,YAAa,YAAa;AAAA,IAEzC,CAAE;AAAA,EAEH;AAAA,EAEA,gBAAgB;AAEf,QAAK,CAAE,KAAK,aAAc;AAEzB,WAAK,cAAc,IAAI,QAAS,SAAW,SAAU;AAEpD,cAAM,QAAQ,IAAI,MAAM;AAIxB,cAAM,MAAM;AAEZ,cAAM,SAAS,MAAM,UAAU,WAAY;AAE1C,kBAAS,MAAM,WAAW,CAAE;AAAA,QAE7B;AAAA,MAED,CAAE;AAAA,IAEH;AAEA,WAAO,KAAK;AAAA,EAEb;AAED;AAOA,IAAM,2BAAN,MAA+B;AAAA,EAE9B,YAAa,QAAS;AAErB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EAEpB;AAAA,EAEA,YAAa,cAAe;AAE3B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAU,YAAa;AAE/C,QAAK,CAAE,WAAW,cAAc,CAAE,WAAW,WAAY,IAAK,GAAI;AAEjE,aAAO;AAAA,IAER;AAEA,UAAM,YAAY,WAAW,WAAY,IAAK;AAC9C,UAAM,SAAS,KAAK,OAAQ,UAAU,MAAO;AAE7C,QAAI,SAAS,OAAO;AACpB,QAAK,OAAO,KAAM;AAEjB,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAY,OAAO,GAAI;AAC9D,UAAK,YAAY,KAAO,UAAS;AAAA,IAElC;AAEA,WAAO,KAAK,cAAc,EAAE,KAAM,SAAW,aAAc;AAE1D,UAAK,YAAc,QAAO,OAAO,iBAAkB,cAAc,UAAU,QAAQ,MAAO;AAE1F,UAAK,KAAK,sBAAsB,KAAK,mBAAmB,QAAS,IAAK,KAAK,GAAI;AAE9E,cAAM,IAAI,MAAO,2DAA4D;AAAA,MAE9E;AAGA,aAAO,OAAO,YAAa,YAAa;AAAA,IAEzC,CAAE;AAAA,EAEH;AAAA,EAEA,gBAAgB;AAEf,QAAK,CAAE,KAAK,aAAc;AAEzB,WAAK,cAAc,IAAI,QAAS,SAAW,SAAU;AAEpD,cAAM,QAAQ,IAAI,MAAM;AAGxB,cAAM,MAAM;AACZ,cAAM,SAAS,MAAM,UAAU,WAAY;AAE1C,kBAAS,MAAM,WAAW,CAAE;AAAA,QAE7B;AAAA,MAED,CAAE;AAAA,IAEH;AAEA,WAAO,KAAK;AAAA,EAEb;AAED;AAOA,IAAM,yBAAN,MAA6B;AAAA,EAE5B,YAAa,QAAS;AAErB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EAEf;AAAA,EAEA,eAAgB,OAAQ;AAEvB,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,aAAa,KAAK,YAAa,KAAM;AAE3C,QAAK,WAAW,cAAc,WAAW,WAAY,KAAK,IAAK,GAAI;AAElE,YAAM,eAAe,WAAW,WAAY,KAAK,IAAK;AAEtD,YAAM,SAAS,KAAK,OAAO,cAAe,UAAU,aAAa,MAAO;AACxE,YAAM,UAAU,KAAK,OAAO,QAAQ;AAEpC,UAAK,CAAE,WAAW,CAAE,QAAQ,WAAY;AAEvC,YAAK,KAAK,sBAAsB,KAAK,mBAAmB,QAAS,KAAK,IAAK,KAAK,GAAI;AAEnF,gBAAM,IAAI,MAAO,oFAAqF;AAAA,QAEvG,OAAO;AAGN,iBAAO;AAAA,QAER;AAAA,MAED;AAEA,aAAO,OAAO,KAAM,SAAW,KAAM;AAEpC,cAAM,aAAa,aAAa,cAAc;AAC9C,cAAM,aAAa,aAAa,cAAc;AAE9C,cAAM,QAAQ,aAAa;AAC3B,cAAM,SAAS,aAAa;AAE5B,cAAM,SAAS,IAAI,WAAY,KAAK,YAAY,UAAW;AAE3D,YAAK,QAAQ,uBAAwB;AAEpC,iBAAO,QAAQ,sBAAuB,OAAO,QAAQ,QAAQ,aAAa,MAAM,aAAa,MAAO,EAAE,KAAM,SAAWC,MAAM;AAE5H,mBAAOA,KAAI;AAAA,UAEZ,CAAE;AAAA,QAEH,OAAO;AAGN,iBAAO,QAAQ,MAAM,KAAM,WAAY;AAEtC,kBAAM,SAAS,IAAI,YAAa,QAAQ,MAAO;AAC/C,oBAAQ,iBAAkB,IAAI,WAAY,MAAO,GAAG,OAAO,QAAQ,QAAQ,aAAa,MAAM,aAAa,MAAO;AAClH,mBAAO;AAAA,UAER,CAAE;AAAA,QAEH;AAAA,MAED,CAAE;AAAA,IAEH,OAAO;AAEN,aAAO;AAAA,IAER;AAAA,EAED;AAED;AAQA,IAAM,wBAAN,MAA4B;AAAA,EAE3B,YAAa,QAAS;AAErB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EAEf;AAAA,EAEA,eAAgB,WAAY;AAE3B,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,UAAU,KAAK,MAAO,SAAU;AAEtC,QAAK,CAAE,QAAQ,cAAc,CAAE,QAAQ,WAAY,KAAK,IAAK,KAC5D,QAAQ,SAAS,QAAY;AAE7B,aAAO;AAAA,IAER;AAEA,UAAM,UAAU,KAAK,OAAQ,QAAQ,IAAK;AAI1C,eAAY,aAAa,QAAQ,YAAa;AAE7C,UAAK,UAAU,SAAS,gBAAgB,aACtC,UAAU,SAAS,gBAAgB,kBACnC,UAAU,SAAS,gBAAgB,gBACnC,UAAU,SAAS,QAAY;AAEhC,eAAO;AAAA,MAER;AAAA,IAED;AAEA,UAAM,eAAe,QAAQ,WAAY,KAAK,IAAK;AACnD,UAAM,gBAAgB,aAAa;AAInC,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,CAAC;AAEpB,eAAY,OAAO,eAAgB;AAElC,cAAQ,KAAM,KAAK,OAAO,cAAe,YAAY,cAAe,GAAI,CAAE,EAAE,KAAM,cAAY;AAE7F,mBAAY,GAAI,IAAI;AACpB,eAAO,WAAY,GAAI;AAAA,MAExB,CAAE,CAAE;AAAA,IAEL;AAEA,QAAK,QAAQ,SAAS,GAAI;AAEzB,aAAO;AAAA,IAER;AAEA,YAAQ,KAAM,KAAK,OAAO,eAAgB,SAAU,CAAE;AAEtD,WAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,aAAW;AAE9C,YAAM,aAAa,QAAQ,IAAI;AAC/B,YAAM,SAAS,WAAW,UAAU,WAAW,WAAW,CAAE,UAAW;AACvE,YAAM,QAAQ,QAAS,CAAE,EAAE;AAC3B,YAAM,kBAAkB,CAAC;AAEzB,iBAAY,QAAQ,QAAS;AAG5B,cAAM,IAAI,IAAI,QAAQ;AACtB,cAAM,IAAI,IAAI,QAAQ;AACtB,cAAM,IAAI,IAAI,WAAW;AACzB,cAAM,IAAI,IAAI,QAAS,GAAG,GAAG,CAAE;AAE/B,cAAM,gBAAgB,IAAI,cAAe,KAAK,UAAU,KAAK,UAAU,KAAM;AAE7E,iBAAU,IAAI,GAAG,IAAI,OAAO,KAAO;AAElC,cAAK,WAAW,aAAc;AAE7B,cAAE,oBAAqB,WAAW,aAAa,CAAE;AAAA,UAElD;AAEA,cAAK,WAAW,UAAW;AAE1B,cAAE,oBAAqB,WAAW,UAAU,CAAE;AAAA,UAE/C;AAEA,cAAK,WAAW,OAAQ;AAEvB,cAAE,oBAAqB,WAAW,OAAO,CAAE;AAAA,UAE5C;AAEA,wBAAc,YAAa,GAAG,EAAE,QAAS,GAAG,GAAG,CAAE,CAAE;AAAA,QAEpD;AAGA,mBAAY,iBAAiB,YAAa;AAEzC,cAAK,kBAAkB,YAAa;AAEnC,kBAAM,OAAO,WAAY,aAAc;AACvC,0BAAc,gBAAgB,IAAI,yBAA0B,KAAK,OAAO,KAAK,UAAU,KAAK,UAAW;AAAA,UAExG,WAAY,kBAAkB,iBAC5B,kBAAkB,cAClB,kBAAkB,SAAU;AAE7B,iBAAK,SAAS,aAAc,eAAe,WAAY,aAAc,CAAE;AAAA,UAExE;AAAA,QAED;AAGA,iBAAS,UAAU,KAAK,KAAM,eAAe,IAAK;AAElD,aAAK,OAAO,oBAAqB,aAAc;AAE/C,wBAAgB,KAAM,aAAc;AAAA,MAErC;AAEA,UAAK,WAAW,SAAU;AAEzB,mBAAW,MAAM;AAEjB,mBAAW,IAAK,GAAI,eAAgB;AAEpC,eAAO;AAAA,MAER;AAEA,aAAO,gBAAiB,CAAE;AAAA,IAE3B,CAAE;AAAA,EAEH;AAED;AAGA,IAAM,gCAAgC;AACtC,IAAM,iCAAiC;AACvC,IAAM,+BAA+B,EAAE,MAAM,YAAY,KAAK,QAAW;AAEzE,IAAM,sBAAN,MAA0B;AAAA,EAEzB,YAAa,MAAO;AAEnB,SAAK,OAAO,WAAW;AACvB,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,UAAM,aAAa,IAAI,SAAU,MAAM,GAAG,8BAA+B;AACzE,UAAM,cAAc,IAAI,YAAY;AAEpC,SAAK,SAAS;AAAA,MACb,OAAO,YAAY,OAAQ,IAAI,WAAY,KAAK,MAAO,GAAG,CAAE,CAAE,CAAE;AAAA,MAChE,SAAS,WAAW,UAAW,GAAG,IAAK;AAAA,MACvC,QAAQ,WAAW,UAAW,GAAG,IAAK;AAAA,IACvC;AAEA,QAAK,KAAK,OAAO,UAAU,+BAAgC;AAE1D,YAAM,IAAI,MAAO,mDAAoD;AAAA,IAEtE,WAAY,KAAK,OAAO,UAAU,GAAM;AAEvC,YAAM,IAAI,MAAO,gDAAiD;AAAA,IAEnE;AAEA,UAAM,sBAAsB,KAAK,OAAO,SAAS;AACjD,UAAM,YAAY,IAAI,SAAU,MAAM,8BAA+B;AACrE,QAAI,aAAa;AAEjB,WAAQ,aAAa,qBAAsB;AAE1C,YAAM,cAAc,UAAU,UAAW,YAAY,IAAK;AAC1D,oBAAc;AAEd,YAAM,YAAY,UAAU,UAAW,YAAY,IAAK;AACxD,oBAAc;AAEd,UAAK,cAAc,6BAA6B,MAAO;AAEtD,cAAM,eAAe,IAAI,WAAY,MAAM,iCAAiC,YAAY,WAAY;AACpG,aAAK,UAAU,YAAY,OAAQ,YAAa;AAAA,MAEjD,WAAY,cAAc,6BAA6B,KAAM;AAE5D,cAAM,aAAa,iCAAiC;AACpD,aAAK,OAAO,KAAK,MAAO,YAAY,aAAa,WAAY;AAAA,MAE9D;AAIA,oBAAc;AAAA,IAEf;AAEA,QAAK,KAAK,YAAY,MAAO;AAE5B,YAAM,IAAI,MAAO,2CAA4C;AAAA,IAE9D;AAAA,EAED;AAED;AAOA,IAAM,oCAAN,MAAwC;AAAA,EAEvC,YAAa,MAAM,aAAc;AAEhC,QAAK,CAAE,aAAc;AAEpB,YAAM,IAAI,MAAO,qDAAsD;AAAA,IAExE;AAEA,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,YAAY,QAAQ;AAAA,EAE1B;AAAA,EAEA,gBAAiB,WAAW,QAAS;AAEpC,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,KAAK;AACzB,UAAM,kBAAkB,UAAU,WAAY,KAAK,IAAK,EAAE;AAC1D,UAAM,mBAAmB,UAAU,WAAY,KAAK,IAAK,EAAE;AAC3D,UAAM,oBAAoB,CAAC;AAC3B,UAAM,yBAAyB,CAAC;AAChC,UAAM,mBAAmB,CAAC;AAE1B,eAAY,iBAAiB,kBAAmB;AAE/C,YAAM,qBAAqB,WAAY,aAAc,KAAK,cAAc,YAAY;AAEpF,wBAAmB,kBAAmB,IAAI,iBAAkB,aAAc;AAAA,IAE3E;AAEA,eAAY,iBAAiB,UAAU,YAAa;AAEnD,YAAM,qBAAqB,WAAY,aAAc,KAAK,cAAc,YAAY;AAEpF,UAAK,iBAAkB,aAAc,MAAM,QAAY;AAEtD,cAAM,cAAc,KAAK,UAAW,UAAU,WAAY,aAAc,CAAE;AAC1E,cAAM,gBAAgB,sBAAuB,YAAY,aAAc;AAEvE,yBAAkB,kBAAmB,IAAI,cAAc;AACvD,+BAAwB,kBAAmB,IAAI,YAAY,eAAe;AAAA,MAE3E;AAAA,IAED;AAEA,WAAO,OAAO,cAAe,cAAc,eAAgB,EAAE,KAAM,SAAW,YAAa;AAE1F,aAAO,IAAI,QAAS,SAAW,SAAS,QAAS;AAEhD,oBAAY,gBAAiB,YAAY,SAAW,UAAW;AAE9D,qBAAY,iBAAiB,SAAS,YAAa;AAElD,kBAAM,YAAY,SAAS,WAAY,aAAc;AACrD,kBAAM,aAAa,uBAAwB,aAAc;AAEzD,gBAAK,eAAe,OAAY,WAAU,aAAa;AAAA,UAExD;AAEA,kBAAS,QAAS;AAAA,QAEnB,GAAG,mBAAmB,kBAAkB,sBAAsB,MAAO;AAAA,MAEtE,CAAE;AAAA,IAEH,CAAE;AAAA,EAEH;AAED;AAOA,IAAM,gCAAN,MAAoC;AAAA,EAEnC,cAAc;AAEb,SAAK,OAAO,WAAW;AAAA,EAExB;AAAA,EAEA,cAAe,SAAS,WAAY;AAEnC,SAAO,UAAU,aAAa,UAAa,UAAU,aAAa,QAAQ,YACtE,UAAU,WAAW,UACrB,UAAU,aAAa,UACvB,UAAU,UAAU,QAAY;AAGnC,aAAO;AAAA,IAER;AAEA,cAAU,QAAQ,MAAM;AAExB,QAAK,UAAU,aAAa,QAAY;AAEvC,cAAQ,UAAU,UAAU;AAAA,IAE7B;AAEA,QAAK,UAAU,WAAW,QAAY;AAErC,cAAQ,OAAO,UAAW,UAAU,MAAO;AAAA,IAE5C;AAEA,QAAK,UAAU,aAAa,QAAY;AAEvC,cAAQ,WAAW,UAAU;AAAA,IAE9B;AAEA,QAAK,UAAU,UAAU,QAAY;AAEpC,cAAQ,OAAO,UAAW,UAAU,KAAM;AAAA,IAE3C;AAEA,YAAQ,cAAc;AAEtB,WAAO;AAAA,EAER;AAED;AAOA,IAAM,gCAAN,MAAoC;AAAA,EAEnC,cAAc;AAEb,SAAK,OAAO,WAAW;AAAA,EAExB;AAED;AAQA,IAAM,6BAAN,cAAyC,YAAY;AAAA,EAEpD,YAAa,oBAAoB,cAAc,YAAY,cAAe;AAEzE,UAAO,oBAAoB,cAAc,YAAY,YAAa;AAAA,EAEnE;AAAA,EAEA,iBAAkB,OAAQ;AAKzB,UAAM,SAAS,KAAK,cACnB,SAAS,KAAK,cACd,YAAY,KAAK,WACjB,SAAS,QAAQ,YAAY,IAAI;AAElC,aAAU,IAAI,GAAG,MAAM,WAAW,KAAO;AAExC,aAAQ,CAAE,IAAI,OAAQ,SAAS,CAAE;AAAA,IAElC;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,aAAc,IAAI,IAAI,GAAG,IAAK;AAE7B,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AAEpB,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AAEzB,UAAM,KAAK,KAAK;AAEhB,UAAM,KAAM,IAAI,MAAO;AACvB,UAAM,KAAK,IAAI;AACf,UAAM,MAAM,KAAK;AAEjB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,UAAU;AAE1B,UAAM,KAAK,KAAM,MAAM,IAAI;AAC3B,UAAM,KAAK,MAAM;AACjB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,KAAK,KAAK;AAIrB,aAAU,IAAI,GAAG,MAAM,QAAQ,KAAO;AAErC,YAAM,KAAK,OAAQ,UAAU,IAAI,MAAO;AACxC,YAAM,KAAK,OAAQ,UAAU,IAAI,OAAQ,IAAI;AAC7C,YAAM,KAAK,OAAQ,UAAU,IAAI,MAAO;AACxC,YAAM,KAAK,OAAQ,UAAU,CAAE,IAAI;AAEnC,aAAQ,CAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IAElD;AAEA,WAAO;AAAA,EAER;AAED;AAEA,IAAM,KAAK,IAAI,WAAW;AAE1B,IAAM,uCAAN,cAAmD,2BAA2B;AAAA,EAE7E,aAAc,IAAI,IAAI,GAAG,IAAK;AAE7B,UAAM,SAAS,MAAM,aAAc,IAAI,IAAI,GAAG,EAAG;AAEjD,OAAG,UAAW,MAAO,EAAE,UAAU,EAAE,QAAS,MAAO;AAEnD,WAAO;AAAA,EAER;AAED;AASA,IAAM,kBAAkB;AAAA,EACvB,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AACjB;AAEA,IAAM,wBAAwB;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACP;AAEA,IAAM,gBAAgB;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACP;AAEA,IAAM,kBAAkB;AAAA,EACvB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACR;AAEA,IAAM,mBAAmB;AAAA,EACxB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACT;AAEA,IAAM,aAAa;AAAA,EAClB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AACX;AAEA,IAAM,kBAAkB;AAAA,EACvB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,UAAU;AAAA,EACV,SAAS;AACV;AAEA,IAAM,gBAAgB;AAAA,EACrB,aAAa;AAAA;AAAA;AAAA,EAEb,QAAQ;AAAA,EACR,MAAM;AACP;AAEA,IAAM,cAAc;AAAA,EACnB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACR;AAKA,SAAS,sBAAuB,OAAQ;AAEvC,MAAK,MAAO,iBAAkB,MAAM,QAAY;AAE/C,UAAO,iBAAkB,IAAI,IAAI,qBAAsB;AAAA,MACtD,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACP,CAAE;AAAA,EAEH;AAEA,SAAO,MAAO,iBAAkB;AAEjC;AAEA,SAAS,+BAAgC,iBAAiB,QAAQ,WAAY;AAI7E,aAAY,QAAQ,UAAU,YAAa;AAE1C,QAAK,gBAAiB,IAAK,MAAM,QAAY;AAE5C,aAAO,SAAS,iBAAiB,OAAO,SAAS,kBAAkB,CAAC;AACpE,aAAO,SAAS,eAAgB,IAAK,IAAI,UAAU,WAAY,IAAK;AAAA,IAErE;AAAA,EAED;AAED;AAMA,SAAS,uBAAwB,QAAQ,SAAU;AAElD,MAAK,QAAQ,WAAW,QAAY;AAEnC,QAAK,OAAO,QAAQ,WAAW,UAAW;AAEzC,aAAO,OAAQ,OAAO,UAAU,QAAQ,MAAO;AAAA,IAEhD,OAAO;AAEN,cAAQ,KAAM,wDAAwD,QAAQ,MAAO;AAAA,IAEtF;AAAA,EAED;AAED;AAUA,SAAS,gBAAiB,UAAU,SAAS,QAAS;AAErD,MAAI,mBAAmB;AACvB,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AAEpB,WAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEpD,UAAM,SAAS,QAAS,CAAE;AAE1B,QAAK,OAAO,aAAa,OAAY,oBAAmB;AACxD,QAAK,OAAO,WAAW,OAAY,kBAAiB;AACpD,QAAK,OAAO,YAAY,OAAY,iBAAgB;AAEpD,QAAK,oBAAoB,kBAAkB,cAAgB;AAAA,EAE5D;AAEA,MAAK,CAAE,oBAAoB,CAAE,kBAAkB,CAAE,cAAgB,QAAO,QAAQ,QAAS,QAAS;AAElG,QAAM,2BAA2B,CAAC;AAClC,QAAM,yBAAyB,CAAC;AAChC,QAAM,wBAAwB,CAAC;AAE/B,WAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEpD,UAAM,SAAS,QAAS,CAAE;AAE1B,QAAK,kBAAmB;AAEvB,YAAM,kBAAkB,OAAO,aAAa,SACzC,OAAO,cAAe,YAAY,OAAO,QAAS,IAClD,SAAS,WAAW;AAEvB,+BAAyB,KAAM,eAAgB;AAAA,IAEhD;AAEA,QAAK,gBAAiB;AAErB,YAAM,kBAAkB,OAAO,WAAW,SACvC,OAAO,cAAe,YAAY,OAAO,MAAO,IAChD,SAAS,WAAW;AAEvB,6BAAuB,KAAM,eAAgB;AAAA,IAE9C;AAEA,QAAK,eAAgB;AAEpB,YAAM,kBAAkB,OAAO,YAAY,SACxC,OAAO,cAAe,YAAY,OAAO,OAAQ,IACjD,SAAS,WAAW;AAEvB,4BAAsB,KAAM,eAAgB;AAAA,IAE7C;AAAA,EAED;AAEA,SAAO,QAAQ,IAAK;AAAA,IACnB,QAAQ,IAAK,wBAAyB;AAAA,IACtC,QAAQ,IAAK,sBAAuB;AAAA,IACpC,QAAQ,IAAK,qBAAsB;AAAA,EACpC,CAAE,EAAE,KAAM,SAAW,WAAY;AAEhC,UAAM,iBAAiB,UAAW,CAAE;AACpC,UAAM,eAAe,UAAW,CAAE;AAClC,UAAM,cAAc,UAAW,CAAE;AAEjC,QAAK,iBAAmB,UAAS,gBAAgB,WAAW;AAC5D,QAAK,eAAiB,UAAS,gBAAgB,SAAS;AACxD,QAAK,cAAgB,UAAS,gBAAgB,QAAQ;AACtD,aAAS,uBAAuB;AAEhC,WAAO;AAAA,EAER,CAAE;AAEH;AAMA,SAAS,mBAAoB,MAAM,SAAU;AAE5C,OAAK,mBAAmB;AAExB,MAAK,QAAQ,YAAY,QAAY;AAEpC,aAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAE5D,WAAK,sBAAuB,CAAE,IAAI,QAAQ,QAAS,CAAE;AAAA,IAEtD;AAAA,EAED;AAGA,MAAK,QAAQ,UAAU,MAAM,QAAS,QAAQ,OAAO,WAAY,GAAI;AAEpE,UAAM,cAAc,QAAQ,OAAO;AAEnC,QAAK,KAAK,sBAAsB,WAAW,YAAY,QAAS;AAE/D,WAAK,wBAAwB,CAAC;AAE9B,eAAU,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAO;AAExD,aAAK,sBAAuB,YAAa,CAAE,CAAE,IAAI;AAAA,MAElD;AAAA,IAED,OAAO;AAEN,cAAQ,KAAM,sEAAuE;AAAA,IAEtF;AAAA,EAED;AAED;AAEA,SAAS,mBAAoB,cAAe;AAE3C,MAAI;AAEJ,QAAM,iBAAiB,aAAa,cAAc,aAAa,WAAY,WAAW,0BAA2B;AAEjH,MAAK,gBAAiB;AAErB,kBAAc,WAAW,eAAe,aACpC,MAAM,eAAe,UACrB,MAAM,oBAAqB,eAAe,UAAW;AAAA,EAE1D,OAAO;AAEN,kBAAc,aAAa,UAAU,MAAM,oBAAqB,aAAa,UAAW,IAAI,MAAM,aAAa;AAAA,EAEhH;AAEA,MAAK,aAAa,YAAY,QAAY;AAEzC,aAAU,IAAI,GAAG,KAAK,aAAa,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEjE,qBAAe,MAAM,oBAAqB,aAAa,QAAS,CAAE,CAAE;AAAA,IAErE;AAAA,EAED;AAEA,SAAO;AAER;AAEA,SAAS,oBAAqB,YAAa;AAE1C,MAAI,gBAAgB;AAEpB,QAAM,OAAO,OAAO,KAAM,UAAW,EAAE,KAAK;AAE5C,WAAU,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAO;AAEjD,qBAAiB,KAAM,CAAE,IAAI,MAAM,WAAY,KAAM,CAAE,CAAE,IAAI;AAAA,EAE9D;AAEA,SAAO;AAER;AAEA,SAAS,4BAA6B,aAAc;AAKnD,UAAS,aAAc;AAAA,IAEtB,KAAK;AACJ,aAAO,IAAI;AAAA,IAEZ,KAAK;AACJ,aAAO,IAAI;AAAA,IAEZ,KAAK;AACJ,aAAO,IAAI;AAAA,IAEZ,KAAK;AACJ,aAAO,IAAI;AAAA,IAEZ;AACC,YAAM,IAAI,MAAO,mEAAoE;AAAA,EAEvF;AAED;AAEA,SAAS,oBAAqB,KAAM;AAEnC,MAAK,IAAI,OAAQ,gBAAiB,IAAI,KAAK,IAAI,OAAQ,oBAAqB,MAAM,EAAI,QAAO;AAC7F,MAAK,IAAI,OAAQ,eAAgB,IAAI,KAAK,IAAI,OAAQ,oBAAqB,MAAM,EAAI,QAAO;AAE5F,SAAO;AAER;AAEA,IAAM,kBAAkB,IAAI,QAAQ;AAIpC,IAAM,aAAN,MAAiB;AAAA,EAEhB,YAAa,OAAO,CAAC,GAAG,UAAU,CAAC,GAAI;AAEtC,SAAK,OAAO;AACZ,SAAK,aAAa,CAAC;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU;AAGf,SAAK,QAAQ,IAAI,aAAa;AAG9B,SAAK,eAAe,oBAAI,IAAI;AAG5B,SAAK,iBAAiB,CAAC;AAGvB,SAAK,YAAY,CAAC;AAGlB,SAAK,YAAY,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE;AACtC,SAAK,cAAc,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE;AACxC,SAAK,aAAa,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE;AAEvC,SAAK,cAAc,CAAC;AACpB,SAAK,eAAe,CAAC;AAGrB,SAAK,gBAAgB,CAAC;AAKtB,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,iBAAiB;AAErB,QAAK,OAAO,cAAc,aAAc;AAEvC,iBAAW,iCAAiC,KAAM,UAAU,SAAU,MAAM;AAC5E,kBAAY,UAAU,UAAU,QAAS,SAAU,IAAI;AACvD,uBAAiB,YAAY,UAAU,UAAU,MAAO,qBAAsB,EAAG,CAAE,IAAI;AAAA,IAExF;AAEA,QAAK,OAAO,sBAAsB,eAAe,YAAc,aAAa,iBAAiB,IAAO;AAEnG,WAAK,gBAAgB,IAAI,cAAe,KAAK,QAAQ,OAAQ;AAAA,IAE9D,OAAO;AAEN,WAAK,gBAAgB,IAAI,kBAAmB,KAAK,QAAQ,OAAQ;AAAA,IAElE;AAEA,SAAK,cAAc,eAAgB,KAAK,QAAQ,WAAY;AAC5D,SAAK,cAAc,iBAAkB,KAAK,QAAQ,aAAc;AAEhE,SAAK,aAAa,IAAI,WAAY,KAAK,QAAQ,OAAQ;AACvD,SAAK,WAAW,gBAAiB,aAAc;AAE/C,QAAK,KAAK,QAAQ,gBAAgB,mBAAoB;AAErD,WAAK,WAAW,mBAAoB,IAAK;AAAA,IAE1C;AAAA,EAED;AAAA,EAEA,cAAe,YAAa;AAE3B,SAAK,aAAa;AAAA,EAEnB;AAAA,EAEA,WAAY,SAAU;AAErB,SAAK,UAAU;AAAA,EAEhB;AAAA,EAEA,MAAO,QAAQ,SAAU;AAExB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AAGxB,SAAK,MAAM,UAAU;AACrB,SAAK,YAAY,CAAC;AAGlB,SAAK,WAAY,SAAW,KAAM;AAEjC,aAAO,IAAI,aAAa,IAAI,UAAU;AAAA,IAEvC,CAAE;AAEF,YAAQ,IAAK,KAAK,WAAY,SAAW,KAAM;AAE9C,aAAO,IAAI,cAAc,IAAI,WAAW;AAAA,IAEzC,CAAE,CAAE,EAAE,KAAM,WAAY;AAEvB,aAAO,QAAQ,IAAK;AAAA,QAEnB,OAAO,gBAAiB,OAAQ;AAAA,QAChC,OAAO,gBAAiB,WAAY;AAAA,QACpC,OAAO,gBAAiB,QAAS;AAAA,MAElC,CAAE;AAAA,IAEH,CAAE,EAAE,KAAM,SAAW,cAAe;AAEnC,YAAM,SAAS;AAAA,QACd,OAAO,aAAc,CAAE,EAAG,KAAK,SAAS,CAAE;AAAA,QAC1C,QAAQ,aAAc,CAAE;AAAA,QACxB,YAAY,aAAc,CAAE;AAAA,QAC5B,SAAS,aAAc,CAAE;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ;AAAA,QACA,UAAU,CAAC;AAAA,MACZ;AAEA,qCAAgC,YAAY,QAAQ,IAAK;AAEzD,6BAAwB,QAAQ,IAAK;AAErC,aAAO,QAAQ,IAAK,OAAO,WAAY,SAAW,KAAM;AAEvD,eAAO,IAAI,aAAa,IAAI,UAAW,MAAO;AAAA,MAE/C,CAAE,CAAE,EAAE,KAAM,WAAY;AAEvB,eAAQ,MAAO;AAAA,MAEhB,CAAE;AAAA,IAEH,CAAE,EAAE,MAAO,OAAQ;AAAA,EAEpB;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAEX,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,WAAW,KAAK,KAAK,UAAU,CAAC;AAItC,aAAU,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAe;AAE7F,YAAM,SAAS,SAAU,SAAU,EAAE;AAErC,eAAU,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAEnD,iBAAU,OAAQ,CAAE,CAAE,EAAE,SAAS;AAAA,MAElC;AAAA,IAED;AAIA,aAAU,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAe;AAE7F,YAAM,UAAU,SAAU,SAAU;AAEpC,UAAK,QAAQ,SAAS,QAAY;AAEjC,aAAK,YAAa,KAAK,WAAW,QAAQ,IAAK;AAK/C,YAAK,QAAQ,SAAS,QAAY;AAEjC,mBAAU,QAAQ,IAAK,EAAE,gBAAgB;AAAA,QAE1C;AAAA,MAED;AAEA,UAAK,QAAQ,WAAW,QAAY;AAEnC,aAAK,YAAa,KAAK,aAAa,QAAQ,MAAO;AAAA,MAEpD;AAAA,IAED;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAa,OAAO,OAAQ;AAE3B,QAAK,UAAU,OAAY;AAE3B,QAAK,MAAM,KAAM,KAAM,MAAM,QAAY;AAExC,YAAM,KAAM,KAAM,IAAI,MAAM,KAAM,KAAM,IAAI;AAAA,IAE7C;AAEA,UAAM,KAAM,KAAM;AAAA,EAEnB;AAAA;AAAA,EAGA,YAAa,OAAO,OAAO,QAAS;AAEnC,QAAK,MAAM,KAAM,KAAM,KAAK,EAAI,QAAO;AAEvC,UAAM,MAAM,OAAO,MAAM;AAIzB,UAAM,iBAAiB,CAAE,UAAU,UAAW;AAE7C,YAAM,WAAW,KAAK,aAAa,IAAK,QAAS;AACjD,UAAK,YAAY,MAAO;AAEvB,aAAK,aAAa,IAAK,OAAO,QAAS;AAAA,MAExC;AAEA,iBAAY,CAAE,GAAG,KAAM,KAAK,SAAS,SAAS,QAAQ,GAAI;AAEzD,uBAAgB,OAAO,MAAM,SAAU,CAAE,CAAE;AAAA,MAE5C;AAAA,IAED;AAEA,mBAAgB,QAAQ,GAAI;AAE5B,QAAI,QAAQ,eAAiB,MAAM,KAAM,KAAM;AAE/C,WAAO;AAAA,EAER;AAAA,EAEA,WAAY,MAAO;AAElB,UAAM,aAAa,OAAO,OAAQ,KAAK,OAAQ;AAC/C,eAAW,KAAM,IAAK;AAEtB,aAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,YAAM,SAAS,KAAM,WAAY,CAAE,CAAE;AAErC,UAAK,OAAS,QAAO;AAAA,IAEtB;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,WAAY,MAAO;AAElB,UAAM,aAAa,OAAO,OAAQ,KAAK,OAAQ;AAC/C,eAAW,QAAS,IAAK;AAEzB,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,YAAM,SAAS,KAAM,WAAY,CAAE,CAAE;AAErC,UAAK,OAAS,SAAQ,KAAM,MAAO;AAAA,IAEpC;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAe,MAAM,OAAQ;AAE5B,UAAM,WAAW,OAAO,MAAM;AAC9B,QAAI,aAAa,KAAK,MAAM,IAAK,QAAS;AAE1C,QAAK,CAAE,YAAa;AAEnB,cAAS,MAAO;AAAA,QAEf,KAAK;AACJ,uBAAa,KAAK,UAAW,KAAM;AACnC;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,YAAY,IAAI,SAAU,KAAM;AAAA,UAE5C,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,YAAY,IAAI,SAAU,KAAM;AAAA,UAE5C,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,aAAc,KAAM;AACtC;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,kBAAkB,IAAI,eAAgB,KAAM;AAAA,UAExD,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,KAAM;AACpC;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,gBAAgB,IAAI,aAAc,KAAM;AAAA,UAEpD,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,eAAe,IAAI,YAAa,KAAM;AAAA,UAElD,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,SAAU,KAAM;AAClC;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,IAAI,iBAAiB,IAAI,cAAe,KAAM;AAAA,UAEtD,CAAE;AACF;AAAA,QAED,KAAK;AACJ,uBAAa,KAAK,WAAY,KAAM;AACpC;AAAA,QAED;AACC,uBAAa,KAAK,WAAY,SAAW,KAAM;AAE9C,mBAAO,OAAO,QAAQ,IAAI,iBAAiB,IAAI,cAAe,MAAM,KAAM;AAAA,UAE3E,CAAE;AAEF,cAAK,CAAE,YAAa;AAEnB,kBAAM,IAAI,MAAO,mBAAmB,IAAK;AAAA,UAE1C;AAEA;AAAA,MAEF;AAEA,WAAK,MAAM,IAAK,UAAU,UAAW;AAAA,IAEtC;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAiB,MAAO;AAEvB,QAAI,eAAe,KAAK,MAAM,IAAK,IAAK;AAExC,QAAK,CAAE,cAAe;AAErB,YAAM,SAAS;AACf,YAAM,OAAO,KAAK,KAAM,QAAS,SAAS,SAAS,OAAO,IAAM,KAAK,CAAC;AAEtE,qBAAe,QAAQ,IAAK,KAAK,IAAK,SAAW,KAAK,OAAQ;AAE7D,eAAO,OAAO,cAAe,MAAM,KAAM;AAAA,MAE1C,CAAE,CAAE;AAEJ,WAAK,MAAM,IAAK,MAAM,YAAa;AAAA,IAEpC;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAY,aAAc;AAEzB,UAAM,YAAY,KAAK,KAAK,QAAS,WAAY;AACjD,UAAM,SAAS,KAAK;AAEpB,QAAK,UAAU,QAAQ,UAAU,SAAS,eAAgB;AAEzD,YAAM,IAAI,MAAO,uBAAuB,UAAU,OAAO,gCAAiC;AAAA,IAE3F;AAGA,QAAK,UAAU,QAAQ,UAAa,gBAAgB,GAAI;AAEvD,aAAO,QAAQ,QAAS,KAAK,WAAY,WAAW,eAAgB,EAAE,IAAK;AAAA,IAE5E;AAEA,UAAM,UAAU,KAAK;AAErB,WAAO,IAAI,QAAS,SAAW,SAAS,QAAS;AAEhD,aAAO,KAAM,YAAY,WAAY,UAAU,KAAK,QAAQ,IAAK,GAAG,SAAS,QAAW,WAAY;AAEnG,eAAQ,IAAI,MAAO,8CAA8C,UAAU,MAAM,IAAK,CAAE;AAAA,MAEzF,CAAE;AAAA,IAEH,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAgB,iBAAkB;AAEjC,UAAM,gBAAgB,KAAK,KAAK,YAAa,eAAgB;AAE7D,WAAO,KAAK,cAAe,UAAU,cAAc,MAAO,EAAE,KAAM,SAAW,QAAS;AAErF,YAAM,aAAa,cAAc,cAAc;AAC/C,YAAM,aAAa,cAAc,cAAc;AAC/C,aAAO,OAAO,MAAO,YAAY,aAAa,UAAW;AAAA,IAE1D,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAc,eAAgB;AAE7B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAElB,UAAM,cAAc,KAAK,KAAK,UAAW,aAAc;AAEvD,QAAK,YAAY,eAAe,UAAa,YAAY,WAAW,QAAY;AAE/E,YAAM,WAAW,iBAAkB,YAAY,IAAK;AACpD,YAAM,aAAa,sBAAuB,YAAY,aAAc;AACpE,YAAM,aAAa,YAAY,eAAe;AAE9C,YAAM,QAAQ,IAAI,WAAY,YAAY,QAAQ,QAAS;AAC3D,aAAO,QAAQ,QAAS,IAAI,gBAAiB,OAAO,UAAU,UAAW,CAAE;AAAA,IAE5E;AAEA,UAAM,qBAAqB,CAAC;AAE5B,QAAK,YAAY,eAAe,QAAY;AAE3C,yBAAmB,KAAM,KAAK,cAAe,cAAc,YAAY,UAAW,CAAE;AAAA,IAErF,OAAO;AAEN,yBAAmB,KAAM,IAAK;AAAA,IAE/B;AAEA,QAAK,YAAY,WAAW,QAAY;AAEvC,yBAAmB,KAAM,KAAK,cAAe,cAAc,YAAY,OAAO,QAAQ,UAAW,CAAE;AACnG,yBAAmB,KAAM,KAAK,cAAe,cAAc,YAAY,OAAO,OAAO,UAAW,CAAE;AAAA,IAEnG;AAEA,WAAO,QAAQ,IAAK,kBAAmB,EAAE,KAAM,SAAW,aAAc;AAEvE,YAAM,aAAa,YAAa,CAAE;AAElC,YAAM,WAAW,iBAAkB,YAAY,IAAK;AACpD,YAAM,aAAa,sBAAuB,YAAY,aAAc;AAGpE,YAAM,eAAe,WAAW;AAChC,YAAM,YAAY,eAAe;AACjC,YAAM,aAAa,YAAY,cAAc;AAC7C,YAAM,aAAa,YAAY,eAAe,SAAY,KAAK,YAAa,YAAY,UAAW,EAAE,aAAa;AAClH,YAAM,aAAa,YAAY,eAAe;AAC9C,UAAI,OAAO;AAGX,UAAK,cAAc,eAAe,WAAY;AAI7C,cAAM,UAAU,KAAK,MAAO,aAAa,UAAW;AACpD,cAAM,aAAa,uBAAuB,YAAY,aAAa,MAAM,YAAY,gBAAgB,MAAM,UAAU,MAAM,YAAY;AACvI,YAAI,KAAK,OAAO,MAAM,IAAK,UAAW;AAEtC,YAAK,CAAE,IAAK;AAEX,kBAAQ,IAAI,WAAY,YAAY,UAAU,YAAY,YAAY,QAAQ,aAAa,YAAa;AAGxG,eAAK,IAAI,kBAAmB,OAAO,aAAa,YAAa;AAE7D,iBAAO,MAAM,IAAK,YAAY,EAAG;AAAA,QAElC;AAEA,0BAAkB,IAAI,2BAA4B,IAAI,UAAY,aAAa,aAAe,cAAc,UAAW;AAAA,MAExH,OAAO;AAEN,YAAK,eAAe,MAAO;AAE1B,kBAAQ,IAAI,WAAY,YAAY,QAAQ,QAAS;AAAA,QAEtD,OAAO;AAEN,kBAAQ,IAAI,WAAY,YAAY,YAAY,YAAY,QAAQ,QAAS;AAAA,QAE9E;AAEA,0BAAkB,IAAI,gBAAiB,OAAO,UAAU,UAAW;AAAA,MAEpE;AAGA,UAAK,YAAY,WAAW,QAAY;AAEvC,cAAM,kBAAkB,iBAAiB;AACzC,cAAM,oBAAoB,sBAAuB,YAAY,OAAO,QAAQ,aAAc;AAE1F,cAAM,oBAAoB,YAAY,OAAO,QAAQ,cAAc;AACnE,cAAM,mBAAmB,YAAY,OAAO,OAAO,cAAc;AAEjE,cAAM,gBAAgB,IAAI,kBAAmB,YAAa,CAAE,GAAG,mBAAmB,YAAY,OAAO,QAAQ,eAAgB;AAC7H,cAAM,eAAe,IAAI,WAAY,YAAa,CAAE,GAAG,kBAAkB,YAAY,OAAO,QAAQ,QAAS;AAE7G,YAAK,eAAe,MAAO;AAG1B,4BAAkB,IAAI,gBAAiB,gBAAgB,MAAM,MAAM,GAAG,gBAAgB,UAAU,gBAAgB,UAAW;AAAA,QAE5H;AAEA,iBAAU,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAO;AAE1D,gBAAM,QAAQ,cAAe,CAAE;AAE/B,0BAAgB,KAAM,OAAO,aAAc,IAAI,QAAS,CAAE;AAC1D,cAAK,YAAY,EAAI,iBAAgB,KAAM,OAAO,aAAc,IAAI,WAAW,CAAE,CAAE;AACnF,cAAK,YAAY,EAAI,iBAAgB,KAAM,OAAO,aAAc,IAAI,WAAW,CAAE,CAAE;AACnF,cAAK,YAAY,EAAI,iBAAgB,KAAM,OAAO,aAAc,IAAI,WAAW,CAAE,CAAE;AACnF,cAAK,YAAY,EAAI,OAAM,IAAI,MAAO,mEAAoE;AAAA,QAE3G;AAAA,MAED;AAEA,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAa,cAAe;AAE3B,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK,SAAU,YAAa;AAC/C,UAAM,cAAc,WAAW;AAC/B,UAAM,YAAY,KAAK,OAAQ,WAAY;AAE3C,QAAI,SAAS,KAAK;AAElB,QAAK,UAAU,KAAM;AAEpB,YAAM,UAAU,QAAQ,QAAQ,WAAY,UAAU,GAAI;AAC1D,UAAK,YAAY,KAAO,UAAS;AAAA,IAElC;AAEA,WAAO,KAAK,iBAAkB,cAAc,aAAa,MAAO;AAAA,EAEjE;AAAA,EAEA,iBAAkB,cAAc,aAAa,QAAS;AAErD,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAElB,UAAM,aAAa,KAAK,SAAU,YAAa;AAC/C,UAAM,YAAY,KAAK,OAAQ,WAAY;AAE3C,UAAM,YAAa,UAAU,OAAO,UAAU,cAAe,MAAM,WAAW;AAE9E,QAAK,KAAK,aAAc,QAAS,GAAI;AAGpC,aAAO,KAAK,aAAc,QAAS;AAAA,IAEpC;AAEA,UAAM,UAAU,KAAK,gBAAiB,aAAa,MAAO,EAAE,KAAM,SAAW,SAAU;AAEtF,cAAQ,QAAQ;AAEhB,cAAQ,OAAO,WAAW,QAAQ,UAAU,QAAQ;AAEpD,UAAK,QAAQ,SAAS,MAAM,OAAO,UAAU,QAAQ,YAAY,UAAU,IAAI,WAAY,aAAc,MAAM,OAAQ;AAEtH,gBAAQ,OAAO,UAAU;AAAA,MAE1B;AAEA,YAAM,WAAW,KAAK,YAAY,CAAC;AACnC,YAAM,UAAU,SAAU,WAAW,OAAQ,KAAK,CAAC;AAEnD,cAAQ,YAAY,cAAe,QAAQ,SAAU,KAAK;AAC1D,cAAQ,YAAY,cAAe,QAAQ,SAAU,KAAK;AAC1D,cAAQ,QAAQ,gBAAiB,QAAQ,KAAM,KAAK;AACpD,cAAQ,QAAQ,gBAAiB,QAAQ,KAAM,KAAK;AAEpD,aAAO,aAAa,IAAK,SAAS,EAAE,UAAU,aAAa,CAAE;AAE7D,aAAO;AAAA,IAER,CAAE,EAAE,MAAO,WAAY;AAEtB,aAAO;AAAA,IAER,CAAE;AAEF,SAAK,aAAc,QAAS,IAAI;AAEhC,WAAO;AAAA,EAER;AAAA,EAEA,gBAAiB,aAAa,QAAS;AAEtC,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,QAAK,KAAK,YAAa,WAAY,MAAM,QAAY;AAEpD,aAAO,KAAK,YAAa,WAAY,EAAE,KAAM,CAAE,YAAa,QAAQ,MAAM,CAAE;AAAA,IAE7E;AAEA,UAAM,YAAY,KAAK,OAAQ,WAAY;AAE3C,UAAM,MAAM,KAAK,OAAO,KAAK;AAE7B,QAAI,YAAY,UAAU,OAAO;AACjC,QAAI,cAAc;AAElB,QAAK,UAAU,eAAe,QAAY;AAIzC,kBAAY,OAAO,cAAe,cAAc,UAAU,UAAW,EAAE,KAAM,SAAW,YAAa;AAEpG,sBAAc;AACd,cAAM,OAAO,IAAI,KAAM,CAAE,UAAW,GAAG,EAAE,MAAM,UAAU,SAAS,CAAE;AACpE,oBAAY,IAAI,gBAAiB,IAAK;AACtC,eAAO;AAAA,MAER,CAAE;AAAA,IAEH,WAAY,UAAU,QAAQ,QAAY;AAEzC,YAAM,IAAI,MAAO,6BAA6B,cAAc,gCAAiC;AAAA,IAE9F;AAEA,UAAM,UAAU,QAAQ,QAAS,SAAU,EAAE,KAAM,SAAWC,YAAY;AAEzE,aAAO,IAAI,QAAS,SAAW,SAAS,QAAS;AAEhD,YAAI,SAAS;AAEb,YAAK,OAAO,wBAAwB,MAAO;AAE1C,mBAAS,SAAW,aAAc;AAEjC,kBAAM,UAAU,IAAI,QAAS,WAAY;AACzC,oBAAQ,cAAc;AAEtB,oBAAS,OAAQ;AAAA,UAElB;AAAA,QAED;AAEA,eAAO,KAAM,YAAY,WAAYA,YAAW,QAAQ,IAAK,GAAG,QAAQ,QAAW,MAAO;AAAA,MAE3F,CAAE;AAAA,IAEH,CAAE,EAAE,KAAM,SAAW,SAAU;AAI9B,UAAK,gBAAgB,MAAO;AAE3B,YAAI,gBAAiB,SAAU;AAAA,MAEhC;AAEA,cAAQ,SAAS,WAAW,UAAU,YAAY,oBAAqB,UAAU,GAAI;AAErF,aAAO;AAAA,IAER,CAAE,EAAE,MAAO,SAAW,OAAQ;AAE7B,cAAQ,MAAO,2CAA4C,SAAU;AACrE,YAAM;AAAA,IAEP,CAAE;AAEF,SAAK,YAAa,WAAY,IAAI;AAClC,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAe,gBAAgB,SAAS,QAAQ,YAAa;AAE5D,UAAM,SAAS;AAEf,WAAO,KAAK,cAAe,WAAW,OAAO,KAAM,EAAE,KAAM,SAAW,SAAU;AAE/E,UAAK,CAAE,QAAU,QAAO;AAExB,UAAK,OAAO,aAAa,UAAa,OAAO,WAAW,GAAI;AAE3D,kBAAU,QAAQ,MAAM;AACxB,gBAAQ,UAAU,OAAO;AAAA,MAE1B;AAEA,UAAK,OAAO,WAAY,WAAW,qBAAsB,GAAI;AAE5D,cAAM,YAAY,OAAO,eAAe,SAAY,OAAO,WAAY,WAAW,qBAAsB,IAAI;AAE5G,YAAK,WAAY;AAEhB,gBAAM,gBAAgB,OAAO,aAAa,IAAK,OAAQ;AACvD,oBAAU,OAAO,WAAY,WAAW,qBAAsB,EAAE,cAAe,SAAS,SAAU;AAClG,iBAAO,aAAa,IAAK,SAAS,aAAc;AAAA,QAEjD;AAAA,MAED;AAEA,UAAK,eAAe,QAAY;AAE/B,gBAAQ,aAAa;AAAA,MAEtB;AAEA,qBAAgB,OAAQ,IAAI;AAE5B,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,oBAAqB,MAAO;AAE3B,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW,KAAK;AAEpB,UAAM,wBAAwB,SAAS,WAAW,YAAY;AAC9D,UAAM,kBAAkB,SAAS,WAAW,UAAU;AACtD,UAAM,iBAAiB,SAAS,WAAW,WAAW;AAEtD,QAAK,KAAK,UAAW;AAEpB,YAAM,WAAW,oBAAoB,SAAS;AAE9C,UAAI,iBAAiB,KAAK,MAAM,IAAK,QAAS;AAE9C,UAAK,CAAE,gBAAiB;AAEvB,yBAAiB,IAAI,eAAe;AACpC,iBAAS,UAAU,KAAK,KAAM,gBAAgB,QAAS;AACvD,uBAAe,MAAM,KAAM,SAAS,KAAM;AAC1C,uBAAe,MAAM,SAAS;AAC9B,uBAAe,kBAAkB;AAEjC,aAAK,MAAM,IAAK,UAAU,cAAe;AAAA,MAE1C;AAEA,iBAAW;AAAA,IAEZ,WAAY,KAAK,QAAS;AAEzB,YAAM,WAAW,uBAAuB,SAAS;AAEjD,UAAI,eAAe,KAAK,MAAM,IAAK,QAAS;AAE5C,UAAK,CAAE,cAAe;AAErB,uBAAe,IAAI,kBAAkB;AACrC,iBAAS,UAAU,KAAK,KAAM,cAAc,QAAS;AACrD,qBAAa,MAAM,KAAM,SAAS,KAAM;AACxC,qBAAa,MAAM,SAAS;AAE5B,aAAK,MAAM,IAAK,UAAU,YAAa;AAAA,MAExC;AAEA,iBAAW;AAAA,IAEZ;AAGA,QAAK,yBAAyB,mBAAmB,gBAAiB;AAEjE,UAAI,WAAW,oBAAoB,SAAS,OAAO;AAEnD,UAAK,sBAAwB,aAAY;AACzC,UAAK,gBAAkB,aAAY;AACnC,UAAK,eAAiB,aAAY;AAElC,UAAI,iBAAiB,KAAK,MAAM,IAAK,QAAS;AAE9C,UAAK,CAAE,gBAAiB;AAEvB,yBAAiB,SAAS,MAAM;AAEhC,YAAK,gBAAkB,gBAAe,eAAe;AACrD,YAAK,eAAiB,gBAAe,cAAc;AAEnD,YAAK,uBAAwB;AAG5B,cAAK,eAAe,YAAc,gBAAe,YAAY,KAAK;AAClE,cAAK,eAAe,qBAAuB,gBAAe,qBAAqB,KAAK;AAAA,QAErF;AAEA,aAAK,MAAM,IAAK,UAAU,cAAe;AAEzC,aAAK,aAAa,IAAK,gBAAgB,KAAK,aAAa,IAAK,QAAS,CAAE;AAAA,MAE1E;AAEA,iBAAW;AAAA,IAEZ;AAEA,SAAK,WAAW;AAAA,EAEjB;AAAA,EAEA,kBAAuC;AAEtC,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAc,eAAgB;AAE7B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK,UAAW,aAAc;AAElD,QAAI;AACJ,UAAM,iBAAiB,CAAC;AACxB,UAAM,qBAAqB,YAAY,cAAc,CAAC;AAEtD,UAAM,UAAU,CAAC;AAEjB,QAAK,mBAAoB,WAAW,mBAAoB,GAAI;AAE3D,YAAM,eAAe,WAAY,WAAW,mBAAoB;AAChE,qBAAe,aAAa,gBAAgB;AAC5C,cAAQ,KAAM,aAAa,aAAc,gBAAgB,aAAa,MAAO,CAAE;AAAA,IAEhF,OAAO;AAKN,YAAM,oBAAoB,YAAY,wBAAwB,CAAC;AAE/D,qBAAe,QAAQ,IAAI,MAAO,GAAK,GAAK,CAAI;AAChD,qBAAe,UAAU;AAEzB,UAAK,MAAM,QAAS,kBAAkB,eAAgB,GAAI;AAEzD,cAAM,QAAQ,kBAAkB;AAEhC,uBAAe,MAAM,OAAQ,MAAO,CAAE,GAAG,MAAO,CAAE,GAAG,MAAO,CAAE,GAAG,oBAAqB;AACtF,uBAAe,UAAU,MAAO,CAAE;AAAA,MAEnC;AAEA,UAAK,kBAAkB,qBAAqB,QAAY;AAEvD,gBAAQ,KAAM,OAAO,cAAe,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAe,CAAE;AAAA,MAEjH;AAEA,qBAAe,YAAY,kBAAkB,mBAAmB,SAAY,kBAAkB,iBAAiB;AAC/G,qBAAe,YAAY,kBAAkB,oBAAoB,SAAY,kBAAkB,kBAAkB;AAEjH,UAAK,kBAAkB,6BAA6B,QAAY;AAE/D,gBAAQ,KAAM,OAAO,cAAe,gBAAgB,gBAAgB,kBAAkB,wBAAyB,CAAE;AACjH,gBAAQ,KAAM,OAAO,cAAe,gBAAgB,gBAAgB,kBAAkB,wBAAyB,CAAE;AAAA,MAElH;AAEA,qBAAe,KAAK,WAAY,SAAW,KAAM;AAEhD,eAAO,IAAI,mBAAmB,IAAI,gBAAiB,aAAc;AAAA,MAElE,CAAE;AAEF,cAAQ,KAAM,QAAQ,IAAK,KAAK,WAAY,SAAW,KAAM;AAE5D,eAAO,IAAI,wBAAwB,IAAI,qBAAsB,eAAe,cAAe;AAAA,MAE5F,CAAE,CAAE,CAAE;AAAA,IAEP;AAEA,QAAK,YAAY,gBAAgB,MAAO;AAEvC,qBAAe,OAAO;AAAA,IAEvB;AAEA,UAAM,YAAY,YAAY,aAAa,YAAY;AAEvD,QAAK,cAAc,YAAY,OAAQ;AAEtC,qBAAe,cAAc;AAG7B,qBAAe,aAAa;AAAA,IAE7B,OAAO;AAEN,qBAAe,cAAc;AAE7B,UAAK,cAAc,YAAY,MAAO;AAErC,uBAAe,YAAY,YAAY,gBAAgB,SAAY,YAAY,cAAc;AAAA,MAE9F;AAAA,IAED;AAEA,QAAK,YAAY,kBAAkB,UAAa,iBAAiB,mBAAoB;AAEpF,cAAQ,KAAM,OAAO,cAAe,gBAAgB,aAAa,YAAY,aAAc,CAAE;AAE7F,qBAAe,cAAc,IAAI,QAAS,GAAG,CAAE;AAE/C,UAAK,YAAY,cAAc,UAAU,QAAY;AAEpD,cAAM,QAAQ,YAAY,cAAc;AAExC,uBAAe,YAAY,IAAK,OAAO,KAAM;AAAA,MAE9C;AAAA,IAED;AAEA,QAAK,YAAY,qBAAqB,UAAa,iBAAiB,mBAAoB;AAEvF,cAAQ,KAAM,OAAO,cAAe,gBAAgB,SAAS,YAAY,gBAAiB,CAAE;AAE5F,UAAK,YAAY,iBAAiB,aAAa,QAAY;AAE1D,uBAAe,iBAAiB,YAAY,iBAAiB;AAAA,MAE9D;AAAA,IAED;AAEA,QAAK,YAAY,mBAAmB,UAAa,iBAAiB,mBAAoB;AAErF,YAAM,iBAAiB,YAAY;AACnC,qBAAe,WAAW,IAAI,MAAM,EAAE,OAAQ,eAAgB,CAAE,GAAG,eAAgB,CAAE,GAAG,eAAgB,CAAE,GAAG,oBAAqB;AAAA,IAEnI;AAEA,QAAK,YAAY,oBAAoB,UAAa,iBAAiB,mBAAoB;AAEtF,cAAQ,KAAM,OAAO,cAAe,gBAAgB,eAAe,YAAY,iBAAiB,cAAe,CAAE;AAAA,IAElH;AAEA,WAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,WAAY;AAE/C,YAAM,WAAW,IAAI,aAAc,cAAe;AAElD,UAAK,YAAY,KAAO,UAAS,OAAO,YAAY;AAEpD,6BAAwB,UAAU,WAAY;AAE9C,aAAO,aAAa,IAAK,UAAU,EAAE,WAAW,cAAc,CAAE;AAEhE,UAAK,YAAY,WAAa,gCAAgC,YAAY,UAAU,WAAY;AAEhG,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA,EAGA,iBAAkB,cAAe;AAEhC,UAAM,gBAAgB,gBAAgB,iBAAkB,gBAAgB,EAAG;AAE3E,QAAK,iBAAiB,KAAK,eAAgB;AAE1C,aAAO,gBAAgB,MAAQ,EAAG,KAAK,cAAe,aAAc;AAAA,IAErE,OAAO;AAEN,WAAK,cAAe,aAAc,IAAI;AAEtC,aAAO;AAAA,IAER;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAgB,YAAa;AAE5B,UAAM,SAAS;AACf,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ,KAAK;AAEnB,aAAS,qBAAsB,WAAY;AAE1C,aAAO,WAAY,WAAW,0BAA2B,EACvD,gBAAiB,WAAW,MAAO,EACnC,KAAM,SAAW,UAAW;AAE5B,eAAO,uBAAwB,UAAU,WAAW,MAAO;AAAA,MAE5D,CAAE;AAAA,IAEJ;AAEA,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,YAAM,YAAY,WAAY,CAAE;AAChC,YAAM,WAAW,mBAAoB,SAAU;AAG/C,YAAM,SAAS,MAAO,QAAS;AAE/B,UAAK,QAAS;AAGb,gBAAQ,KAAM,OAAO,OAAQ;AAAA,MAE9B,OAAO;AAEN,YAAI;AAEJ,YAAK,UAAU,cAAc,UAAU,WAAY,WAAW,0BAA2B,GAAI;AAG5F,4BAAkB,qBAAsB,SAAU;AAAA,QAEnD,OAAO;AAGN,4BAAkB,uBAAwB,IAAI,eAAe,GAAG,WAAW,MAAO;AAAA,QAEnF;AAGA,cAAO,QAAS,IAAI,EAAE,WAAsB,SAAS,gBAAgB;AAErE,gBAAQ,KAAM,eAAgB;AAAA,MAE/B;AAAA,IAED;AAEA,WAAO,QAAQ,IAAK,OAAQ;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAU,WAAY;AAErB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AAExB,UAAM,UAAU,KAAK,OAAQ,SAAU;AACvC,UAAM,aAAa,QAAQ;AAE3B,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,YAAM,WAAW,WAAY,CAAE,EAAE,aAAa,SAC3C,sBAAuB,KAAK,KAAM,IAClC,KAAK,cAAe,YAAY,WAAY,CAAE,EAAE,QAAS;AAE5D,cAAQ,KAAM,QAAS;AAAA,IAExB;AAEA,YAAQ,KAAM,OAAO,eAAgB,UAAW,CAAE;AAElD,WAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,SAAW,SAAU;AAExD,YAAM,YAAY,QAAQ,MAAO,GAAG,QAAQ,SAAS,CAAE;AACvD,YAAM,aAAa,QAAS,QAAQ,SAAS,CAAE;AAE/C,YAAM,SAAS,CAAC;AAEhB,eAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,cAAM,WAAW,WAAY,CAAE;AAC/B,cAAM,YAAY,WAAY,CAAE;AAIhC,YAAI;AAEJ,cAAM,WAAW,UAAW,CAAE;AAE9B,YAAK,UAAU,SAAS,gBAAgB,aACtC,UAAU,SAAS,gBAAgB,kBACnC,UAAU,SAAS,gBAAgB,gBACnC,UAAU,SAAS,QAAY;AAGhC,iBAAO,QAAQ,kBAAkB,OAC9B,IAAI,YAAa,UAAU,QAAS,IACpC,IAAI,KAAM,UAAU,QAAS;AAEhC,cAAK,KAAK,kBAAkB,MAAO;AAGlC,iBAAK,qBAAqB;AAAA,UAE3B;AAEA,cAAK,UAAU,SAAS,gBAAgB,gBAAiB;AAExD,iBAAK,WAAW,oBAAqB,KAAK,UAAU,qBAAsB;AAAA,UAE3E,WAAY,UAAU,SAAS,gBAAgB,cAAe;AAE7D,iBAAK,WAAW,oBAAqB,KAAK,UAAU,mBAAoB;AAAA,UAEzE;AAAA,QAED,WAAY,UAAU,SAAS,gBAAgB,OAAQ;AAEtD,iBAAO,IAAI,aAAc,UAAU,QAAS;AAAA,QAE7C,WAAY,UAAU,SAAS,gBAAgB,YAAa;AAE3D,iBAAO,IAAI,KAAM,UAAU,QAAS;AAAA,QAErC,WAAY,UAAU,SAAS,gBAAgB,WAAY;AAE1D,iBAAO,IAAI,SAAU,UAAU,QAAS;AAAA,QAEzC,WAAY,UAAU,SAAS,gBAAgB,QAAS;AAEvD,iBAAO,IAAI,OAAQ,UAAU,QAAS;AAAA,QAEvC,OAAO;AAEN,gBAAM,IAAI,MAAO,mDAAmD,UAAU,IAAK;AAAA,QAEpF;AAEA,YAAK,OAAO,KAAM,KAAK,SAAS,eAAgB,EAAE,SAAS,GAAI;AAE9D,6BAAoB,MAAM,OAAQ;AAAA,QAEnC;AAEA,aAAK,OAAO,OAAO,iBAAkB,QAAQ,QAAU,UAAU,SAAY;AAE7E,+BAAwB,MAAM,OAAQ;AAEtC,YAAK,UAAU,WAAa,gCAAgC,YAAY,MAAM,SAAU;AAExF,eAAO,oBAAqB,IAAK;AAEjC,eAAO,KAAM,IAAK;AAAA,MAEnB;AAEA,eAAU,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAEnD,eAAO,aAAa,IAAK,OAAQ,CAAE,GAAG;AAAA,UACrC,QAAQ;AAAA,UACR,YAAY;AAAA,QACb,CAAE;AAAA,MAEH;AAEA,UAAK,OAAO,WAAW,GAAI;AAE1B,YAAK,QAAQ,WAAa,gCAAgC,YAAY,OAAQ,CAAE,GAAG,OAAQ;AAE3F,eAAO,OAAQ,CAAE;AAAA,MAElB;AAEA,YAAM,QAAQ,IAAI,MAAM;AAExB,UAAK,QAAQ,WAAa,gCAAgC,YAAY,OAAO,OAAQ;AAErF,aAAO,aAAa,IAAK,OAAO,EAAE,QAAQ,UAAU,CAAE;AAEtD,eAAU,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAEnD,cAAM,IAAK,OAAQ,CAAE,CAAE;AAAA,MAExB;AAEA,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAY,aAAc;AAEzB,QAAI;AACJ,UAAM,YAAY,KAAK,KAAK,QAAS,WAAY;AACjD,UAAM,SAAS,UAAW,UAAU,IAAK;AAEzC,QAAK,CAAE,QAAS;AAEf,cAAQ,KAAM,8CAA+C;AAC7D;AAAA,IAED;AAEA,QAAK,UAAU,SAAS,eAAgB;AAEvC,eAAS,IAAI,kBAAmB,UAAU,SAAU,OAAO,IAAK,GAAG,OAAO,eAAe,GAAG,OAAO,SAAS,GAAG,OAAO,QAAQ,GAAI;AAAA,IAEnI,WAAY,UAAU,SAAS,gBAAiB;AAE/C,eAAS,IAAI,mBAAoB,CAAE,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,CAAE,OAAO,MAAM,OAAO,OAAO,OAAO,IAAK;AAAA,IAEpH;AAEA,QAAK,UAAU,KAAO,QAAO,OAAO,KAAK,iBAAkB,UAAU,IAAK;AAE1E,2BAAwB,QAAQ,SAAU;AAE1C,WAAO,QAAQ,QAAS,MAAO;AAAA,EAEhC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAU,WAAY;AAErB,UAAM,UAAU,KAAK,KAAK,MAAO,SAAU;AAE3C,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,KAAK,QAAQ,OAAO,QAAQ,IAAI,IAAI,KAAO;AAE3D,cAAQ,KAAM,KAAK,iBAAkB,QAAQ,OAAQ,CAAE,CAAE,CAAE;AAAA,IAE5D;AAEA,QAAK,QAAQ,wBAAwB,QAAY;AAEhD,cAAQ,KAAM,KAAK,cAAe,YAAY,QAAQ,mBAAoB,CAAE;AAAA,IAE7E,OAAO;AAEN,cAAQ,KAAM,IAAK;AAAA,IAEpB;AAEA,WAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,SAAW,SAAU;AAExD,YAAM,sBAAsB,QAAQ,IAAI;AACxC,YAAM,aAAa;AAKnB,YAAM,QAAQ,CAAC;AACf,YAAM,eAAe,CAAC;AAEtB,eAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,cAAM,YAAY,WAAY,CAAE;AAEhC,YAAK,WAAY;AAEhB,gBAAM,KAAM,SAAU;AAEtB,gBAAM,MAAM,IAAI,QAAQ;AAExB,cAAK,wBAAwB,MAAO;AAEnC,gBAAI,UAAW,oBAAoB,OAAO,IAAI,EAAG;AAAA,UAElD;AAEA,uBAAa,KAAM,GAAI;AAAA,QAExB,OAAO;AAEN,kBAAQ,KAAM,oDAAoD,QAAQ,OAAQ,CAAE,CAAE;AAAA,QAEvF;AAAA,MAED;AAEA,aAAO,IAAI,SAAU,OAAO,YAAa;AAAA,IAE1C,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAe,gBAAiB;AAE/B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AAEf,UAAM,eAAe,KAAK,WAAY,cAAe;AACrD,UAAM,gBAAgB,aAAa,OAAO,aAAa,OAAO,eAAe;AAE7E,UAAM,eAAe,CAAC;AACtB,UAAM,wBAAwB,CAAC;AAC/B,UAAM,yBAAyB,CAAC;AAChC,UAAM,kBAAkB,CAAC;AACzB,UAAM,iBAAiB,CAAC;AAExB,aAAU,IAAI,GAAG,KAAK,aAAa,SAAS,QAAQ,IAAI,IAAI,KAAO;AAElE,YAAM,UAAU,aAAa,SAAU,CAAE;AACzC,YAAM,UAAU,aAAa,SAAU,QAAQ,OAAQ;AACvD,YAAM,SAAS,QAAQ;AACvB,YAAM,OAAO,OAAO;AACpB,YAAM,QAAQ,aAAa,eAAe,SAAY,aAAa,WAAY,QAAQ,KAAM,IAAI,QAAQ;AACzG,YAAM,SAAS,aAAa,eAAe,SAAY,aAAa,WAAY,QAAQ,MAAO,IAAI,QAAQ;AAE3G,UAAK,OAAO,SAAS,OAAY;AAEjC,mBAAa,KAAM,KAAK,cAAe,QAAQ,IAAK,CAAE;AACtD,4BAAsB,KAAM,KAAK,cAAe,YAAY,KAAM,CAAE;AACpE,6BAAuB,KAAM,KAAK,cAAe,YAAY,MAAO,CAAE;AACtE,sBAAgB,KAAM,OAAQ;AAC9B,qBAAe,KAAM,MAAO;AAAA,IAE7B;AAEA,WAAO,QAAQ,IAAK;AAAA,MAEnB,QAAQ,IAAK,YAAa;AAAA,MAC1B,QAAQ,IAAK,qBAAsB;AAAA,MACnC,QAAQ,IAAK,sBAAuB;AAAA,MACpC,QAAQ,IAAK,eAAgB;AAAA,MAC7B,QAAQ,IAAK,cAAe;AAAA,IAE7B,CAAE,EAAE,KAAM,SAAW,cAAe;AAEnC,YAAM,QAAQ,aAAc,CAAE;AAC9B,YAAM,iBAAiB,aAAc,CAAE;AACvC,YAAM,kBAAkB,aAAc,CAAE;AACxC,YAAM,WAAW,aAAc,CAAE;AACjC,YAAM,UAAU,aAAc,CAAE;AAEhC,YAAM,SAAS,CAAC;AAEhB,eAAU,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAO;AAElD,cAAM,OAAO,MAAO,CAAE;AACtB,cAAM,gBAAgB,eAAgB,CAAE;AACxC,cAAM,iBAAiB,gBAAiB,CAAE;AAC1C,cAAM,UAAU,SAAU,CAAE;AAC5B,cAAM,SAAS,QAAS,CAAE;AAE1B,YAAK,SAAS,OAAY;AAE1B,YAAK,KAAK,cAAe;AAExB,eAAK,aAAa;AAAA,QAEnB;AAEA,cAAM,gBAAgB,OAAO,uBAAwB,MAAM,eAAe,gBAAgB,SAAS,MAAO;AAE1G,YAAK,eAAgB;AAEpB,mBAAU,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAO;AAEjD,mBAAO,KAAM,cAAe,CAAE,CAAE;AAAA,UAEjC;AAAA,QAED;AAAA,MAED;AAEA,aAAO,IAAI,cAAe,eAAe,QAAW,MAAO;AAAA,IAE5D,CAAE;AAAA,EAEH;AAAA,EAEA,eAAgB,WAAY;AAE3B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,KAAK,MAAO,SAAU;AAEtC,QAAK,QAAQ,SAAS,OAAY,QAAO;AAEzC,WAAO,OAAO,cAAe,QAAQ,QAAQ,IAAK,EAAE,KAAM,SAAW,MAAO;AAE3E,YAAM,OAAO,OAAO,YAAa,OAAO,WAAW,QAAQ,MAAM,IAAK;AAGtE,UAAK,QAAQ,YAAY,QAAY;AAEpC,aAAK,SAAU,SAAW,GAAI;AAE7B,cAAK,CAAE,EAAE,OAAS;AAElB,mBAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAE5D,cAAE,sBAAuB,CAAE,IAAI,QAAQ,QAAS,CAAE;AAAA,UAEnD;AAAA,QAED,CAAE;AAAA,MAEH;AAEA,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAU,WAAY;AAErB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AAEf,UAAM,UAAU,KAAK,MAAO,SAAU;AAEtC,UAAM,cAAc,OAAO,iBAAkB,SAAU;AAEvD,UAAM,eAAe,CAAC;AACtB,UAAM,cAAc,QAAQ,YAAY,CAAC;AAEzC,aAAU,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAO;AAExD,mBAAa,KAAM,OAAO,cAAe,QAAQ,YAAa,CAAE,CAAE,CAAE;AAAA,IAErE;AAEA,UAAM,kBAAkB,QAAQ,SAAS,SACtC,QAAQ,QAAS,IAAK,IACtB,OAAO,cAAe,QAAQ,QAAQ,IAAK;AAE9C,WAAO,QAAQ,IAAK;AAAA,MACnB;AAAA,MACA,QAAQ,IAAK,YAAa;AAAA,MAC1B;AAAA,IACD,CAAE,EAAE,KAAM,SAAW,SAAU;AAE9B,YAAM,OAAO,QAAS,CAAE;AACxB,YAAM,WAAW,QAAS,CAAE;AAC5B,YAAM,WAAW,QAAS,CAAE;AAE5B,UAAK,aAAa,MAAO;AAIxB,aAAK,SAAU,SAAW,MAAO;AAEhC,cAAK,CAAE,KAAK,cAAgB;AAE5B,eAAK,KAAM,UAAU,eAAgB;AAAA,QAEtC,CAAE;AAAA,MAEH;AAEA,eAAU,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAO;AAErD,aAAK,IAAK,SAAU,CAAE,CAAE;AAAA,MAEzB;AAEA,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA;AAAA;AAAA,EAIA,iBAAkB,WAAY;AAE7B,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS;AAKf,QAAK,KAAK,UAAW,SAAU,MAAM,QAAY;AAEhD,aAAO,KAAK,UAAW,SAAU;AAAA,IAElC;AAEA,UAAM,UAAU,KAAK,MAAO,SAAU;AAGtC,UAAM,WAAW,QAAQ,OAAO,OAAO,iBAAkB,QAAQ,IAAK,IAAI;AAE1E,UAAM,UAAU,CAAC;AAEjB,UAAM,cAAc,OAAO,WAAY,SAAW,KAAM;AAEvD,aAAO,IAAI,kBAAkB,IAAI,eAAgB,SAAU;AAAA,IAE5D,CAAE;AAEF,QAAK,aAAc;AAElB,cAAQ,KAAM,WAAY;AAAA,IAE3B;AAEA,QAAK,QAAQ,WAAW,QAAY;AAEnC,cAAQ,KAAM,OAAO,cAAe,UAAU,QAAQ,MAAO,EAAE,KAAM,SAAW,QAAS;AAExF,eAAO,OAAO,YAAa,OAAO,aAAa,QAAQ,QAAQ,MAAO;AAAA,MAEvE,CAAE,CAAE;AAAA,IAEL;AAEA,WAAO,WAAY,SAAW,KAAM;AAEnC,aAAO,IAAI,wBAAwB,IAAI,qBAAsB,SAAU;AAAA,IAExE,CAAE,EAAE,QAAS,SAAW,SAAU;AAEjC,cAAQ,KAAM,OAAQ;AAAA,IAEvB,CAAE;AAEF,SAAK,UAAW,SAAU,IAAI,QAAQ,IAAK,OAAQ,EAAE,KAAM,SAAW,SAAU;AAE/E,UAAI;AAGJ,UAAK,QAAQ,WAAW,MAAO;AAE9B,eAAO,IAAI,KAAK;AAAA,MAEjB,WAAY,QAAQ,SAAS,GAAI;AAEhC,eAAO,IAAI,MAAM;AAAA,MAElB,WAAY,QAAQ,WAAW,GAAI;AAElC,eAAO,QAAS,CAAE;AAAA,MAEnB,OAAO;AAEN,eAAO,IAAI,SAAS;AAAA,MAErB;AAEA,UAAK,SAAS,QAAS,CAAE,GAAI;AAE5B,iBAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEpD,eAAK,IAAK,QAAS,CAAE,CAAE;AAAA,QAExB;AAAA,MAED;AAEA,UAAK,QAAQ,MAAO;AAEnB,aAAK,SAAS,OAAO,QAAQ;AAC7B,aAAK,OAAO;AAAA,MAEb;AAEA,6BAAwB,MAAM,OAAQ;AAEtC,UAAK,QAAQ,WAAa,gCAAgC,YAAY,MAAM,OAAQ;AAEpF,UAAK,QAAQ,WAAW,QAAY;AAEnC,cAAM,SAAS,IAAI,QAAQ;AAC3B,eAAO,UAAW,QAAQ,MAAO;AACjC,aAAK,aAAc,MAAO;AAAA,MAE3B,OAAO;AAEN,YAAK,QAAQ,gBAAgB,QAAY;AAExC,eAAK,SAAS,UAAW,QAAQ,WAAY;AAAA,QAE9C;AAEA,YAAK,QAAQ,aAAa,QAAY;AAErC,eAAK,WAAW,UAAW,QAAQ,QAAS;AAAA,QAE7C;AAEA,YAAK,QAAQ,UAAU,QAAY;AAElC,eAAK,MAAM,UAAW,QAAQ,KAAM;AAAA,QAErC;AAAA,MAED;AAEA,UAAK,CAAE,OAAO,aAAa,IAAK,IAAK,GAAI;AAExC,eAAO,aAAa,IAAK,MAAM,CAAC,CAAE;AAAA,MAEnC;AAEA,aAAO,aAAa,IAAK,IAAK,EAAE,QAAQ;AAExC,aAAO;AAAA,IAER,CAAE;AAEF,WAAO,KAAK,UAAW,SAAU;AAAA,EAElC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAW,YAAa;AAEvB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK,KAAK,OAAQ,UAAW;AAC9C,UAAM,SAAS;AAIf,UAAM,QAAQ,IAAI,MAAM;AACxB,QAAK,SAAS,KAAO,OAAM,OAAO,OAAO,iBAAkB,SAAS,IAAK;AAEzE,2BAAwB,OAAO,QAAS;AAExC,QAAK,SAAS,WAAa,gCAAgC,YAAY,OAAO,QAAS;AAEvF,UAAM,UAAU,SAAS,SAAS,CAAC;AAEnC,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEpD,cAAQ,KAAM,OAAO,cAAe,QAAQ,QAAS,CAAE,CAAE,CAAE;AAAA,IAE5D;AAEA,WAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,SAAW,OAAQ;AAEtD,eAAU,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAO;AAElD,cAAM,IAAK,MAAO,CAAE,CAAE;AAAA,MAEvB;AAIA,YAAM,qBAAqB,CAAE,SAAU;AAEtC,cAAM,sBAAsB,oBAAI,IAAI;AAEpC,mBAAY,CAAE,KAAK,KAAM,KAAK,OAAO,cAAe;AAEnD,cAAK,eAAe,YAAY,eAAe,SAAU;AAExD,gCAAoB,IAAK,KAAK,KAAM;AAAA,UAErC;AAAA,QAED;AAEA,aAAK,SAAU,CAAEC,UAAU;AAE1B,gBAAM,WAAW,OAAO,aAAa,IAAKA,KAAK;AAE/C,cAAK,YAAY,MAAO;AAEvB,gCAAoB,IAAKA,OAAM,QAAS;AAAA,UAEzC;AAAA,QAED,CAAE;AAEF,eAAO;AAAA,MAER;AAEA,aAAO,eAAe,mBAAoB,KAAM;AAEhD,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA,EAEA,uBAAwB,MAAM,eAAe,gBAAgB,SAAS,QAAS;AAE9E,UAAM,SAAS,CAAC;AAEhB,UAAM,aAAa,KAAK,OAAO,KAAK,OAAO,KAAK;AAChD,UAAM,cAAc,CAAC;AAErB,QAAK,gBAAiB,OAAO,IAAK,MAAM,gBAAgB,SAAU;AAEjE,WAAK,SAAU,SAAW,QAAS;AAElC,YAAK,OAAO,uBAAwB;AAEnC,sBAAY,KAAM,OAAO,OAAO,OAAO,OAAO,OAAO,IAAK;AAAA,QAE3D;AAAA,MAED,CAAE;AAAA,IAEH,OAAO;AAEN,kBAAY,KAAM,UAAW;AAAA,IAE9B;AAEA,QAAI;AAEJ,YAAS,gBAAiB,OAAO,IAAK,GAAI;AAAA,MAEzC,KAAK,gBAAgB;AAEpB,6BAAqB;AACrB;AAAA,MAED,KAAK,gBAAgB;AAEpB,6BAAqB;AACrB;AAAA,MAED,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AAEpB,6BAAqB;AACrB;AAAA,MAED;AAEC,gBAAS,eAAe,UAAW;AAAA,UAElC,KAAK;AACJ,iCAAqB;AACrB;AAAA,UACD,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AACC,iCAAqB;AACrB;AAAA,QAEF;AAEA;AAAA,IAEF;AAEA,UAAM,gBAAgB,QAAQ,kBAAkB,SAAY,cAAe,QAAQ,aAAc,IAAI;AAGrG,UAAM,cAAc,KAAK,sBAAuB,cAAe;AAE/D,aAAU,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAO;AAExD,YAAM,QAAQ,IAAI;AAAA,QACjB,YAAa,CAAE,IAAI,MAAM,gBAAiB,OAAO,IAAK;AAAA,QACtD,cAAc;AAAA,QACd;AAAA,QACA;AAAA,MACD;AAGA,UAAK,QAAQ,kBAAkB,eAAgB;AAE9C,aAAK,mCAAoC,KAAM;AAAA,MAEhD;AAEA,aAAO,KAAM,KAAM;AAAA,IAEpB;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,sBAAuB,UAAW;AAEjC,QAAI,cAAc,SAAS;AAE3B,QAAK,SAAS,YAAa;AAE1B,YAAM,QAAQ,4BAA6B,YAAY,WAAY;AACnE,YAAM,SAAS,IAAI,aAAc,YAAY,MAAO;AAEpD,eAAU,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAO;AAExD,eAAQ,CAAE,IAAI,YAAa,CAAE,IAAI;AAAA,MAElC;AAEA,oBAAc;AAAA,IAEf;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,mCAAoC,OAAQ;AAE3C,UAAM,oBAAoB,SAAS,wCAAyC,QAAS;AAMpF,YAAM,kBAAoB,gBAAgB,0BAA4B,uCAAuC;AAE7G,aAAO,IAAI,gBAAiB,KAAK,OAAO,KAAK,QAAQ,KAAK,aAAa,IAAI,GAAG,MAAO;AAAA,IAEtF;AAGA,UAAM,kBAAkB,4CAA4C;AAAA,EAErE;AAED;AAOA,SAAS,cAAe,UAAU,cAAc,QAAS;AAExD,QAAM,aAAa,aAAa;AAEhC,QAAM,MAAM,IAAI,KAAK;AAErB,MAAK,WAAW,aAAa,QAAY;AAExC,UAAM,WAAW,OAAO,KAAK,UAAW,WAAW,QAAS;AAE5D,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,SAAS;AAIrB,QAAK,QAAQ,UAAa,QAAQ,QAAY;AAE7C,UAAI;AAAA,QACH,IAAI,QAAS,IAAK,CAAE,GAAG,IAAK,CAAE,GAAG,IAAK,CAAE,CAAE;AAAA,QAC1C,IAAI,QAAS,IAAK,CAAE,GAAG,IAAK,CAAE,GAAG,IAAK,CAAE,CAAE;AAAA,MAC3C;AAEA,UAAK,SAAS,YAAa;AAE1B,cAAM,WAAW,4BAA6B,sBAAuB,SAAS,aAAc,CAAE;AAC9F,YAAI,IAAI,eAAgB,QAAS;AACjC,YAAI,IAAI,eAAgB,QAAS;AAAA,MAElC;AAAA,IAED,OAAO;AAEN,cAAQ,KAAM,qEAAsE;AAEpF;AAAA,IAED;AAAA,EAED,OAAO;AAEN;AAAA,EAED;AAEA,QAAM,UAAU,aAAa;AAE7B,MAAK,YAAY,QAAY;AAE5B,UAAM,kBAAkB,IAAI,QAAQ;AACpC,UAAM,SAAS,IAAI,QAAQ;AAE3B,aAAU,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAO;AAEpD,YAAM,SAAS,QAAS,CAAE;AAE1B,UAAK,OAAO,aAAa,QAAY;AAEpC,cAAM,WAAW,OAAO,KAAK,UAAW,OAAO,QAAS;AACxD,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,SAAS;AAIrB,YAAK,QAAQ,UAAa,QAAQ,QAAY;AAG7C,iBAAO,KAAM,KAAK,IAAK,KAAK,IAAK,IAAK,CAAE,CAAE,GAAG,KAAK,IAAK,IAAK,CAAE,CAAE,CAAE,CAAE;AACpE,iBAAO,KAAM,KAAK,IAAK,KAAK,IAAK,IAAK,CAAE,CAAE,GAAG,KAAK,IAAK,IAAK,CAAE,CAAE,CAAE,CAAE;AACpE,iBAAO,KAAM,KAAK,IAAK,KAAK,IAAK,IAAK,CAAE,CAAE,GAAG,KAAK,IAAK,IAAK,CAAE,CAAE,CAAE,CAAE;AAGpE,cAAK,SAAS,YAAa;AAE1B,kBAAM,WAAW,4BAA6B,sBAAuB,SAAS,aAAc,CAAE;AAC9F,mBAAO,eAAgB,QAAS;AAAA,UAEjC;AAMA,0BAAgB,IAAK,MAAO;AAAA,QAE7B,OAAO;AAEN,kBAAQ,KAAM,qEAAsE;AAAA,QAErF;AAAA,MAED;AAAA,IAED;AAGA,QAAI,eAAgB,eAAgB;AAAA,EAErC;AAEA,WAAS,cAAc;AAEvB,QAAM,SAAS,IAAI,OAAO;AAE1B,MAAI,UAAW,OAAO,MAAO;AAC7B,SAAO,SAAS,IAAI,IAAI,WAAY,IAAI,GAAI,IAAI;AAEhD,WAAS,iBAAiB;AAE3B;AAQA,SAAS,uBAAwB,UAAU,cAAc,QAAS;AAEjE,QAAM,aAAa,aAAa;AAEhC,QAAM,UAAU,CAAC;AAEjB,WAAS,wBAAyB,eAAe,eAAgB;AAEhE,WAAO,OAAO,cAAe,YAAY,aAAc,EACrD,KAAM,SAAW,UAAW;AAE5B,eAAS,aAAc,eAAe,QAAS;AAAA,IAEhD,CAAE;AAAA,EAEJ;AAEA,aAAY,qBAAqB,YAAa;AAE7C,UAAM,qBAAqB,WAAY,iBAAkB,KAAK,kBAAkB,YAAY;AAG5F,QAAK,sBAAsB,SAAS,WAAa;AAEjD,YAAQ,KAAM,wBAAyB,WAAY,iBAAkB,GAAG,kBAAmB,CAAE;AAAA,EAE9F;AAEA,MAAK,aAAa,YAAY,UAAa,CAAE,SAAS,OAAQ;AAE7D,UAAM,WAAW,OAAO,cAAe,YAAY,aAAa,OAAQ,EAAE,KAAM,SAAWC,WAAW;AAErG,eAAS,SAAUA,SAAS;AAAA,IAE7B,CAAE;AAEF,YAAQ,KAAM,QAAS;AAAA,EAExB;AAEA,MAAK,gBAAgB,sBAAsB,wBAAwB,aAAa,YAAa;AAE5F,YAAQ,KAAM,qEAAqE,gBAAgB,iBAAiB,kBAAmB;AAAA,EAExI;AAEA,yBAAwB,UAAU,YAAa;AAE/C,gBAAe,UAAU,cAAc,MAAO;AAE9C,SAAO,QAAQ,IAAK,OAAQ,EAAE,KAAM,WAAY;AAE/C,WAAO,aAAa,YAAY,SAC7B,gBAAiB,UAAU,aAAa,SAAS,MAAO,IACxD;AAAA,EAEJ,CAAE;AAEH;", "names": ["self", "res", "sourceURI", "node", "accessor"]}