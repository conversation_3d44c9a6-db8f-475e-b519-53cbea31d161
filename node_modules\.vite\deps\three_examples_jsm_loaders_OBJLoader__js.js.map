{"version": 3, "sources": ["../../three/examples/jsm/loaders/OBJLoader.js"], "sourcesContent": ["import {\n\tBufferGeometry,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tGroup,\n\tLineBasicMaterial,\n\tLineSegments,\n\tLoader,\n\tMaterial,\n\tMesh,\n\tMeshPhongMaterial,\n\tPoints,\n\tPointsMaterial,\n\tVector3,\n\tColor\n} from 'three';\n\n// o object_name | g group_name\nconst _object_pattern = /^[og]\\s*(.+)?/;\n// mtllib file_reference\nconst _material_library_pattern = /^mtllib /;\n// usemtl material_name\nconst _material_use_pattern = /^usemtl /;\n// usemap map_name\nconst _map_use_pattern = /^usemap /;\nconst _face_vertex_data_separator_pattern = /\\s+/;\n\nconst _vA = new Vector3();\nconst _vB = new Vector3();\nconst _vC = new Vector3();\n\nconst _ab = new Vector3();\nconst _cb = new Vector3();\n\nconst _color = new Color();\n\nfunction ParserState() {\n\n\tconst state = {\n\t\tobjects: [],\n\t\tobject: {},\n\n\t\tvertices: [],\n\t\tnormals: [],\n\t\tcolors: [],\n\t\tuvs: [],\n\n\t\tmaterials: {},\n\t\tmaterialLibraries: [],\n\n\t\tstartObject: function ( name, fromDeclaration ) {\n\n\t\t\t// If the current object (initial from reset) is not from a g/o declaration in the parsed\n\t\t\t// file. We need to use it for the first parsed g/o to keep things in sync.\n\t\t\tif ( this.object && this.object.fromDeclaration === false ) {\n\n\t\t\t\tthis.object.name = name;\n\t\t\t\tthis.object.fromDeclaration = ( fromDeclaration !== false );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t\tconst previousMaterial = ( this.object && typeof this.object.currentMaterial === 'function' ? this.object.currentMaterial() : undefined );\n\n\t\t\tif ( this.object && typeof this.object._finalize === 'function' ) {\n\n\t\t\t\tthis.object._finalize( true );\n\n\t\t\t}\n\n\t\t\tthis.object = {\n\t\t\t\tname: name || '',\n\t\t\t\tfromDeclaration: ( fromDeclaration !== false ),\n\n\t\t\t\tgeometry: {\n\t\t\t\t\tvertices: [],\n\t\t\t\t\tnormals: [],\n\t\t\t\t\tcolors: [],\n\t\t\t\t\tuvs: [],\n\t\t\t\t\thasUVIndices: false\n\t\t\t\t},\n\t\t\t\tmaterials: [],\n\t\t\t\tsmooth: true,\n\n\t\t\t\tstartMaterial: function ( name, libraries ) {\n\n\t\t\t\t\tconst previous = this._finalize( false );\n\n\t\t\t\t\t// New usemtl declaration overwrites an inherited material, except if faces were declared\n\t\t\t\t\t// after the material, then it must be preserved for proper MultiMaterial continuation.\n\t\t\t\t\tif ( previous && ( previous.inherited || previous.groupCount <= 0 ) ) {\n\n\t\t\t\t\t\tthis.materials.splice( previous.index, 1 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst material = {\n\t\t\t\t\t\tindex: this.materials.length,\n\t\t\t\t\t\tname: name || '',\n\t\t\t\t\t\tmtllib: ( Array.isArray( libraries ) && libraries.length > 0 ? libraries[ libraries.length - 1 ] : '' ),\n\t\t\t\t\t\tsmooth: ( previous !== undefined ? previous.smooth : this.smooth ),\n\t\t\t\t\t\tgroupStart: ( previous !== undefined ? previous.groupEnd : 0 ),\n\t\t\t\t\t\tgroupEnd: - 1,\n\t\t\t\t\t\tgroupCount: - 1,\n\t\t\t\t\t\tinherited: false,\n\n\t\t\t\t\t\tclone: function ( index ) {\n\n\t\t\t\t\t\t\tconst cloned = {\n\t\t\t\t\t\t\t\tindex: ( typeof index === 'number' ? index : this.index ),\n\t\t\t\t\t\t\t\tname: this.name,\n\t\t\t\t\t\t\t\tmtllib: this.mtllib,\n\t\t\t\t\t\t\t\tsmooth: this.smooth,\n\t\t\t\t\t\t\t\tgroupStart: 0,\n\t\t\t\t\t\t\t\tgroupEnd: - 1,\n\t\t\t\t\t\t\t\tgroupCount: - 1,\n\t\t\t\t\t\t\t\tinherited: false\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcloned.clone = this.clone.bind( cloned );\n\t\t\t\t\t\t\treturn cloned;\n\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\n\t\t\t\t\tthis.materials.push( material );\n\n\t\t\t\t\treturn material;\n\n\t\t\t\t},\n\n\t\t\t\tcurrentMaterial: function () {\n\n\t\t\t\t\tif ( this.materials.length > 0 ) {\n\n\t\t\t\t\t\treturn this.materials[ this.materials.length - 1 ];\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn undefined;\n\n\t\t\t\t},\n\n\t\t\t\t_finalize: function ( end ) {\n\n\t\t\t\t\tconst lastMultiMaterial = this.currentMaterial();\n\t\t\t\t\tif ( lastMultiMaterial && lastMultiMaterial.groupEnd === - 1 ) {\n\n\t\t\t\t\t\tlastMultiMaterial.groupEnd = this.geometry.vertices.length / 3;\n\t\t\t\t\t\tlastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart;\n\t\t\t\t\t\tlastMultiMaterial.inherited = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// Ignore objects tail materials if no face declarations followed them before a new o/g started.\n\t\t\t\t\tif ( end && this.materials.length > 1 ) {\n\n\t\t\t\t\t\tfor ( let mi = this.materials.length - 1; mi >= 0; mi -- ) {\n\n\t\t\t\t\t\t\tif ( this.materials[ mi ].groupCount <= 0 ) {\n\n\t\t\t\t\t\t\t\tthis.materials.splice( mi, 1 );\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// Guarantee at least one empty material, this makes the creation later more straight forward.\n\t\t\t\t\tif ( end && this.materials.length === 0 ) {\n\n\t\t\t\t\t\tthis.materials.push( {\n\t\t\t\t\t\t\tname: '',\n\t\t\t\t\t\t\tsmooth: this.smooth\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn lastMultiMaterial;\n\n\t\t\t\t}\n\t\t\t};\n\n\t\t\t// Inherit previous objects material.\n\t\t\t// Spec tells us that a declared material must be set to all objects until a new material is declared.\n\t\t\t// If a usemtl declaration is encountered while this new object is being parsed, it will\n\t\t\t// overwrite the inherited material. Exception being that there was already face declarations\n\t\t\t// to the inherited material, then it will be preserved for proper MultiMaterial continuation.\n\n\t\t\tif ( previousMaterial && previousMaterial.name && typeof previousMaterial.clone === 'function' ) {\n\n\t\t\t\tconst declared = previousMaterial.clone( 0 );\n\t\t\t\tdeclared.inherited = true;\n\t\t\t\tthis.object.materials.push( declared );\n\n\t\t\t}\n\n\t\t\tthis.objects.push( this.object );\n\n\t\t},\n\n\t\tfinalize: function () {\n\n\t\t\tif ( this.object && typeof this.object._finalize === 'function' ) {\n\n\t\t\t\tthis.object._finalize( true );\n\n\t\t\t}\n\n\t\t},\n\n\t\tparseVertexIndex: function ( value, len ) {\n\n\t\t\tconst index = parseInt( value, 10 );\n\t\t\treturn ( index >= 0 ? index - 1 : index + len / 3 ) * 3;\n\n\t\t},\n\n\t\tparseNormalIndex: function ( value, len ) {\n\n\t\t\tconst index = parseInt( value, 10 );\n\t\t\treturn ( index >= 0 ? index - 1 : index + len / 3 ) * 3;\n\n\t\t},\n\n\t\tparseUVIndex: function ( value, len ) {\n\n\t\t\tconst index = parseInt( value, 10 );\n\t\t\treturn ( index >= 0 ? index - 1 : index + len / 2 ) * 2;\n\n\t\t},\n\n\t\taddVertex: function ( a, b, c ) {\n\n\t\t\tconst src = this.vertices;\n\t\t\tconst dst = this.object.geometry.vertices;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ], src[ a + 2 ] );\n\t\t\tdst.push( src[ b + 0 ], src[ b + 1 ], src[ b + 2 ] );\n\t\t\tdst.push( src[ c + 0 ], src[ c + 1 ], src[ c + 2 ] );\n\n\t\t},\n\n\t\taddVertexPoint: function ( a ) {\n\n\t\t\tconst src = this.vertices;\n\t\t\tconst dst = this.object.geometry.vertices;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ], src[ a + 2 ] );\n\n\t\t},\n\n\t\taddVertexLine: function ( a ) {\n\n\t\t\tconst src = this.vertices;\n\t\t\tconst dst = this.object.geometry.vertices;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ], src[ a + 2 ] );\n\n\t\t},\n\n\t\taddNormal: function ( a, b, c ) {\n\n\t\t\tconst src = this.normals;\n\t\t\tconst dst = this.object.geometry.normals;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ], src[ a + 2 ] );\n\t\t\tdst.push( src[ b + 0 ], src[ b + 1 ], src[ b + 2 ] );\n\t\t\tdst.push( src[ c + 0 ], src[ c + 1 ], src[ c + 2 ] );\n\n\t\t},\n\n\t\taddFaceNormal: function ( a, b, c ) {\n\n\t\t\tconst src = this.vertices;\n\t\t\tconst dst = this.object.geometry.normals;\n\n\t\t\t_vA.fromArray( src, a );\n\t\t\t_vB.fromArray( src, b );\n\t\t\t_vC.fromArray( src, c );\n\n\t\t\t_cb.subVectors( _vC, _vB );\n\t\t\t_ab.subVectors( _vA, _vB );\n\t\t\t_cb.cross( _ab );\n\n\t\t\t_cb.normalize();\n\n\t\t\tdst.push( _cb.x, _cb.y, _cb.z );\n\t\t\tdst.push( _cb.x, _cb.y, _cb.z );\n\t\t\tdst.push( _cb.x, _cb.y, _cb.z );\n\n\t\t},\n\n\t\taddColor: function ( a, b, c ) {\n\n\t\t\tconst src = this.colors;\n\t\t\tconst dst = this.object.geometry.colors;\n\n\t\t\tif ( src[ a ] !== undefined ) dst.push( src[ a + 0 ], src[ a + 1 ], src[ a + 2 ] );\n\t\t\tif ( src[ b ] !== undefined ) dst.push( src[ b + 0 ], src[ b + 1 ], src[ b + 2 ] );\n\t\t\tif ( src[ c ] !== undefined ) dst.push( src[ c + 0 ], src[ c + 1 ], src[ c + 2 ] );\n\n\t\t},\n\n\t\taddUV: function ( a, b, c ) {\n\n\t\t\tconst src = this.uvs;\n\t\t\tconst dst = this.object.geometry.uvs;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ] );\n\t\t\tdst.push( src[ b + 0 ], src[ b + 1 ] );\n\t\t\tdst.push( src[ c + 0 ], src[ c + 1 ] );\n\n\t\t},\n\n\t\taddDefaultUV: function () {\n\n\t\t\tconst dst = this.object.geometry.uvs;\n\n\t\t\tdst.push( 0, 0 );\n\t\t\tdst.push( 0, 0 );\n\t\t\tdst.push( 0, 0 );\n\n\t\t},\n\n\t\taddUVLine: function ( a ) {\n\n\t\t\tconst src = this.uvs;\n\t\t\tconst dst = this.object.geometry.uvs;\n\n\t\t\tdst.push( src[ a + 0 ], src[ a + 1 ] );\n\n\t\t},\n\n\t\taddFace: function ( a, b, c, ua, ub, uc, na, nb, nc ) {\n\n\t\t\tconst vLen = this.vertices.length;\n\n\t\t\tlet ia = this.parseVertexIndex( a, vLen );\n\t\t\tlet ib = this.parseVertexIndex( b, vLen );\n\t\t\tlet ic = this.parseVertexIndex( c, vLen );\n\n\t\t\tthis.addVertex( ia, ib, ic );\n\t\t\tthis.addColor( ia, ib, ic );\n\n\t\t\t// normals\n\n\t\t\tif ( na !== undefined && na !== '' ) {\n\n\t\t\t\tconst nLen = this.normals.length;\n\n\t\t\t\tia = this.parseNormalIndex( na, nLen );\n\t\t\t\tib = this.parseNormalIndex( nb, nLen );\n\t\t\t\tic = this.parseNormalIndex( nc, nLen );\n\n\t\t\t\tthis.addNormal( ia, ib, ic );\n\n\t\t\t} else {\n\n\t\t\t\tthis.addFaceNormal( ia, ib, ic );\n\n\t\t\t}\n\n\t\t\t// uvs\n\n\t\t\tif ( ua !== undefined && ua !== '' ) {\n\n\t\t\t\tconst uvLen = this.uvs.length;\n\n\t\t\t\tia = this.parseUVIndex( ua, uvLen );\n\t\t\t\tib = this.parseUVIndex( ub, uvLen );\n\t\t\t\tic = this.parseUVIndex( uc, uvLen );\n\n\t\t\t\tthis.addUV( ia, ib, ic );\n\n\t\t\t\tthis.object.geometry.hasUVIndices = true;\n\n\t\t\t} else {\n\n\t\t\t\t// add placeholder values (for inconsistent face definitions)\n\n\t\t\t\tthis.addDefaultUV();\n\n\t\t\t}\n\n\t\t},\n\n\t\taddPointGeometry: function ( vertices ) {\n\n\t\t\tthis.object.geometry.type = 'Points';\n\n\t\t\tconst vLen = this.vertices.length;\n\n\t\t\tfor ( let vi = 0, l = vertices.length; vi < l; vi ++ ) {\n\n\t\t\t\tconst index = this.parseVertexIndex( vertices[ vi ], vLen );\n\n\t\t\t\tthis.addVertexPoint( index );\n\t\t\t\tthis.addColor( index );\n\n\t\t\t}\n\n\t\t},\n\n\t\taddLineGeometry: function ( vertices, uvs ) {\n\n\t\t\tthis.object.geometry.type = 'Line';\n\n\t\t\tconst vLen = this.vertices.length;\n\t\t\tconst uvLen = this.uvs.length;\n\n\t\t\tfor ( let vi = 0, l = vertices.length; vi < l; vi ++ ) {\n\n\t\t\t\tthis.addVertexLine( this.parseVertexIndex( vertices[ vi ], vLen ) );\n\n\t\t\t}\n\n\t\t\tfor ( let uvi = 0, l = uvs.length; uvi < l; uvi ++ ) {\n\n\t\t\t\tthis.addUVLine( this.parseUVIndex( uvs[ uvi ], uvLen ) );\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n\tstate.startObject( '', false );\n\n\treturn state;\n\n}\n\n//\n\nclass OBJLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.materials = null;\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.path );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\t\tloader.load( url, function ( text ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( text ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tsetMaterials( materials ) {\n\n\t\tthis.materials = materials;\n\n\t\treturn this;\n\n\t}\n\n\tparse( text ) {\n\n\t\tconst state = new ParserState();\n\n\t\tif ( text.indexOf( '\\r\\n' ) !== - 1 ) {\n\n\t\t\t// This is faster than String.split with regex that splits on both\n\t\t\ttext = text.replace( /\\r\\n/g, '\\n' );\n\n\t\t}\n\n\t\tif ( text.indexOf( '\\\\\\n' ) !== - 1 ) {\n\n\t\t\t// join lines separated by a line continuation character (\\)\n\t\t\ttext = text.replace( /\\\\\\n/g, '' );\n\n\t\t}\n\n\t\tconst lines = text.split( '\\n' );\n\t\tlet result = [];\n\n\t\tfor ( let i = 0, l = lines.length; i < l; i ++ ) {\n\n\t\t\tconst line = lines[ i ].trimStart();\n\n\t\t\tif ( line.length === 0 ) continue;\n\n\t\t\tconst lineFirstChar = line.charAt( 0 );\n\n\t\t\t// @todo invoke passed in handler if any\n\t\t\tif ( lineFirstChar === '#' ) continue; // skip comments\n\n\t\t\tif ( lineFirstChar === 'v' ) {\n\n\t\t\t\tconst data = line.split( _face_vertex_data_separator_pattern );\n\n\t\t\t\tswitch ( data[ 0 ] ) {\n\n\t\t\t\t\tcase 'v':\n\t\t\t\t\t\tstate.vertices.push(\n\t\t\t\t\t\t\tparseFloat( data[ 1 ] ),\n\t\t\t\t\t\t\tparseFloat( data[ 2 ] ),\n\t\t\t\t\t\t\tparseFloat( data[ 3 ] )\n\t\t\t\t\t\t);\n\t\t\t\t\t\tif ( data.length >= 7 ) {\n\n\t\t\t\t\t\t\t_color.setRGB(\n\t\t\t\t\t\t\t\tparseFloat( data[ 4 ] ),\n\t\t\t\t\t\t\t\tparseFloat( data[ 5 ] ),\n\t\t\t\t\t\t\t\tparseFloat( data[ 6 ] )\n\t\t\t\t\t\t\t).convertSRGBToLinear();\n\n\t\t\t\t\t\t\tstate.colors.push( _color.r, _color.g, _color.b );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// if no colors are defined, add placeholders so color and vertex indices match\n\n\t\t\t\t\t\t\tstate.colors.push( undefined, undefined, undefined );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'vn':\n\t\t\t\t\t\tstate.normals.push(\n\t\t\t\t\t\t\tparseFloat( data[ 1 ] ),\n\t\t\t\t\t\t\tparseFloat( data[ 2 ] ),\n\t\t\t\t\t\t\tparseFloat( data[ 3 ] )\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'vt':\n\t\t\t\t\t\tstate.uvs.push(\n\t\t\t\t\t\t\tparseFloat( data[ 1 ] ),\n\t\t\t\t\t\t\tparseFloat( data[ 2 ] )\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t} else if ( lineFirstChar === 'f' ) {\n\n\t\t\t\tconst lineData = line.slice( 1 ).trim();\n\t\t\t\tconst vertexData = lineData.split( _face_vertex_data_separator_pattern );\n\t\t\t\tconst faceVertices = [];\n\n\t\t\t\t// Parse the face vertex data into an easy to work with format\n\n\t\t\t\tfor ( let j = 0, jl = vertexData.length; j < jl; j ++ ) {\n\n\t\t\t\t\tconst vertex = vertexData[ j ];\n\n\t\t\t\t\tif ( vertex.length > 0 ) {\n\n\t\t\t\t\t\tconst vertexParts = vertex.split( '/' );\n\t\t\t\t\t\tfaceVertices.push( vertexParts );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Draw an edge between the first vertex and all subsequent vertices to form an n-gon\n\n\t\t\t\tconst v1 = faceVertices[ 0 ];\n\n\t\t\t\tfor ( let j = 1, jl = faceVertices.length - 1; j < jl; j ++ ) {\n\n\t\t\t\t\tconst v2 = faceVertices[ j ];\n\t\t\t\t\tconst v3 = faceVertices[ j + 1 ];\n\n\t\t\t\t\tstate.addFace(\n\t\t\t\t\t\tv1[ 0 ], v2[ 0 ], v3[ 0 ],\n\t\t\t\t\t\tv1[ 1 ], v2[ 1 ], v3[ 1 ],\n\t\t\t\t\t\tv1[ 2 ], v2[ 2 ], v3[ 2 ]\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t} else if ( lineFirstChar === 'l' ) {\n\n\t\t\t\tconst lineParts = line.substring( 1 ).trim().split( ' ' );\n\t\t\t\tlet lineVertices = [];\n\t\t\t\tconst lineUVs = [];\n\n\t\t\t\tif ( line.indexOf( '/' ) === - 1 ) {\n\n\t\t\t\t\tlineVertices = lineParts;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tfor ( let li = 0, llen = lineParts.length; li < llen; li ++ ) {\n\n\t\t\t\t\t\tconst parts = lineParts[ li ].split( '/' );\n\n\t\t\t\t\t\tif ( parts[ 0 ] !== '' ) lineVertices.push( parts[ 0 ] );\n\t\t\t\t\t\tif ( parts[ 1 ] !== '' ) lineUVs.push( parts[ 1 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tstate.addLineGeometry( lineVertices, lineUVs );\n\n\t\t\t} else if ( lineFirstChar === 'p' ) {\n\n\t\t\t\tconst lineData = line.slice( 1 ).trim();\n\t\t\t\tconst pointData = lineData.split( ' ' );\n\n\t\t\t\tstate.addPointGeometry( pointData );\n\n\t\t\t} else if ( ( result = _object_pattern.exec( line ) ) !== null ) {\n\n\t\t\t\t// o object_name\n\t\t\t\t// or\n\t\t\t\t// g group_name\n\n\t\t\t\t// WORKAROUND: https://bugs.chromium.org/p/v8/issues/detail?id=2869\n\t\t\t\t// let name = result[ 0 ].slice( 1 ).trim();\n\t\t\t\tconst name = ( ' ' + result[ 0 ].slice( 1 ).trim() ).slice( 1 );\n\n\t\t\t\tstate.startObject( name );\n\n\t\t\t} else if ( _material_use_pattern.test( line ) ) {\n\n\t\t\t\t// material\n\n\t\t\t\tstate.object.startMaterial( line.substring( 7 ).trim(), state.materialLibraries );\n\n\t\t\t} else if ( _material_library_pattern.test( line ) ) {\n\n\t\t\t\t// mtl file\n\n\t\t\t\tstate.materialLibraries.push( line.substring( 7 ).trim() );\n\n\t\t\t} else if ( _map_use_pattern.test( line ) ) {\n\n\t\t\t\t// the line is parsed but ignored since the loader assumes textures are defined MTL files\n\t\t\t\t// (according to https://www.okino.com/conv/imp_wave.htm, 'usemap' is the old-style Wavefront texture reference method)\n\n\t\t\t\tconsole.warn( 'THREE.OBJLoader: Rendering identifier \"usemap\" not supported. Textures must be defined in MTL files.' );\n\n\t\t\t} else if ( lineFirstChar === 's' ) {\n\n\t\t\t\tresult = line.split( ' ' );\n\n\t\t\t\t// smooth shading\n\n\t\t\t\t// @todo Handle files that have varying smooth values for a set of faces inside one geometry,\n\t\t\t\t// but does not define a usemtl for each face set.\n\t\t\t\t// This should be detected and a dummy material created (later MultiMaterial and geometry groups).\n\t\t\t\t// This requires some care to not create extra material on each smooth value for \"normal\" obj files.\n\t\t\t\t// where explicit usemtl defines geometry groups.\n\t\t\t\t// Example asset: examples/models/obj/cerberus/Cerberus.obj\n\n\t\t\t\t/*\n\t\t\t\t\t * http://paulbourke.net/dataformats/obj/\n\t\t\t\t\t *\n\t\t\t\t\t * From chapter \"Grouping\" Syntax explanation \"s group_number\":\n\t\t\t\t\t * \"group_number is the smoothing group number. To turn off smoothing groups, use a value of 0 or off.\n\t\t\t\t\t * Polygonal elements use group numbers to put elements in different smoothing groups. For free-form\n\t\t\t\t\t * surfaces, smoothing groups are either turned on or off; there is no difference between values greater\n\t\t\t\t\t * than 0.\"\n\t\t\t\t\t */\n\t\t\t\tif ( result.length > 1 ) {\n\n\t\t\t\t\tconst value = result[ 1 ].trim().toLowerCase();\n\t\t\t\t\tstate.object.smooth = ( value !== '0' && value !== 'off' );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// ZBrush can produce \"s\" lines #11707\n\t\t\t\t\tstate.object.smooth = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst material = state.object.currentMaterial();\n\t\t\t\tif ( material ) material.smooth = state.object.smooth;\n\n\t\t\t} else {\n\n\t\t\t\t// Handle null terminated files without exception\n\t\t\t\tif ( line === '\\0' ) continue;\n\n\t\t\t\tconsole.warn( 'THREE.OBJLoader: Unexpected line: \"' + line + '\"' );\n\n\t\t\t}\n\n\t\t}\n\n\t\tstate.finalize();\n\n\t\tconst container = new Group();\n\t\tcontainer.materialLibraries = [].concat( state.materialLibraries );\n\n\t\tconst hasPrimitives = ! ( state.objects.length === 1 && state.objects[ 0 ].geometry.vertices.length === 0 );\n\n\t\tif ( hasPrimitives === true ) {\n\n\t\t\tfor ( let i = 0, l = state.objects.length; i < l; i ++ ) {\n\n\t\t\t\tconst object = state.objects[ i ];\n\t\t\t\tconst geometry = object.geometry;\n\t\t\t\tconst materials = object.materials;\n\t\t\t\tconst isLine = ( geometry.type === 'Line' );\n\t\t\t\tconst isPoints = ( geometry.type === 'Points' );\n\t\t\t\tlet hasVertexColors = false;\n\n\t\t\t\t// Skip o/g line declarations that did not follow with any faces\n\t\t\t\tif ( geometry.vertices.length === 0 ) continue;\n\n\t\t\t\tconst buffergeometry = new BufferGeometry();\n\n\t\t\t\tbuffergeometry.setAttribute( 'position', new Float32BufferAttribute( geometry.vertices, 3 ) );\n\n\t\t\t\tif ( geometry.normals.length > 0 ) {\n\n\t\t\t\t\tbuffergeometry.setAttribute( 'normal', new Float32BufferAttribute( geometry.normals, 3 ) );\n\n\t\t\t\t}\n\n\t\t\t\tif ( geometry.colors.length > 0 ) {\n\n\t\t\t\t\thasVertexColors = true;\n\t\t\t\t\tbuffergeometry.setAttribute( 'color', new Float32BufferAttribute( geometry.colors, 3 ) );\n\n\t\t\t\t}\n\n\t\t\t\tif ( geometry.hasUVIndices === true ) {\n\n\t\t\t\t\tbuffergeometry.setAttribute( 'uv', new Float32BufferAttribute( geometry.uvs, 2 ) );\n\n\t\t\t\t}\n\n\t\t\t\t// Create materials\n\n\t\t\t\tconst createdMaterials = [];\n\n\t\t\t\tfor ( let mi = 0, miLen = materials.length; mi < miLen; mi ++ ) {\n\n\t\t\t\t\tconst sourceMaterial = materials[ mi ];\n\t\t\t\t\tconst materialHash = sourceMaterial.name + '_' + sourceMaterial.smooth + '_' + hasVertexColors;\n\t\t\t\t\tlet material = state.materials[ materialHash ];\n\n\t\t\t\t\tif ( this.materials !== null ) {\n\n\t\t\t\t\t\tmaterial = this.materials.create( sourceMaterial.name );\n\n\t\t\t\t\t\t// mtl etc. loaders probably can't create line materials correctly, copy properties to a line material.\n\t\t\t\t\t\tif ( isLine && material && ! ( material instanceof LineBasicMaterial ) ) {\n\n\t\t\t\t\t\t\tconst materialLine = new LineBasicMaterial();\n\t\t\t\t\t\t\tMaterial.prototype.copy.call( materialLine, material );\n\t\t\t\t\t\t\tmaterialLine.color.copy( material.color );\n\t\t\t\t\t\t\tmaterial = materialLine;\n\n\t\t\t\t\t\t} else if ( isPoints && material && ! ( material instanceof PointsMaterial ) ) {\n\n\t\t\t\t\t\t\tconst materialPoints = new PointsMaterial( { size: 10, sizeAttenuation: false } );\n\t\t\t\t\t\t\tMaterial.prototype.copy.call( materialPoints, material );\n\t\t\t\t\t\t\tmaterialPoints.color.copy( material.color );\n\t\t\t\t\t\t\tmaterialPoints.map = material.map;\n\t\t\t\t\t\t\tmaterial = materialPoints;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( material === undefined ) {\n\n\t\t\t\t\t\tif ( isLine ) {\n\n\t\t\t\t\t\t\tmaterial = new LineBasicMaterial();\n\n\t\t\t\t\t\t} else if ( isPoints ) {\n\n\t\t\t\t\t\t\tmaterial = new PointsMaterial( { size: 1, sizeAttenuation: false } );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tmaterial = new MeshPhongMaterial();\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tmaterial.name = sourceMaterial.name;\n\t\t\t\t\t\tmaterial.flatShading = sourceMaterial.smooth ? false : true;\n\t\t\t\t\t\tmaterial.vertexColors = hasVertexColors;\n\n\t\t\t\t\t\tstate.materials[ materialHash ] = material;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tcreatedMaterials.push( material );\n\n\t\t\t\t}\n\n\t\t\t\t// Create mesh\n\n\t\t\t\tlet mesh;\n\n\t\t\t\tif ( createdMaterials.length > 1 ) {\n\n\t\t\t\t\tfor ( let mi = 0, miLen = materials.length; mi < miLen; mi ++ ) {\n\n\t\t\t\t\t\tconst sourceMaterial = materials[ mi ];\n\t\t\t\t\t\tbuffergeometry.addGroup( sourceMaterial.groupStart, sourceMaterial.groupCount, mi );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( isLine ) {\n\n\t\t\t\t\t\tmesh = new LineSegments( buffergeometry, createdMaterials );\n\n\t\t\t\t\t} else if ( isPoints ) {\n\n\t\t\t\t\t\tmesh = new Points( buffergeometry, createdMaterials );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tmesh = new Mesh( buffergeometry, createdMaterials );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( isLine ) {\n\n\t\t\t\t\t\tmesh = new LineSegments( buffergeometry, createdMaterials[ 0 ] );\n\n\t\t\t\t\t} else if ( isPoints ) {\n\n\t\t\t\t\t\tmesh = new Points( buffergeometry, createdMaterials[ 0 ] );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tmesh = new Mesh( buffergeometry, createdMaterials[ 0 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tmesh.name = object.name;\n\n\t\t\t\tcontainer.add( mesh );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// if there is only the default parser state object with no geometry data, interpret data as point cloud\n\n\t\t\tif ( state.vertices.length > 0 ) {\n\n\t\t\t\tconst material = new PointsMaterial( { size: 1, sizeAttenuation: false } );\n\n\t\t\t\tconst buffergeometry = new BufferGeometry();\n\n\t\t\t\tbuffergeometry.setAttribute( 'position', new Float32BufferAttribute( state.vertices, 3 ) );\n\n\t\t\t\tif ( state.colors.length > 0 && state.colors[ 0 ] !== undefined ) {\n\n\t\t\t\t\tbuffergeometry.setAttribute( 'color', new Float32BufferAttribute( state.colors, 3 ) );\n\t\t\t\t\tmaterial.vertexColors = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst points = new Points( buffergeometry, material );\n\t\t\t\tcontainer.add( points );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn container;\n\n\t}\n\n}\n\nexport { OBJLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAM,kBAAkB;AAExB,IAAM,4BAA4B;AAElC,IAAM,wBAAwB;AAE9B,IAAM,mBAAmB;AACzB,IAAM,sCAAsC;AAE5C,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AAExB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AAExB,IAAM,SAAS,IAAI,MAAM;AAEzB,SAAS,cAAc;AAEtB,QAAM,QAAQ;AAAA,IACb,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IAET,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,KAAK,CAAC;AAAA,IAEN,WAAW,CAAC;AAAA,IACZ,mBAAmB,CAAC;AAAA,IAEpB,aAAa,SAAW,MAAM,iBAAkB;AAI/C,UAAK,KAAK,UAAU,KAAK,OAAO,oBAAoB,OAAQ;AAE3D,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO,kBAAoB,oBAAoB;AACpD;AAAA,MAED;AAEA,YAAM,mBAAqB,KAAK,UAAU,OAAO,KAAK,OAAO,oBAAoB,aAAa,KAAK,OAAO,gBAAgB,IAAI;AAE9H,UAAK,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAa;AAEjE,aAAK,OAAO,UAAW,IAAK;AAAA,MAE7B;AAEA,WAAK,SAAS;AAAA,QACb,MAAM,QAAQ;AAAA,QACd,iBAAmB,oBAAoB;AAAA,QAEvC,UAAU;AAAA,UACT,UAAU,CAAC;AAAA,UACX,SAAS,CAAC;AAAA,UACV,QAAQ,CAAC;AAAA,UACT,KAAK,CAAC;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACA,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QAER,eAAe,SAAWA,OAAM,WAAY;AAE3C,gBAAM,WAAW,KAAK,UAAW,KAAM;AAIvC,cAAK,aAAc,SAAS,aAAa,SAAS,cAAc,IAAM;AAErE,iBAAK,UAAU,OAAQ,SAAS,OAAO,CAAE;AAAA,UAE1C;AAEA,gBAAM,WAAW;AAAA,YAChB,OAAO,KAAK,UAAU;AAAA,YACtB,MAAMA,SAAQ;AAAA,YACd,QAAU,MAAM,QAAS,SAAU,KAAK,UAAU,SAAS,IAAI,UAAW,UAAU,SAAS,CAAE,IAAI;AAAA,YACnG,QAAU,aAAa,SAAY,SAAS,SAAS,KAAK;AAAA,YAC1D,YAAc,aAAa,SAAY,SAAS,WAAW;AAAA,YAC3D,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YAEX,OAAO,SAAW,OAAQ;AAEzB,oBAAM,SAAS;AAAA,gBACd,OAAS,OAAO,UAAU,WAAW,QAAQ,KAAK;AAAA,gBAClD,MAAM,KAAK;AAAA,gBACX,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,WAAW;AAAA,cACZ;AACA,qBAAO,QAAQ,KAAK,MAAM,KAAM,MAAO;AACvC,qBAAO;AAAA,YAER;AAAA,UACD;AAEA,eAAK,UAAU,KAAM,QAAS;AAE9B,iBAAO;AAAA,QAER;AAAA,QAEA,iBAAiB,WAAY;AAE5B,cAAK,KAAK,UAAU,SAAS,GAAI;AAEhC,mBAAO,KAAK,UAAW,KAAK,UAAU,SAAS,CAAE;AAAA,UAElD;AAEA,iBAAO;AAAA,QAER;AAAA,QAEA,WAAW,SAAW,KAAM;AAE3B,gBAAM,oBAAoB,KAAK,gBAAgB;AAC/C,cAAK,qBAAqB,kBAAkB,aAAa,IAAM;AAE9D,8BAAkB,WAAW,KAAK,SAAS,SAAS,SAAS;AAC7D,8BAAkB,aAAa,kBAAkB,WAAW,kBAAkB;AAC9E,8BAAkB,YAAY;AAAA,UAE/B;AAGA,cAAK,OAAO,KAAK,UAAU,SAAS,GAAI;AAEvC,qBAAU,KAAK,KAAK,UAAU,SAAS,GAAG,MAAM,GAAG,MAAQ;AAE1D,kBAAK,KAAK,UAAW,EAAG,EAAE,cAAc,GAAI;AAE3C,qBAAK,UAAU,OAAQ,IAAI,CAAE;AAAA,cAE9B;AAAA,YAED;AAAA,UAED;AAGA,cAAK,OAAO,KAAK,UAAU,WAAW,GAAI;AAEzC,iBAAK,UAAU,KAAM;AAAA,cACpB,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,YACd,CAAE;AAAA,UAEH;AAEA,iBAAO;AAAA,QAER;AAAA,MACD;AAQA,UAAK,oBAAoB,iBAAiB,QAAQ,OAAO,iBAAiB,UAAU,YAAa;AAEhG,cAAM,WAAW,iBAAiB,MAAO,CAAE;AAC3C,iBAAS,YAAY;AACrB,aAAK,OAAO,UAAU,KAAM,QAAS;AAAA,MAEtC;AAEA,WAAK,QAAQ,KAAM,KAAK,MAAO;AAAA,IAEhC;AAAA,IAEA,UAAU,WAAY;AAErB,UAAK,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAa;AAEjE,aAAK,OAAO,UAAW,IAAK;AAAA,MAE7B;AAAA,IAED;AAAA,IAEA,kBAAkB,SAAW,OAAO,KAAM;AAEzC,YAAM,QAAQ,SAAU,OAAO,EAAG;AAClC,cAAS,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAM;AAAA,IAEvD;AAAA,IAEA,kBAAkB,SAAW,OAAO,KAAM;AAEzC,YAAM,QAAQ,SAAU,OAAO,EAAG;AAClC,cAAS,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAM;AAAA,IAEvD;AAAA,IAEA,cAAc,SAAW,OAAO,KAAM;AAErC,YAAM,QAAQ,SAAU,OAAO,EAAG;AAClC,cAAS,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAM;AAAA,IAEvD;AAAA,IAEA,WAAW,SAAW,GAAG,GAAG,GAAI;AAE/B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACnD,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACnD,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEpD;AAAA,IAEA,gBAAgB,SAAW,GAAI;AAE9B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEpD;AAAA,IAEA,eAAe,SAAW,GAAI;AAE7B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEpD;AAAA,IAEA,WAAW,SAAW,GAAG,GAAG,GAAI;AAE/B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACnD,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACnD,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEpD;AAAA,IAEA,eAAe,SAAW,GAAG,GAAG,GAAI;AAEnC,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,UAAW,KAAK,CAAE;AACtB,UAAI,UAAW,KAAK,CAAE;AACtB,UAAI,UAAW,KAAK,CAAE;AAEtB,UAAI,WAAY,KAAK,GAAI;AACzB,UAAI,WAAY,KAAK,GAAI;AACzB,UAAI,MAAO,GAAI;AAEf,UAAI,UAAU;AAEd,UAAI,KAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;AAC9B,UAAI,KAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;AAC9B,UAAI,KAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;AAAA,IAE/B;AAAA,IAEA,UAAU,SAAW,GAAG,GAAG,GAAI;AAE9B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAK,IAAK,CAAE,MAAM,OAAY,KAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACjF,UAAK,IAAK,CAAE,MAAM,OAAY,KAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACjF,UAAK,IAAK,CAAE,MAAM,OAAY,KAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAElF;AAAA,IAEA,OAAO,SAAW,GAAG,GAAG,GAAI;AAE3B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACrC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AACrC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEtC;AAAA,IAEA,cAAc,WAAY;AAEzB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,GAAG,CAAE;AACf,UAAI,KAAM,GAAG,CAAE;AACf,UAAI,KAAM,GAAG,CAAE;AAAA,IAEhB;AAAA,IAEA,WAAW,SAAW,GAAI;AAEzB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAM,IAAK,IAAI,CAAE,GAAG,IAAK,IAAI,CAAE,CAAE;AAAA,IAEtC;AAAA,IAEA,SAAS,SAAW,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAK;AAErD,YAAM,OAAO,KAAK,SAAS;AAE3B,UAAI,KAAK,KAAK,iBAAkB,GAAG,IAAK;AACxC,UAAI,KAAK,KAAK,iBAAkB,GAAG,IAAK;AACxC,UAAI,KAAK,KAAK,iBAAkB,GAAG,IAAK;AAExC,WAAK,UAAW,IAAI,IAAI,EAAG;AAC3B,WAAK,SAAU,IAAI,IAAI,EAAG;AAI1B,UAAK,OAAO,UAAa,OAAO,IAAK;AAEpC,cAAM,OAAO,KAAK,QAAQ;AAE1B,aAAK,KAAK,iBAAkB,IAAI,IAAK;AACrC,aAAK,KAAK,iBAAkB,IAAI,IAAK;AACrC,aAAK,KAAK,iBAAkB,IAAI,IAAK;AAErC,aAAK,UAAW,IAAI,IAAI,EAAG;AAAA,MAE5B,OAAO;AAEN,aAAK,cAAe,IAAI,IAAI,EAAG;AAAA,MAEhC;AAIA,UAAK,OAAO,UAAa,OAAO,IAAK;AAEpC,cAAM,QAAQ,KAAK,IAAI;AAEvB,aAAK,KAAK,aAAc,IAAI,KAAM;AAClC,aAAK,KAAK,aAAc,IAAI,KAAM;AAClC,aAAK,KAAK,aAAc,IAAI,KAAM;AAElC,aAAK,MAAO,IAAI,IAAI,EAAG;AAEvB,aAAK,OAAO,SAAS,eAAe;AAAA,MAErC,OAAO;AAIN,aAAK,aAAa;AAAA,MAEnB;AAAA,IAED;AAAA,IAEA,kBAAkB,SAAW,UAAW;AAEvC,WAAK,OAAO,SAAS,OAAO;AAE5B,YAAM,OAAO,KAAK,SAAS;AAE3B,eAAU,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAQ;AAEtD,cAAM,QAAQ,KAAK,iBAAkB,SAAU,EAAG,GAAG,IAAK;AAE1D,aAAK,eAAgB,KAAM;AAC3B,aAAK,SAAU,KAAM;AAAA,MAEtB;AAAA,IAED;AAAA,IAEA,iBAAiB,SAAW,UAAU,KAAM;AAE3C,WAAK,OAAO,SAAS,OAAO;AAE5B,YAAM,OAAO,KAAK,SAAS;AAC3B,YAAM,QAAQ,KAAK,IAAI;AAEvB,eAAU,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAQ;AAEtD,aAAK,cAAe,KAAK,iBAAkB,SAAU,EAAG,GAAG,IAAK,CAAE;AAAA,MAEnE;AAEA,eAAU,MAAM,GAAG,IAAI,IAAI,QAAQ,MAAM,GAAG,OAAS;AAEpD,aAAK,UAAW,KAAK,aAAc,IAAK,GAAI,GAAG,KAAM,CAAE;AAAA,MAExD;AAAA,IAED;AAAA,EAED;AAEA,QAAM,YAAa,IAAI,KAAM;AAE7B,SAAO;AAER;AAIA,IAAM,YAAN,cAAwB,OAAO;AAAA,EAE9B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,YAAY;AAAA,EAElB;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAChD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,eAAQ,MAAM,MAAO,IAAK,CAAE;AAAA,MAE7B,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,aAAc,WAAY;AAEzB,SAAK,YAAY;AAEjB,WAAO;AAAA,EAER;AAAA,EAEA,MAAO,MAAO;AAEb,UAAM,QAAQ,IAAI,YAAY;AAE9B,QAAK,KAAK,QAAS,MAAO,MAAM,IAAM;AAGrC,aAAO,KAAK,QAAS,SAAS,IAAK;AAAA,IAEpC;AAEA,QAAK,KAAK,QAAS,MAAO,MAAM,IAAM;AAGrC,aAAO,KAAK,QAAS,SAAS,EAAG;AAAA,IAElC;AAEA,UAAM,QAAQ,KAAK,MAAO,IAAK;AAC/B,QAAI,SAAS,CAAC;AAEd,aAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,YAAM,OAAO,MAAO,CAAE,EAAE,UAAU;AAElC,UAAK,KAAK,WAAW,EAAI;AAEzB,YAAM,gBAAgB,KAAK,OAAQ,CAAE;AAGrC,UAAK,kBAAkB,IAAM;AAE7B,UAAK,kBAAkB,KAAM;AAE5B,cAAM,OAAO,KAAK,MAAO,mCAAoC;AAE7D,gBAAS,KAAM,CAAE,GAAI;AAAA,UAEpB,KAAK;AACJ,kBAAM,SAAS;AAAA,cACd,WAAY,KAAM,CAAE,CAAE;AAAA,cACtB,WAAY,KAAM,CAAE,CAAE;AAAA,cACtB,WAAY,KAAM,CAAE,CAAE;AAAA,YACvB;AACA,gBAAK,KAAK,UAAU,GAAI;AAEvB,qBAAO;AAAA,gBACN,WAAY,KAAM,CAAE,CAAE;AAAA,gBACtB,WAAY,KAAM,CAAE,CAAE;AAAA,gBACtB,WAAY,KAAM,CAAE,CAAE;AAAA,cACvB,EAAE,oBAAoB;AAEtB,oBAAM,OAAO,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAAA,YAEjD,OAAO;AAIN,oBAAM,OAAO,KAAM,QAAW,QAAW,MAAU;AAAA,YAEpD;AAEA;AAAA,UACD,KAAK;AACJ,kBAAM,QAAQ;AAAA,cACb,WAAY,KAAM,CAAE,CAAE;AAAA,cACtB,WAAY,KAAM,CAAE,CAAE;AAAA,cACtB,WAAY,KAAM,CAAE,CAAE;AAAA,YACvB;AACA;AAAA,UACD,KAAK;AACJ,kBAAM,IAAI;AAAA,cACT,WAAY,KAAM,CAAE,CAAE;AAAA,cACtB,WAAY,KAAM,CAAE,CAAE;AAAA,YACvB;AACA;AAAA,QAEF;AAAA,MAED,WAAY,kBAAkB,KAAM;AAEnC,cAAM,WAAW,KAAK,MAAO,CAAE,EAAE,KAAK;AACtC,cAAM,aAAa,SAAS,MAAO,mCAAoC;AACvE,cAAM,eAAe,CAAC;AAItB,iBAAU,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAO;AAEvD,gBAAM,SAAS,WAAY,CAAE;AAE7B,cAAK,OAAO,SAAS,GAAI;AAExB,kBAAM,cAAc,OAAO,MAAO,GAAI;AACtC,yBAAa,KAAM,WAAY;AAAA,UAEhC;AAAA,QAED;AAIA,cAAM,KAAK,aAAc,CAAE;AAE3B,iBAAU,IAAI,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI,IAAI,KAAO;AAE7D,gBAAM,KAAK,aAAc,CAAE;AAC3B,gBAAM,KAAK,aAAc,IAAI,CAAE;AAE/B,gBAAM;AAAA,YACL,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,YACxB,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,YACxB,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,YAAG,GAAI,CAAE;AAAA,UACzB;AAAA,QAED;AAAA,MAED,WAAY,kBAAkB,KAAM;AAEnC,cAAM,YAAY,KAAK,UAAW,CAAE,EAAE,KAAK,EAAE,MAAO,GAAI;AACxD,YAAI,eAAe,CAAC;AACpB,cAAM,UAAU,CAAC;AAEjB,YAAK,KAAK,QAAS,GAAI,MAAM,IAAM;AAElC,yBAAe;AAAA,QAEhB,OAAO;AAEN,mBAAU,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,MAAM,MAAQ;AAE7D,kBAAM,QAAQ,UAAW,EAAG,EAAE,MAAO,GAAI;AAEzC,gBAAK,MAAO,CAAE,MAAM,GAAK,cAAa,KAAM,MAAO,CAAE,CAAE;AACvD,gBAAK,MAAO,CAAE,MAAM,GAAK,SAAQ,KAAM,MAAO,CAAE,CAAE;AAAA,UAEnD;AAAA,QAED;AAEA,cAAM,gBAAiB,cAAc,OAAQ;AAAA,MAE9C,WAAY,kBAAkB,KAAM;AAEnC,cAAM,WAAW,KAAK,MAAO,CAAE,EAAE,KAAK;AACtC,cAAM,YAAY,SAAS,MAAO,GAAI;AAEtC,cAAM,iBAAkB,SAAU;AAAA,MAEnC,YAAc,SAAS,gBAAgB,KAAM,IAAK,OAAQ,MAAO;AAQhE,cAAM,QAAS,MAAM,OAAQ,CAAE,EAAE,MAAO,CAAE,EAAE,KAAK,GAAI,MAAO,CAAE;AAE9D,cAAM,YAAa,IAAK;AAAA,MAEzB,WAAY,sBAAsB,KAAM,IAAK,GAAI;AAIhD,cAAM,OAAO,cAAe,KAAK,UAAW,CAAE,EAAE,KAAK,GAAG,MAAM,iBAAkB;AAAA,MAEjF,WAAY,0BAA0B,KAAM,IAAK,GAAI;AAIpD,cAAM,kBAAkB,KAAM,KAAK,UAAW,CAAE,EAAE,KAAK,CAAE;AAAA,MAE1D,WAAY,iBAAiB,KAAM,IAAK,GAAI;AAK3C,gBAAQ,KAAM,sGAAuG;AAAA,MAEtH,WAAY,kBAAkB,KAAM;AAEnC,iBAAS,KAAK,MAAO,GAAI;AAoBzB,YAAK,OAAO,SAAS,GAAI;AAExB,gBAAM,QAAQ,OAAQ,CAAE,EAAE,KAAK,EAAE,YAAY;AAC7C,gBAAM,OAAO,SAAW,UAAU,OAAO,UAAU;AAAA,QAEpD,OAAO;AAGN,gBAAM,OAAO,SAAS;AAAA,QAEvB;AAEA,cAAM,WAAW,MAAM,OAAO,gBAAgB;AAC9C,YAAK,SAAW,UAAS,SAAS,MAAM,OAAO;AAAA,MAEhD,OAAO;AAGN,YAAK,SAAS,KAAO;AAErB,gBAAQ,KAAM,wCAAwC,OAAO,GAAI;AAAA,MAElE;AAAA,IAED;AAEA,UAAM,SAAS;AAEf,UAAM,YAAY,IAAI,MAAM;AAC5B,cAAU,oBAAoB,CAAC,EAAE,OAAQ,MAAM,iBAAkB;AAEjE,UAAM,gBAAgB,EAAI,MAAM,QAAQ,WAAW,KAAK,MAAM,QAAS,CAAE,EAAE,SAAS,SAAS,WAAW;AAExG,QAAK,kBAAkB,MAAO;AAE7B,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAO;AAExD,cAAM,SAAS,MAAM,QAAS,CAAE;AAChC,cAAM,WAAW,OAAO;AACxB,cAAM,YAAY,OAAO;AACzB,cAAM,SAAW,SAAS,SAAS;AACnC,cAAM,WAAa,SAAS,SAAS;AACrC,YAAI,kBAAkB;AAGtB,YAAK,SAAS,SAAS,WAAW,EAAI;AAEtC,cAAM,iBAAiB,IAAI,eAAe;AAE1C,uBAAe,aAAc,YAAY,IAAI,uBAAwB,SAAS,UAAU,CAAE,CAAE;AAE5F,YAAK,SAAS,QAAQ,SAAS,GAAI;AAElC,yBAAe,aAAc,UAAU,IAAI,uBAAwB,SAAS,SAAS,CAAE,CAAE;AAAA,QAE1F;AAEA,YAAK,SAAS,OAAO,SAAS,GAAI;AAEjC,4BAAkB;AAClB,yBAAe,aAAc,SAAS,IAAI,uBAAwB,SAAS,QAAQ,CAAE,CAAE;AAAA,QAExF;AAEA,YAAK,SAAS,iBAAiB,MAAO;AAErC,yBAAe,aAAc,MAAM,IAAI,uBAAwB,SAAS,KAAK,CAAE,CAAE;AAAA,QAElF;AAIA,cAAM,mBAAmB,CAAC;AAE1B,iBAAU,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAQ;AAE/D,gBAAM,iBAAiB,UAAW,EAAG;AACrC,gBAAM,eAAe,eAAe,OAAO,MAAM,eAAe,SAAS,MAAM;AAC/E,cAAI,WAAW,MAAM,UAAW,YAAa;AAE7C,cAAK,KAAK,cAAc,MAAO;AAE9B,uBAAW,KAAK,UAAU,OAAQ,eAAe,IAAK;AAGtD,gBAAK,UAAU,YAAY,EAAI,oBAAoB,oBAAsB;AAExE,oBAAM,eAAe,IAAI,kBAAkB;AAC3C,uBAAS,UAAU,KAAK,KAAM,cAAc,QAAS;AACrD,2BAAa,MAAM,KAAM,SAAS,KAAM;AACxC,yBAAW;AAAA,YAEZ,WAAY,YAAY,YAAY,EAAI,oBAAoB,iBAAmB;AAE9E,oBAAM,iBAAiB,IAAI,eAAgB,EAAE,MAAM,IAAI,iBAAiB,MAAM,CAAE;AAChF,uBAAS,UAAU,KAAK,KAAM,gBAAgB,QAAS;AACvD,6BAAe,MAAM,KAAM,SAAS,KAAM;AAC1C,6BAAe,MAAM,SAAS;AAC9B,yBAAW;AAAA,YAEZ;AAAA,UAED;AAEA,cAAK,aAAa,QAAY;AAE7B,gBAAK,QAAS;AAEb,yBAAW,IAAI,kBAAkB;AAAA,YAElC,WAAY,UAAW;AAEtB,yBAAW,IAAI,eAAgB,EAAE,MAAM,GAAG,iBAAiB,MAAM,CAAE;AAAA,YAEpE,OAAO;AAEN,yBAAW,IAAI,kBAAkB;AAAA,YAElC;AAEA,qBAAS,OAAO,eAAe;AAC/B,qBAAS,cAAc,eAAe,SAAS,QAAQ;AACvD,qBAAS,eAAe;AAExB,kBAAM,UAAW,YAAa,IAAI;AAAA,UAEnC;AAEA,2BAAiB,KAAM,QAAS;AAAA,QAEjC;AAIA,YAAI;AAEJ,YAAK,iBAAiB,SAAS,GAAI;AAElC,mBAAU,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAQ;AAE/D,kBAAM,iBAAiB,UAAW,EAAG;AACrC,2BAAe,SAAU,eAAe,YAAY,eAAe,YAAY,EAAG;AAAA,UAEnF;AAEA,cAAK,QAAS;AAEb,mBAAO,IAAI,aAAc,gBAAgB,gBAAiB;AAAA,UAE3D,WAAY,UAAW;AAEtB,mBAAO,IAAI,OAAQ,gBAAgB,gBAAiB;AAAA,UAErD,OAAO;AAEN,mBAAO,IAAI,KAAM,gBAAgB,gBAAiB;AAAA,UAEnD;AAAA,QAED,OAAO;AAEN,cAAK,QAAS;AAEb,mBAAO,IAAI,aAAc,gBAAgB,iBAAkB,CAAE,CAAE;AAAA,UAEhE,WAAY,UAAW;AAEtB,mBAAO,IAAI,OAAQ,gBAAgB,iBAAkB,CAAE,CAAE;AAAA,UAE1D,OAAO;AAEN,mBAAO,IAAI,KAAM,gBAAgB,iBAAkB,CAAE,CAAE;AAAA,UAExD;AAAA,QAED;AAEA,aAAK,OAAO,OAAO;AAEnB,kBAAU,IAAK,IAAK;AAAA,MAErB;AAAA,IAED,OAAO;AAIN,UAAK,MAAM,SAAS,SAAS,GAAI;AAEhC,cAAM,WAAW,IAAI,eAAgB,EAAE,MAAM,GAAG,iBAAiB,MAAM,CAAE;AAEzE,cAAM,iBAAiB,IAAI,eAAe;AAE1C,uBAAe,aAAc,YAAY,IAAI,uBAAwB,MAAM,UAAU,CAAE,CAAE;AAEzF,YAAK,MAAM,OAAO,SAAS,KAAK,MAAM,OAAQ,CAAE,MAAM,QAAY;AAEjE,yBAAe,aAAc,SAAS,IAAI,uBAAwB,MAAM,QAAQ,CAAE,CAAE;AACpF,mBAAS,eAAe;AAAA,QAEzB;AAEA,cAAM,SAAS,IAAI,OAAQ,gBAAgB,QAAS;AACpD,kBAAU,IAAK,MAAO;AAAA,MAEvB;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAED;", "names": ["name"]}