{"version": 3, "sources": ["../../three/examples/jsm/loaders/STLLoader.js"], "sourcesContent": ["import {\n\t<PERSON>uffer<PERSON>ttri<PERSON>e,\n\tBufferGeometry,\n\tColor,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tLoader,\n\tVector3\n} from 'three';\n\n/**\n * Description: A THREE loader for STL ASCII files, as created by Solidworks and other CAD programs.\n *\n * Supports both binary and ASCII encoded files, with automatic detection of type.\n *\n * The loader returns a non-indexed buffer geometry.\n *\n * Limitations:\n *  Binary decoding supports \"Magics\" color format (http://en.wikipedia.org/wiki/STL_(file_format)#Color_in_binary_STL).\n *  There is perhaps some question as to how valid it is to always assume little-endian-ness.\n *  ASCII decoding assumes file is UTF-8.\n *\n * Usage:\n *  const loader = new STLLoader();\n *  loader.load( './models/stl/slotted_disk.stl', function ( geometry ) {\n *    scene.add( new THREE.Mesh( geometry ) );\n *  });\n *\n * For binary STLs geometry might contain colors for vertices. To use it:\n *  // use the same code to load STL as above\n *  if (geometry.hasColors) {\n *    material = new THREE.MeshPhongMaterial({ opacity: geometry.alpha, vertexColors: true });\n *  } else { .... }\n *  const mesh = new THREE.Mesh( geometry, material );\n *\n * For ASCII STLs containing multiple solids, each solid is assigned to a different group.\n * Groups can be used to assign a different color by defining an array of materials with the same length of\n * geometry.groups and passing it to the Mesh constructor:\n *\n * const mesh = new THREE.Mesh( geometry, material );\n *\n * For example:\n *\n *  const materials = [];\n *  const nGeometryGroups = geometry.groups.length;\n *\n *  const colorMap = ...; // Some logic to index colors.\n *\n *  for (let i = 0; i < nGeometryGroups; i++) {\n *\n *\t\tconst material = new THREE.MeshPhongMaterial({\n *\t\t\tcolor: colorMap[i],\n *\t\t\twireframe: false\n *\t\t});\n *\n *  }\n *\n *  materials.push(material);\n *  const mesh = new THREE.Mesh(geometry, materials);\n */\n\n\nclass STLLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, function ( text ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( text ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tparse( data ) {\n\n\t\tfunction isBinary( data ) {\n\n\t\t\tconst reader = new DataView( data );\n\t\t\tconst face_size = ( 32 / 8 * 3 ) + ( ( 32 / 8 * 3 ) * 3 ) + ( 16 / 8 );\n\t\t\tconst n_faces = reader.getUint32( 80, true );\n\t\t\tconst expect = 80 + ( 32 / 8 ) + ( n_faces * face_size );\n\n\t\t\tif ( expect === reader.byteLength ) {\n\n\t\t\t\treturn true;\n\n\t\t\t}\n\n\t\t\t// An ASCII STL data must begin with 'solid ' as the first six bytes.\n\t\t\t// However, ASCII STLs lacking the SPACE after the 'd' are known to be\n\t\t\t// plentiful.  So, check the first 5 bytes for 'solid'.\n\n\t\t\t// Several encodings, such as UTF-8, precede the text with up to 5 bytes:\n\t\t\t// https://en.wikipedia.org/wiki/Byte_order_mark#Byte_order_marks_by_encoding\n\t\t\t// Search for \"solid\" to start anywhere after those prefixes.\n\n\t\t\t// US-ASCII ordinal values for 's', 'o', 'l', 'i', 'd'\n\n\t\t\tconst solid = [ 115, 111, 108, 105, 100 ];\n\n\t\t\tfor ( let off = 0; off < 5; off ++ ) {\n\n\t\t\t\t// If \"solid\" text is matched to the current offset, declare it to be an ASCII STL.\n\n\t\t\t\tif ( matchDataViewAt( solid, reader, off ) ) return false;\n\n\t\t\t}\n\n\t\t\t// Couldn't find \"solid\" text at the beginning; it is binary STL.\n\n\t\t\treturn true;\n\n\t\t}\n\n\t\tfunction matchDataViewAt( query, reader, offset ) {\n\n\t\t\t// Check if each byte in query matches the corresponding byte from the current offset\n\n\t\t\tfor ( let i = 0, il = query.length; i < il; i ++ ) {\n\n\t\t\t\tif ( query[ i ] !== reader.getUint8( offset + i ) ) return false;\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t\tfunction parseBinary( data ) {\n\n\t\t\tconst reader = new DataView( data );\n\t\t\tconst faces = reader.getUint32( 80, true );\n\n\t\t\tlet r, g, b, hasColors = false, colors;\n\t\t\tlet defaultR, defaultG, defaultB, alpha;\n\n\t\t\t// process STL header\n\t\t\t// check for default color in header (\"COLOR=rgba\" sequence).\n\n\t\t\tfor ( let index = 0; index < 80 - 10; index ++ ) {\n\n\t\t\t\tif ( ( reader.getUint32( index, false ) == 0x434F4C4F /*COLO*/ ) &&\n\t\t\t\t\t( reader.getUint8( index + 4 ) == 0x52 /*'R'*/ ) &&\n\t\t\t\t\t( reader.getUint8( index + 5 ) == 0x3D /*'='*/ ) ) {\n\n\t\t\t\t\thasColors = true;\n\t\t\t\t\tcolors = new Float32Array( faces * 3 * 3 );\n\n\t\t\t\t\tdefaultR = reader.getUint8( index + 6 ) / 255;\n\t\t\t\t\tdefaultG = reader.getUint8( index + 7 ) / 255;\n\t\t\t\t\tdefaultB = reader.getUint8( index + 8 ) / 255;\n\t\t\t\t\talpha = reader.getUint8( index + 9 ) / 255;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tconst dataOffset = 84;\n\t\t\tconst faceLength = 12 * 4 + 2;\n\n\t\t\tconst geometry = new BufferGeometry();\n\n\t\t\tconst vertices = new Float32Array( faces * 3 * 3 );\n\t\t\tconst normals = new Float32Array( faces * 3 * 3 );\n\n\t\t\tconst color = new Color();\n\n\t\t\tfor ( let face = 0; face < faces; face ++ ) {\n\n\t\t\t\tconst start = dataOffset + face * faceLength;\n\t\t\t\tconst normalX = reader.getFloat32( start, true );\n\t\t\t\tconst normalY = reader.getFloat32( start + 4, true );\n\t\t\t\tconst normalZ = reader.getFloat32( start + 8, true );\n\n\t\t\t\tif ( hasColors ) {\n\n\t\t\t\t\tconst packedColor = reader.getUint16( start + 48, true );\n\n\t\t\t\t\tif ( ( packedColor & 0x8000 ) === 0 ) {\n\n\t\t\t\t\t\t// facet has its own unique color\n\n\t\t\t\t\t\tr = ( packedColor & 0x1F ) / 31;\n\t\t\t\t\t\tg = ( ( packedColor >> 5 ) & 0x1F ) / 31;\n\t\t\t\t\t\tb = ( ( packedColor >> 10 ) & 0x1F ) / 31;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tr = defaultR;\n\t\t\t\t\t\tg = defaultG;\n\t\t\t\t\t\tb = defaultB;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tfor ( let i = 1; i <= 3; i ++ ) {\n\n\t\t\t\t\tconst vertexstart = start + i * 12;\n\t\t\t\t\tconst componentIdx = ( face * 3 * 3 ) + ( ( i - 1 ) * 3 );\n\n\t\t\t\t\tvertices[ componentIdx ] = reader.getFloat32( vertexstart, true );\n\t\t\t\t\tvertices[ componentIdx + 1 ] = reader.getFloat32( vertexstart + 4, true );\n\t\t\t\t\tvertices[ componentIdx + 2 ] = reader.getFloat32( vertexstart + 8, true );\n\n\t\t\t\t\tnormals[ componentIdx ] = normalX;\n\t\t\t\t\tnormals[ componentIdx + 1 ] = normalY;\n\t\t\t\t\tnormals[ componentIdx + 2 ] = normalZ;\n\n\t\t\t\t\tif ( hasColors ) {\n\n\t\t\t\t\t\tcolor.set( r, g, b ).convertSRGBToLinear();\n\n\t\t\t\t\t\tcolors[ componentIdx ] = color.r;\n\t\t\t\t\t\tcolors[ componentIdx + 1 ] = color.g;\n\t\t\t\t\t\tcolors[ componentIdx + 2 ] = color.b;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tgeometry.setAttribute( 'position', new BufferAttribute( vertices, 3 ) );\n\t\t\tgeometry.setAttribute( 'normal', new BufferAttribute( normals, 3 ) );\n\n\t\t\tif ( hasColors ) {\n\n\t\t\t\tgeometry.setAttribute( 'color', new BufferAttribute( colors, 3 ) );\n\t\t\t\tgeometry.hasColors = true;\n\t\t\t\tgeometry.alpha = alpha;\n\n\t\t\t}\n\n\t\t\treturn geometry;\n\n\t\t}\n\n\t\tfunction parseASCII( data ) {\n\n\t\t\tconst geometry = new BufferGeometry();\n\t\t\tconst patternSolid = /solid([\\s\\S]*?)endsolid/g;\n\t\t\tconst patternFace = /facet([\\s\\S]*?)endfacet/g;\n\t\t\tconst patternName = /solid\\s(.+)/;\n\t\t\tlet faceCounter = 0;\n\n\t\t\tconst patternFloat = /[\\s]+([+-]?(?:\\d*)(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)/.source;\n\t\t\tconst patternVertex = new RegExp( 'vertex' + patternFloat + patternFloat + patternFloat, 'g' );\n\t\t\tconst patternNormal = new RegExp( 'normal' + patternFloat + patternFloat + patternFloat, 'g' );\n\n\t\t\tconst vertices = [];\n\t\t\tconst normals = [];\n\t\t\tconst groupNames = [];\n\n\t\t\tconst normal = new Vector3();\n\n\t\t\tlet result;\n\n\t\t\tlet groupCount = 0;\n\t\t\tlet startVertex = 0;\n\t\t\tlet endVertex = 0;\n\n\t\t\twhile ( ( result = patternSolid.exec( data ) ) !== null ) {\n\n\t\t\t\tstartVertex = endVertex;\n\n\t\t\t\tconst solid = result[ 0 ];\n\n\t\t\t\tconst name = ( result = patternName.exec( solid ) ) !== null ? result[ 1 ] : '';\n\t\t\t\tgroupNames.push( name );\n\n\t\t\t\twhile ( ( result = patternFace.exec( solid ) ) !== null ) {\n\n\t\t\t\t\tlet vertexCountPerFace = 0;\n\t\t\t\t\tlet normalCountPerFace = 0;\n\n\t\t\t\t\tconst text = result[ 0 ];\n\n\t\t\t\t\twhile ( ( result = patternNormal.exec( text ) ) !== null ) {\n\n\t\t\t\t\t\tnormal.x = parseFloat( result[ 1 ] );\n\t\t\t\t\t\tnormal.y = parseFloat( result[ 2 ] );\n\t\t\t\t\t\tnormal.z = parseFloat( result[ 3 ] );\n\t\t\t\t\t\tnormalCountPerFace ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t\twhile ( ( result = patternVertex.exec( text ) ) !== null ) {\n\n\t\t\t\t\t\tvertices.push( parseFloat( result[ 1 ] ), parseFloat( result[ 2 ] ), parseFloat( result[ 3 ] ) );\n\t\t\t\t\t\tnormals.push( normal.x, normal.y, normal.z );\n\t\t\t\t\t\tvertexCountPerFace ++;\n\t\t\t\t\t\tendVertex ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// every face have to own ONE valid normal\n\n\t\t\t\t\tif ( normalCountPerFace !== 1 ) {\n\n\t\t\t\t\t\tconsole.error( 'THREE.STLLoader: Something isn\\'t right with the normal of face number ' + faceCounter );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// each face have to own THREE valid vertices\n\n\t\t\t\t\tif ( vertexCountPerFace !== 3 ) {\n\n\t\t\t\t\t\tconsole.error( 'THREE.STLLoader: Something isn\\'t right with the vertices of face number ' + faceCounter );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfaceCounter ++;\n\n\t\t\t\t}\n\n\t\t\t\tconst start = startVertex;\n\t\t\t\tconst count = endVertex - startVertex;\n\n\t\t\t\tgeometry.userData.groupNames = groupNames;\n\n\t\t\t\tgeometry.addGroup( start, count, groupCount );\n\t\t\t\tgroupCount ++;\n\n\t\t\t}\n\n\t\t\tgeometry.setAttribute( 'position', new Float32BufferAttribute( vertices, 3 ) );\n\t\t\tgeometry.setAttribute( 'normal', new Float32BufferAttribute( normals, 3 ) );\n\n\t\t\treturn geometry;\n\n\t\t}\n\n\t\tfunction ensureString( buffer ) {\n\n\t\t\tif ( typeof buffer !== 'string' ) {\n\n\t\t\t\treturn new TextDecoder().decode( buffer );\n\n\t\t\t}\n\n\t\t\treturn buffer;\n\n\t\t}\n\n\t\tfunction ensureBinary( buffer ) {\n\n\t\t\tif ( typeof buffer === 'string' ) {\n\n\t\t\t\tconst array_buffer = new Uint8Array( buffer.length );\n\t\t\t\tfor ( let i = 0; i < buffer.length; i ++ ) {\n\n\t\t\t\t\tarray_buffer[ i ] = buffer.charCodeAt( i ) & 0xff; // implicitly assumes little-endian\n\n\t\t\t\t}\n\n\t\t\t\treturn array_buffer.buffer || array_buffer;\n\n\t\t\t} else {\n\n\t\t\t\treturn buffer;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// start\n\n\t\tconst binData = ensureBinary( data );\n\n\t\treturn isBinary( binData ) ? parseBinary( binData ) : parseASCII( ensureString( data ) );\n\n\t}\n\n}\n\nexport { STLLoader };\n"], "mappings": ";;;;;;;;;;;AA8DA,IAAM,YAAN,cAAwB,OAAO;AAAA,EAE9B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAAA,EAEhB;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAEhD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,eAAQ,MAAM,MAAO,IAAK,CAAE;AAAA,MAE7B,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,MAAO,MAAO;AAEb,aAAS,SAAUA,OAAO;AAEzB,YAAM,SAAS,IAAI,SAAUA,KAAK;AAClC,YAAM,YAAc,KAAK,IAAI,IAAU,KAAK,IAAI,IAAM,IAAQ,KAAK;AACnE,YAAM,UAAU,OAAO,UAAW,IAAI,IAAK;AAC3C,YAAM,SAAS,KAAO,KAAK,IAAQ,UAAU;AAE7C,UAAK,WAAW,OAAO,YAAa;AAEnC,eAAO;AAAA,MAER;AAYA,YAAM,QAAQ,CAAE,KAAK,KAAK,KAAK,KAAK,GAAI;AAExC,eAAU,MAAM,GAAG,MAAM,GAAG,OAAS;AAIpC,YAAK,gBAAiB,OAAO,QAAQ,GAAI,EAAI,QAAO;AAAA,MAErD;AAIA,aAAO;AAAA,IAER;AAEA,aAAS,gBAAiB,OAAO,QAAQ,QAAS;AAIjD,eAAU,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAO;AAElD,YAAK,MAAO,CAAE,MAAM,OAAO,SAAU,SAAS,CAAE,EAAI,QAAO;AAAA,MAE5D;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,YAAaA,OAAO;AAE5B,YAAM,SAAS,IAAI,SAAUA,KAAK;AAClC,YAAM,QAAQ,OAAO,UAAW,IAAI,IAAK;AAEzC,UAAI,GAAG,GAAG,GAAG,YAAY,OAAO;AAChC,UAAI,UAAU,UAAU,UAAU;AAKlC,eAAU,QAAQ,GAAG,QAAQ,KAAK,IAAI,SAAW;AAEhD,YAAO,OAAO,UAAW,OAAO,KAAM,KAAK,cACxC,OAAO,SAAU,QAAQ,CAAE,KAAK,MAChC,OAAO,SAAU,QAAQ,CAAE,KAAK,IAAiB;AAEnD,sBAAY;AACZ,mBAAS,IAAI,aAAc,QAAQ,IAAI,CAAE;AAEzC,qBAAW,OAAO,SAAU,QAAQ,CAAE,IAAI;AAC1C,qBAAW,OAAO,SAAU,QAAQ,CAAE,IAAI;AAC1C,qBAAW,OAAO,SAAU,QAAQ,CAAE,IAAI;AAC1C,kBAAQ,OAAO,SAAU,QAAQ,CAAE,IAAI;AAAA,QAExC;AAAA,MAED;AAEA,YAAM,aAAa;AACnB,YAAM,aAAa,KAAK,IAAI;AAE5B,YAAM,WAAW,IAAI,eAAe;AAEpC,YAAM,WAAW,IAAI,aAAc,QAAQ,IAAI,CAAE;AACjD,YAAM,UAAU,IAAI,aAAc,QAAQ,IAAI,CAAE;AAEhD,YAAM,QAAQ,IAAI,MAAM;AAExB,eAAU,OAAO,GAAG,OAAO,OAAO,QAAU;AAE3C,cAAM,QAAQ,aAAa,OAAO;AAClC,cAAM,UAAU,OAAO,WAAY,OAAO,IAAK;AAC/C,cAAM,UAAU,OAAO,WAAY,QAAQ,GAAG,IAAK;AACnD,cAAM,UAAU,OAAO,WAAY,QAAQ,GAAG,IAAK;AAEnD,YAAK,WAAY;AAEhB,gBAAM,cAAc,OAAO,UAAW,QAAQ,IAAI,IAAK;AAEvD,eAAO,cAAc,WAAa,GAAI;AAIrC,iBAAM,cAAc,MAAS;AAC7B,iBAAQ,eAAe,IAAM,MAAS;AACtC,iBAAQ,eAAe,KAAO,MAAS;AAAA,UAExC,OAAO;AAEN,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AAAA,UAEL;AAAA,QAED;AAEA,iBAAU,IAAI,GAAG,KAAK,GAAG,KAAO;AAE/B,gBAAM,cAAc,QAAQ,IAAI;AAChC,gBAAM,eAAiB,OAAO,IAAI,KAAU,IAAI,KAAM;AAEtD,mBAAU,YAAa,IAAI,OAAO,WAAY,aAAa,IAAK;AAChE,mBAAU,eAAe,CAAE,IAAI,OAAO,WAAY,cAAc,GAAG,IAAK;AACxE,mBAAU,eAAe,CAAE,IAAI,OAAO,WAAY,cAAc,GAAG,IAAK;AAExE,kBAAS,YAAa,IAAI;AAC1B,kBAAS,eAAe,CAAE,IAAI;AAC9B,kBAAS,eAAe,CAAE,IAAI;AAE9B,cAAK,WAAY;AAEhB,kBAAM,IAAK,GAAG,GAAG,CAAE,EAAE,oBAAoB;AAEzC,mBAAQ,YAAa,IAAI,MAAM;AAC/B,mBAAQ,eAAe,CAAE,IAAI,MAAM;AACnC,mBAAQ,eAAe,CAAE,IAAI,MAAM;AAAA,UAEpC;AAAA,QAED;AAAA,MAED;AAEA,eAAS,aAAc,YAAY,IAAI,gBAAiB,UAAU,CAAE,CAAE;AACtE,eAAS,aAAc,UAAU,IAAI,gBAAiB,SAAS,CAAE,CAAE;AAEnE,UAAK,WAAY;AAEhB,iBAAS,aAAc,SAAS,IAAI,gBAAiB,QAAQ,CAAE,CAAE;AACjE,iBAAS,YAAY;AACrB,iBAAS,QAAQ;AAAA,MAElB;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,WAAYA,OAAO;AAE3B,YAAM,WAAW,IAAI,eAAe;AACpC,YAAM,eAAe;AACrB,YAAM,cAAc;AACpB,YAAM,cAAc;AACpB,UAAI,cAAc;AAElB,YAAM,eAAe,iDAAiD;AACtE,YAAM,gBAAgB,IAAI,OAAQ,WAAW,eAAe,eAAe,cAAc,GAAI;AAC7F,YAAM,gBAAgB,IAAI,OAAQ,WAAW,eAAe,eAAe,cAAc,GAAI;AAE7F,YAAM,WAAW,CAAC;AAClB,YAAM,UAAU,CAAC;AACjB,YAAM,aAAa,CAAC;AAEpB,YAAM,SAAS,IAAI,QAAQ;AAE3B,UAAI;AAEJ,UAAI,aAAa;AACjB,UAAI,cAAc;AAClB,UAAI,YAAY;AAEhB,cAAU,SAAS,aAAa,KAAMA,KAAK,OAAQ,MAAO;AAEzD,sBAAc;AAEd,cAAM,QAAQ,OAAQ,CAAE;AAExB,cAAM,QAAS,SAAS,YAAY,KAAM,KAAM,OAAQ,OAAO,OAAQ,CAAE,IAAI;AAC7E,mBAAW,KAAM,IAAK;AAEtB,gBAAU,SAAS,YAAY,KAAM,KAAM,OAAQ,MAAO;AAEzD,cAAI,qBAAqB;AACzB,cAAI,qBAAqB;AAEzB,gBAAM,OAAO,OAAQ,CAAE;AAEvB,kBAAU,SAAS,cAAc,KAAM,IAAK,OAAQ,MAAO;AAE1D,mBAAO,IAAI,WAAY,OAAQ,CAAE,CAAE;AACnC,mBAAO,IAAI,WAAY,OAAQ,CAAE,CAAE;AACnC,mBAAO,IAAI,WAAY,OAAQ,CAAE,CAAE;AACnC;AAAA,UAED;AAEA,kBAAU,SAAS,cAAc,KAAM,IAAK,OAAQ,MAAO;AAE1D,qBAAS,KAAM,WAAY,OAAQ,CAAE,CAAE,GAAG,WAAY,OAAQ,CAAE,CAAE,GAAG,WAAY,OAAQ,CAAE,CAAE,CAAE;AAC/F,oBAAQ,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAC3C;AACA;AAAA,UAED;AAIA,cAAK,uBAAuB,GAAI;AAE/B,oBAAQ,MAAO,2EAA4E,WAAY;AAAA,UAExG;AAIA,cAAK,uBAAuB,GAAI;AAE/B,oBAAQ,MAAO,6EAA8E,WAAY;AAAA,UAE1G;AAEA;AAAA,QAED;AAEA,cAAM,QAAQ;AACd,cAAM,QAAQ,YAAY;AAE1B,iBAAS,SAAS,aAAa;AAE/B,iBAAS,SAAU,OAAO,OAAO,UAAW;AAC5C;AAAA,MAED;AAEA,eAAS,aAAc,YAAY,IAAI,uBAAwB,UAAU,CAAE,CAAE;AAC7E,eAAS,aAAc,UAAU,IAAI,uBAAwB,SAAS,CAAE,CAAE;AAE1E,aAAO;AAAA,IAER;AAEA,aAAS,aAAc,QAAS;AAE/B,UAAK,OAAO,WAAW,UAAW;AAEjC,eAAO,IAAI,YAAY,EAAE,OAAQ,MAAO;AAAA,MAEzC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,aAAc,QAAS;AAE/B,UAAK,OAAO,WAAW,UAAW;AAEjC,cAAM,eAAe,IAAI,WAAY,OAAO,MAAO;AACnD,iBAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAE1C,uBAAc,CAAE,IAAI,OAAO,WAAY,CAAE,IAAI;AAAA,QAE9C;AAEA,eAAO,aAAa,UAAU;AAAA,MAE/B,OAAO;AAEN,eAAO;AAAA,MAER;AAAA,IAED;AAIA,UAAM,UAAU,aAAc,IAAK;AAEnC,WAAO,SAAU,OAAQ,IAAI,YAAa,OAAQ,IAAI,WAAY,aAAc,IAAK,CAAE;AAAA,EAExF;AAED;", "names": ["data"]}