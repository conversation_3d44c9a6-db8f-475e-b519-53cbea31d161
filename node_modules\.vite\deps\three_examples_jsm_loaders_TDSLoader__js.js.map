{"version": 3, "sources": ["../../three/examples/jsm/loaders/TDSLoader.js"], "sourcesContent": ["import {\n\tAdditiveBlending,\n\tBufferGeometry,\n\tColor,\n\tDoubleSide,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tGroup,\n\tLoader,\n\tLoaderUtils,\n\tMatrix4,\n\tMesh,\n\tMeshPhongMaterial,\n\tTextureLoader\n} from 'three';\n\n/**\n * Autodesk 3DS three.js file loader, based on lib3ds.\n *\n * Loads geometry with uv and materials basic properties with texture support.\n *\n * @class TDSLoader\n * @constructor\n */\n\nclass TDSLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.debug = false;\n\n\t\tthis.group = null;\n\n\t\tthis.materials = [];\n\t\tthis.meshes = [];\n\n\t}\n\n\t/**\n\t * Load 3ds file from url.\n\t *\n\t * @method load\n\t * @param {[type]} url URL for the file.\n\t * @param {Function} onLoad onLoad callback, receives group Object3D as argument.\n\t * @param {Function} onProgress onProgress callback.\n\t * @param {Function} onError onError callback.\n\t */\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst path = ( this.path === '' ) ? LoaderUtils.extractUrlBase( url ) : this.path;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, function ( data ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( data, path ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\t/**\n\t * Parse arraybuffer data and load 3ds file.\n\t *\n\t * @method parse\n\t * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n\t * @param {String} path Path for external resources.\n\t * @return {Group} Group loaded from 3ds file.\n\t */\n\tparse( arraybuffer, path ) {\n\n\t\tthis.group = new Group();\n\t\tthis.materials = [];\n\t\tthis.meshes = [];\n\n\t\tthis.readFile( arraybuffer, path );\n\n\t\tfor ( let i = 0; i < this.meshes.length; i ++ ) {\n\n\t\t\tthis.group.add( this.meshes[ i ] );\n\n\t\t}\n\n\t\treturn this.group;\n\n\t}\n\n\t/**\n\t * Decode file content to read 3ds data.\n\t *\n\t * @method readFile\n\t * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n\t * @param {String} path Path for external resources.\n\t */\n\treadFile( arraybuffer, path ) {\n\n\t\tconst data = new DataView( arraybuffer );\n\t\tconst chunk = new Chunk( data, 0, this.debugMessage );\n\n\t\tif ( chunk.id === MLIBMAGIC || chunk.id === CMAGIC || chunk.id === M3DMAGIC ) {\n\n\t\t\tlet next = chunk.readChunk();\n\n\t\t\twhile ( next ) {\n\n\t\t\t\tif ( next.id === M3D_VERSION ) {\n\n\t\t\t\t\tconst version = next.readDWord();\n\t\t\t\t\tthis.debugMessage( '3DS file version: ' + version );\n\n\t\t\t\t} else if ( next.id === MDATA ) {\n\n\t\t\t\t\tthis.readMeshData( next, path );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tthis.debugMessage( 'Unknown main chunk: ' + next.hexId );\n\n\t\t\t\t}\n\n\t\t\t\tnext = chunk.readChunk();\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.debugMessage( 'Parsed ' + this.meshes.length + ' meshes' );\n\n\t}\n\n\t/**\n\t * Read mesh data chunk.\n\t *\n\t * @method readMeshData\n\t * @param {Chunk} chunk to read mesh from\n\t * @param {String} path Path for external resources.\n\t */\n\treadMeshData( chunk, path ) {\n\n\t\tlet next = chunk.readChunk();\n\n\t\twhile ( next ) {\n\n\t\t\tif ( next.id === MESH_VERSION ) {\n\n\t\t\t\tconst version = + next.readDWord();\n\t\t\t\tthis.debugMessage( 'Mesh Version: ' + version );\n\n\t\t\t} else if ( next.id === MASTER_SCALE ) {\n\n\t\t\t\tconst scale = next.readFloat();\n\t\t\t\tthis.debugMessage( 'Master scale: ' + scale );\n\t\t\t\tthis.group.scale.set( scale, scale, scale );\n\n\t\t\t} else if ( next.id === NAMED_OBJECT ) {\n\n\t\t\t\tthis.debugMessage( 'Named Object' );\n\t\t\t\tthis.readNamedObject( next );\n\n\t\t\t} else if ( next.id === MAT_ENTRY ) {\n\n\t\t\t\tthis.debugMessage( 'Material' );\n\t\t\t\tthis.readMaterialEntry( next, path );\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( 'Unknown MDATA chunk: ' + next.hexId );\n\n\t\t\t}\n\n\t\t\tnext = chunk.readChunk();\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Read named object chunk.\n\t *\n\t * @method readNamedObject\n\t * @param {Chunk} chunk Chunk in use.\n\t */\n\treadNamedObject( chunk ) {\n\n\t\tconst name = chunk.readString();\n\n\t\tlet next = chunk.readChunk();\n\t\twhile ( next ) {\n\n\t\t\tif ( next.id === N_TRI_OBJECT ) {\n\n\t\t\t\tconst mesh = this.readMesh( next );\n\t\t\t\tmesh.name = name;\n\t\t\t\tthis.meshes.push( mesh );\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( 'Unknown named object chunk: ' + next.hexId );\n\n\t\t\t}\n\n\t\t\tnext = chunk.readChunk( );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Read material data chunk and add it to the material list.\n\t *\n\t * @method readMaterialEntry\n\t * @param {Chunk} chunk Chunk in use.\n\t * @param {String} path Path for external resources.\n\t */\n\treadMaterialEntry( chunk, path ) {\n\n\t\tlet next = chunk.readChunk();\n\t\tconst material = new MeshPhongMaterial();\n\n\t\twhile ( next ) {\n\n\t\t\tif ( next.id === MAT_NAME ) {\n\n\t\t\t\tmaterial.name = next.readString();\n\t\t\t\tthis.debugMessage( '   Name: ' + material.name );\n\n\t\t\t} else if ( next.id === MAT_WIRE ) {\n\n\t\t\t\tthis.debugMessage( '   Wireframe' );\n\t\t\t\tmaterial.wireframe = true;\n\n\t\t\t} else if ( next.id === MAT_WIRE_SIZE ) {\n\n\t\t\t\tconst value = next.readByte();\n\t\t\t\tmaterial.wireframeLinewidth = value;\n\t\t\t\tthis.debugMessage( '   Wireframe Thickness: ' + value );\n\n\t\t\t} else if ( next.id === MAT_TWO_SIDE ) {\n\n\t\t\t\tmaterial.side = DoubleSide;\n\t\t\t\tthis.debugMessage( '   DoubleSided' );\n\n\t\t\t} else if ( next.id === MAT_ADDITIVE ) {\n\n\t\t\t\tthis.debugMessage( '   Additive Blending' );\n\t\t\t\tmaterial.blending = AdditiveBlending;\n\n\t\t\t} else if ( next.id === MAT_DIFFUSE ) {\n\n\t\t\t\tthis.debugMessage( '   Diffuse Color' );\n\t\t\t\tmaterial.color = this.readColor( next );\n\n\t\t\t} else if ( next.id === MAT_SPECULAR ) {\n\n\t\t\t\tthis.debugMessage( '   Specular Color' );\n\t\t\t\tmaterial.specular = this.readColor( next );\n\n\t\t\t} else if ( next.id === MAT_AMBIENT ) {\n\n\t\t\t\tthis.debugMessage( '   Ambient color' );\n\t\t\t\tmaterial.color = this.readColor( next );\n\n\t\t\t} else if ( next.id === MAT_SHININESS ) {\n\n\t\t\t\tconst shininess = this.readPercentage( next );\n\t\t\t\tmaterial.shininess = shininess * 100;\n\t\t\t\tthis.debugMessage( '   Shininess : ' + shininess );\n\n\t\t\t} else if ( next.id === MAT_TRANSPARENCY ) {\n\n\t\t\t\tconst transparency = this.readPercentage( next );\n\t\t\t\tmaterial.opacity = 1 - transparency;\n\t\t\t\tthis.debugMessage( '  Transparency : ' + transparency );\n\t\t\t\tmaterial.transparent = material.opacity < 1 ? true : false;\n\n\t\t\t} else if ( next.id === MAT_TEXMAP ) {\n\n\t\t\t\tthis.debugMessage( '   ColorMap' );\n\t\t\t\tmaterial.map = this.readMap( next, path );\n\n\t\t\t} else if ( next.id === MAT_BUMPMAP ) {\n\n\t\t\t\tthis.debugMessage( '   BumpMap' );\n\t\t\t\tmaterial.bumpMap = this.readMap( next, path );\n\n\t\t\t} else if ( next.id === MAT_OPACMAP ) {\n\n\t\t\t\tthis.debugMessage( '   OpacityMap' );\n\t\t\t\tmaterial.alphaMap = this.readMap( next, path );\n\n\t\t\t} else if ( next.id === MAT_SPECMAP ) {\n\n\t\t\t\tthis.debugMessage( '   SpecularMap' );\n\t\t\t\tmaterial.specularMap = this.readMap( next, path );\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( '   Unknown material chunk: ' + next.hexId );\n\n\t\t\t}\n\n\t\t\tnext = chunk.readChunk();\n\n\t\t}\n\n\t\tthis.materials[ material.name ] = material;\n\n\t}\n\n\t/**\n\t * Read mesh data chunk.\n\t *\n\t * @method readMesh\n\t * @param {Chunk} chunk Chunk in use.\n\t * @return {Mesh} The parsed mesh.\n\t */\n\treadMesh( chunk ) {\n\n\t\tlet next = chunk.readChunk( );\n\n\t\tconst geometry = new BufferGeometry();\n\n\t\tconst material = new MeshPhongMaterial();\n\t\tconst mesh = new Mesh( geometry, material );\n\t\tmesh.name = 'mesh';\n\n\t\twhile ( next ) {\n\n\t\t\tif ( next.id === POINT_ARRAY ) {\n\n\t\t\t\tconst points = next.readWord( );\n\n\t\t\t\tthis.debugMessage( '   Vertex: ' + points );\n\n\t\t\t\t//BufferGeometry\n\n\t\t\t\tconst vertices = [];\n\n\t\t\t\tfor ( let i = 0; i < points; i ++ )\t\t{\n\n\t\t\t\t\tvertices.push( next.readFloat( ) );\n\t\t\t\t\tvertices.push( next.readFloat( ) );\n\t\t\t\t\tvertices.push( next.readFloat( ) );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.setAttribute( 'position', new Float32BufferAttribute( vertices, 3 ) );\n\n\t\t\t} else if ( next.id === FACE_ARRAY ) {\n\n\t\t\t\tthis.readFaceArray( next, mesh );\n\n\t\t\t} else if ( next.id === TEX_VERTS ) {\n\n\t\t\t\tconst texels = next.readWord( );\n\n\t\t\t\tthis.debugMessage( '   UV: ' + texels );\n\n\t\t\t\t//BufferGeometry\n\n\t\t\t\tconst uvs = [];\n\n\t\t\t\tfor ( let i = 0; i < texels; i ++ ) {\n\n\t\t\t\t\tuvs.push( next.readFloat( ) );\n\t\t\t\t\tuvs.push( next.readFloat( ) );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.setAttribute( 'uv', new Float32BufferAttribute( uvs, 2 ) );\n\n\n\t\t\t} else if ( next.id === MESH_MATRIX ) {\n\n\t\t\t\tthis.debugMessage( '   Tranformation Matrix (TODO)' );\n\n\t\t\t\tconst values = [];\n\t\t\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\t\t\tvalues[ i ] = next.readFloat( );\n\n\t\t\t\t}\n\n\t\t\t\tconst matrix = new Matrix4();\n\n\t\t\t\t//X Line\n\t\t\t\tmatrix.elements[ 0 ] = values[ 0 ];\n\t\t\t\tmatrix.elements[ 1 ] = values[ 6 ];\n\t\t\t\tmatrix.elements[ 2 ] = values[ 3 ];\n\t\t\t\tmatrix.elements[ 3 ] = values[ 9 ];\n\n\t\t\t\t//Y Line\n\t\t\t\tmatrix.elements[ 4 ] = values[ 2 ];\n\t\t\t\tmatrix.elements[ 5 ] = values[ 8 ];\n\t\t\t\tmatrix.elements[ 6 ] = values[ 5 ];\n\t\t\t\tmatrix.elements[ 7 ] = values[ 11 ];\n\n\t\t\t\t//Z Line\n\t\t\t\tmatrix.elements[ 8 ] = values[ 1 ];\n\t\t\t\tmatrix.elements[ 9 ] = values[ 7 ];\n\t\t\t\tmatrix.elements[ 10 ] = values[ 4 ];\n\t\t\t\tmatrix.elements[ 11 ] = values[ 10 ];\n\n\t\t\t\t//W Line\n\t\t\t\tmatrix.elements[ 12 ] = 0;\n\t\t\t\tmatrix.elements[ 13 ] = 0;\n\t\t\t\tmatrix.elements[ 14 ] = 0;\n\t\t\t\tmatrix.elements[ 15 ] = 1;\n\n\t\t\t\tmatrix.transpose();\n\n\t\t\t\tconst inverse = new Matrix4();\n\t\t\t\tinverse.copy( matrix ).invert();\n\t\t\t\tgeometry.applyMatrix4( inverse );\n\n\t\t\t\tmatrix.decompose( mesh.position, mesh.quaternion, mesh.scale );\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( '   Unknown mesh chunk: ' + next.hexId );\n\n\t\t\t}\n\n\t\t\tnext = chunk.readChunk( );\n\n\t\t}\n\n\t\tgeometry.computeVertexNormals();\n\n\t\treturn mesh;\n\n\t}\n\n\t/**\n\t * Read face array data chunk.\n\t *\n\t * @method readFaceArray\n\t * @param {Chunk} chunk Chunk in use.\n\t * @param {Mesh} mesh Mesh to be filled with the data read.\n\t */\n\treadFaceArray( chunk, mesh ) {\n\n\t\tconst faces = chunk.readWord( );\n\n\t\tthis.debugMessage( '   Faces: ' + faces );\n\n\t\tconst index = [];\n\n\t\tfor ( let i = 0; i < faces; ++ i ) {\n\n\t\t\tindex.push( chunk.readWord( ), chunk.readWord( ), chunk.readWord( ) );\n\n\t\t\tchunk.readWord( ); // visibility\n\n\t\t}\n\n\t\tmesh.geometry.setIndex( index );\n\n\t\t//The rest of the FACE_ARRAY chunk is subchunks\n\n\t\tlet materialIndex = 0;\n\t\tlet start = 0;\n\n\t\twhile ( ! chunk.endOfChunk ) {\n\n\t\t\tconst subchunk = chunk.readChunk( );\n\n\t\t\tif ( subchunk.id === MSH_MAT_GROUP ) {\n\n\t\t\t\tthis.debugMessage( '      Material Group' );\n\n\t\t\t\tconst group = this.readMaterialGroup( subchunk );\n\t\t\t\tconst count = group.index.length * 3; // assuming successive indices\n\n\t\t\t\tmesh.geometry.addGroup( start, count, materialIndex );\n\n\t\t\t\tstart += count;\n\t\t\t\tmaterialIndex ++;\n\n\t\t\t\tconst material = this.materials[ group.name ];\n\n\t\t\t\tif ( Array.isArray( mesh.material ) === false ) mesh.material = [];\n\n\t\t\t\tif ( material !== undefined )\t{\n\n\t\t\t\t\tmesh.material.push( material );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( '      Unknown face array chunk: ' + subchunk.hexId );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( mesh.material.length === 1 ) mesh.material = mesh.material[ 0 ]; // for backwards compatibility\n\n\t}\n\n\t/**\n\t * Read texture map data chunk.\n\t *\n\t * @method readMap\n\t * @param {Chunk} chunk Chunk in use.\n\t * @param {String} path Path for external resources.\n\t * @return {Texture} Texture read from this data chunk.\n\t */\n\treadMap( chunk, path ) {\n\n\t\tlet next = chunk.readChunk( );\n\t\tlet texture = {};\n\n\t\tconst loader = new TextureLoader( this.manager );\n\t\tloader.setPath( this.resourcePath || path ).setCrossOrigin( this.crossOrigin );\n\n\t\twhile ( next ) {\n\n\t\t\tif ( next.id === MAT_MAPNAME ) {\n\n\t\t\t\tconst name = next.readString();\n\t\t\t\ttexture = loader.load( name );\n\n\t\t\t\tthis.debugMessage( '      File: ' + path + name );\n\n\t\t\t} else if ( next.id === MAT_MAP_UOFFSET ) {\n\n\t\t\t\ttexture.offset.x = next.readFloat( );\n\t\t\t\tthis.debugMessage( '      OffsetX: ' + texture.offset.x );\n\n\t\t\t} else if ( next.id === MAT_MAP_VOFFSET ) {\n\n\t\t\t\ttexture.offset.y = next.readFloat( );\n\t\t\t\tthis.debugMessage( '      OffsetY: ' + texture.offset.y );\n\n\t\t\t} else if ( next.id === MAT_MAP_USCALE ) {\n\n\t\t\t\ttexture.repeat.x = next.readFloat( );\n\t\t\t\tthis.debugMessage( '      RepeatX: ' + texture.repeat.x );\n\n\t\t\t} else if ( next.id === MAT_MAP_VSCALE ) {\n\n\t\t\t\ttexture.repeat.y = next.readFloat( );\n\t\t\t\tthis.debugMessage( '      RepeatY: ' + texture.repeat.y );\n\n\t\t\t} else {\n\n\t\t\t\tthis.debugMessage( '      Unknown map chunk: ' + next.hexId );\n\n\t\t\t}\n\n\t\t\tnext = chunk.readChunk( );\n\n\t\t}\n\n\t\treturn texture;\n\n\t}\n\n\t/**\n\t * Read material group data chunk.\n\t *\n\t * @method readMaterialGroup\n\t * @param {Chunk} chunk Chunk in use.\n\t * @return {Object} Object with name and index of the object.\n\t */\n\treadMaterialGroup( chunk ) {\n\n\t\tconst name = chunk.readString();\n\t\tconst numFaces = chunk.readWord();\n\n\t\tthis.debugMessage( '         Name: ' + name );\n\t\tthis.debugMessage( '         Faces: ' + numFaces );\n\n\t\tconst index = [];\n\t\tfor ( let i = 0; i < numFaces; ++ i ) {\n\n\t\t\tindex.push( chunk.readWord( ) );\n\n\t\t}\n\n\t\treturn { name: name, index: index };\n\n\t}\n\n\t/**\n\t * Read a color value.\n\t *\n\t * @method readColor\n\t * @param {Chunk} chunk Chunk.\n\t * @return {Color} Color value read..\n\t */\n\treadColor( chunk ) {\n\n\t\tconst subChunk = chunk.readChunk( );\n\t\tconst color = new Color();\n\n\t\tif ( subChunk.id === COLOR_24 || subChunk.id === LIN_COLOR_24 ) {\n\n\t\t\tconst r = subChunk.readByte( );\n\t\t\tconst g = subChunk.readByte( );\n\t\t\tconst b = subChunk.readByte( );\n\n\t\t\tcolor.setRGB( r / 255, g / 255, b / 255 );\n\n\t\t\tthis.debugMessage( '      Color: ' + color.r + ', ' + color.g + ', ' + color.b );\n\n\t\t}\telse if ( subChunk.id === COLOR_F || subChunk.id === LIN_COLOR_F ) {\n\n\t\t\tconst r = subChunk.readFloat( );\n\t\t\tconst g = subChunk.readFloat( );\n\t\t\tconst b = subChunk.readFloat( );\n\n\t\t\tcolor.setRGB( r, g, b );\n\n\t\t\tthis.debugMessage( '      Color: ' + color.r + ', ' + color.g + ', ' + color.b );\n\n\t\t}\telse {\n\n\t\t\tthis.debugMessage( '      Unknown color chunk: ' + subChunk.hexId );\n\n\t\t}\n\n\t\treturn color;\n\n\t}\n\n\t/**\n\t * Read percentage value.\n\t *\n\t * @method readPercentage\n\t * @param {Chunk} chunk Chunk to read data from.\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadPercentage( chunk ) {\n\n\t\tconst subChunk = chunk.readChunk( );\n\n\t\tswitch ( subChunk.id ) {\n\n\t\t\tcase INT_PERCENTAGE:\n\t\t\t\treturn ( subChunk.readShort( ) / 100 );\n\t\t\t\tbreak;\n\n\t\t\tcase FLOAT_PERCENTAGE:\n\t\t\t\treturn subChunk.readFloat( );\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tthis.debugMessage( '      Unknown percentage chunk: ' + subChunk.hexId );\n\t\t\t\treturn 0;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Print debug message to the console.\n\t *\n\t * Is controlled by a flag to show or hide debug messages.\n\t *\n\t * @method debugMessage\n\t * @param {Object} message Debug message to print to the console.\n\t */\n\tdebugMessage( message ) {\n\n\t\tif ( this.debug ) {\n\n\t\t\tconsole.log( message );\n\n\t\t}\n\n\t}\n\n}\n\n\n/** Read data/sub-chunks from chunk */\nclass Chunk {\n\n\t/**\n\t * Create a new chunk\n\t *\n\t * @class Chunk\n\t * @param {DataView} data DataView to read from.\n\t * @param {Number} position in data.\n\t * @param {Function} debugMessage logging callback.\n\t */\n\tconstructor( data, position, debugMessage ) {\n\n\t\tthis.data = data;\n\t\t// the offset to the begin of this chunk\n\t\tthis.offset = position;\n\t\t// the current reading position\n\t\tthis.position = position;\n\t\tthis.debugMessage = debugMessage;\n\n\t\tif ( this.debugMessage instanceof Function ) {\n\n\t\t\tthis.debugMessage = function () {};\n\n\t\t}\n\n\t\tthis.id = this.readWord();\n\t\tthis.size = this.readDWord();\n\t\tthis.end = this.offset + this.size;\n\n\t\tif ( this.end > data.byteLength ) {\n\n\t\t\tthis.debugMessage( 'Bad chunk size for chunk at ' + position );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * read a sub cchunk.\n\t *\n\t * @method readChunk\n\t * @return {Chunk | null} next sub chunk\n\t */\n\treadChunk() {\n\n\t\tif ( this.endOfChunk ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\ttry {\n\n\t\t\tconst next = new Chunk( this.data, this.position, this.debugMessage );\n\t\t\tthis.position += next.size;\n\t\t\treturn next;\n\n\t\t}\tcatch ( e ) {\n\n\t\t\tthis.debugMessage( 'Unable to read chunk at ' + this.position );\n\t\t\treturn null;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * return the ID of this chunk as Hex\n\t *\n\t * @method idToString\n\t * @return {String} hex-string of id\n\t */\n\tget hexId() {\n\n\t\treturn this.id.toString( 16 );\n\n\t}\n\n\tget endOfChunk() {\n\n\t\treturn this.position >= this.end;\n\n\t}\n\n\t/**\n\t * Read byte value.\n\t *\n\t * @method readByte\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadByte() {\n\n\t\tconst v = this.data.getUint8( this.position, true );\n\t\tthis.position += 1;\n\t\treturn v;\n\n\t}\n\n\t/**\n\t * Read 32 bit float value.\n\t *\n\t * @method readFloat\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadFloat() {\n\n\t\ttry {\n\n\t\t\tconst v = this.data.getFloat32( this.position, true );\n\t\t\tthis.position += 4;\n\t\t\treturn v;\n\n\t\t}\tcatch ( e ) {\n\n\t\t\tthis.debugMessage( e + ' ' + this.position + ' ' + this.data.byteLength );\n\t\t\treturn 0;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Read 32 bit signed integer value.\n\t *\n\t * @method readInt\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadInt() {\n\n\t\tconst v = this.data.getInt32( this.position, true );\n\t\tthis.position += 4;\n\t\treturn v;\n\n\t}\n\n\t/**\n\t * Read 16 bit signed integer value.\n\t *\n\t * @method readShort\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadShort() {\n\n\t\tconst v = this.data.getInt16( this.position, true );\n\t\tthis.position += 2;\n\t\treturn v;\n\n\t}\n\n\t/**\n\t * Read 64 bit unsigned integer value.\n\t *\n\t * @method readDWord\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadDWord() {\n\n\t\tconst v = this.data.getUint32( this.position, true );\n\t\tthis.position += 4;\n\t\treturn v;\n\n\t}\n\n\t/**\n\t * Read 32 bit unsigned integer value.\n\t *\n\t * @method readWord\n\t * @return {Number} Data read from the dataview.\n\t */\n\treadWord() {\n\n\t\tconst v = this.data.getUint16( this.position, true );\n\t\tthis.position += 2;\n\t\treturn v;\n\n\t}\n\n\t/**\n\t * Read NULL terminated ASCII string value from chunk-pos.\n\t *\n\t * @method readString\n\t * @return {String} Data read from the dataview.\n\t */\n\treadString() {\n\n\t\tlet s = '';\n\t\tlet c = this.readByte();\n\t\twhile ( c ) {\n\n\t\t\ts += String.fromCharCode( c );\n\t\t\tc = this.readByte();\n\n\t\t}\n\n\t\treturn s;\n\n\t}\n\n}\n\n// const NULL_CHUNK = 0x0000;\nconst M3DMAGIC = 0x4D4D;\n// const SMAGIC = 0x2D2D;\n// const LMAGIC = 0x2D3D;\nconst MLIBMAGIC = 0x3DAA;\n// const MATMAGIC = 0x3DFF;\nconst CMAGIC = 0xC23D;\nconst M3D_VERSION = 0x0002;\n// const M3D_KFVERSION = 0x0005;\nconst COLOR_F = 0x0010;\nconst COLOR_24 = 0x0011;\nconst LIN_COLOR_24 = 0x0012;\nconst LIN_COLOR_F = 0x0013;\nconst INT_PERCENTAGE = 0x0030;\nconst FLOAT_PERCENTAGE = 0x0031;\nconst MDATA = 0x3D3D;\nconst MESH_VERSION = 0x3D3E;\nconst MASTER_SCALE = 0x0100;\n// const LO_SHADOW_BIAS = 0x1400;\n// const HI_SHADOW_BIAS = 0x1410;\n// const SHADOW_MAP_SIZE = 0x1420;\n// const SHADOW_SAMPLES = 0x1430;\n// const SHADOW_RANGE = 0x1440;\n// const SHADOW_FILTER = 0x1450;\n// const RAY_BIAS = 0x1460;\n// const O_CONSTS = 0x1500;\n// const AMBIENT_LIGHT = 0x2100;\n// const BIT_MAP = 0x1100;\n// const SOLID_BGND = 0x1200;\n// const V_GRADIENT = 0x1300;\n// const USE_BIT_MAP = 0x1101;\n// const USE_SOLID_BGND = 0x1201;\n// const USE_V_GRADIENT = 0x1301;\n// const FOG = 0x2200;\n// const FOG_BGND = 0x2210;\n// const LAYER_FOG = 0x2302;\n// const DISTANCE_CUE = 0x2300;\n// const DCUE_BGND = 0x2310;\n// const USE_FOG = 0x2201;\n// const USE_LAYER_FOG = 0x2303;\n// const USE_DISTANCE_CUE = 0x2301;\nconst MAT_ENTRY = 0xAFFF;\nconst MAT_NAME = 0xA000;\nconst MAT_AMBIENT = 0xA010;\nconst MAT_DIFFUSE = 0xA020;\nconst MAT_SPECULAR = 0xA030;\nconst MAT_SHININESS = 0xA040;\n// const MAT_SHIN2PCT = 0xA041;\nconst MAT_TRANSPARENCY = 0xA050;\n// const MAT_XPFALL = 0xA052;\n// const MAT_USE_XPFALL = 0xA240;\n// const MAT_REFBLUR = 0xA053;\n// const MAT_SHADING = 0xA100;\n// const MAT_USE_REFBLUR = 0xA250;\n// const MAT_SELF_ILLUM = 0xA084;\nconst MAT_TWO_SIDE = 0xA081;\n// const MAT_DECAL = 0xA082;\nconst MAT_ADDITIVE = 0xA083;\nconst MAT_WIRE = 0xA085;\n// const MAT_FACEMAP = 0xA088;\n// const MAT_TRANSFALLOFF_IN = 0xA08A;\n// const MAT_PHONGSOFT = 0xA08C;\n// const MAT_WIREABS = 0xA08E;\nconst MAT_WIRE_SIZE = 0xA087;\nconst MAT_TEXMAP = 0xA200;\n// const MAT_SXP_TEXT_DATA = 0xA320;\n// const MAT_TEXMASK = 0xA33E;\n// const MAT_SXP_TEXTMASK_DATA = 0xA32A;\n// const MAT_TEX2MAP = 0xA33A;\n// const MAT_SXP_TEXT2_DATA = 0xA321;\n// const MAT_TEX2MASK = 0xA340;\n// const MAT_SXP_TEXT2MASK_DATA = 0xA32C;\nconst MAT_OPACMAP = 0xA210;\n// const MAT_SXP_OPAC_DATA = 0xA322;\n// const MAT_OPACMASK = 0xA342;\n// const MAT_SXP_OPACMASK_DATA = 0xA32E;\nconst MAT_BUMPMAP = 0xA230;\n// const MAT_SXP_BUMP_DATA = 0xA324;\n// const MAT_BUMPMASK = 0xA344;\n// const MAT_SXP_BUMPMASK_DATA = 0xA330;\nconst MAT_SPECMAP = 0xA204;\n// const MAT_SXP_SPEC_DATA = 0xA325;\n// const MAT_SPECMASK = 0xA348;\n// const MAT_SXP_SPECMASK_DATA = 0xA332;\n// const MAT_SHINMAP = 0xA33C;\n// const MAT_SXP_SHIN_DATA = 0xA326;\n// const MAT_SHINMASK = 0xA346;\n// const MAT_SXP_SHINMASK_DATA = 0xA334;\n// const MAT_SELFIMAP = 0xA33D;\n// const MAT_SXP_SELFI_DATA = 0xA328;\n// const MAT_SELFIMASK = 0xA34A;\n// const MAT_SXP_SELFIMASK_DATA = 0xA336;\n// const MAT_REFLMAP = 0xA220;\n// const MAT_REFLMASK = 0xA34C;\n// const MAT_SXP_REFLMASK_DATA = 0xA338;\n// const MAT_ACUBIC = 0xA310;\nconst MAT_MAPNAME = 0xA300;\n// const MAT_MAP_TILING = 0xA351;\n// const MAT_MAP_TEXBLUR = 0xA353;\nconst MAT_MAP_USCALE = 0xA354;\nconst MAT_MAP_VSCALE = 0xA356;\nconst MAT_MAP_UOFFSET = 0xA358;\nconst MAT_MAP_VOFFSET = 0xA35A;\n// const MAT_MAP_ANG = 0xA35C;\n// const MAT_MAP_COL1 = 0xA360;\n// const MAT_MAP_COL2 = 0xA362;\n// const MAT_MAP_RCOL = 0xA364;\n// const MAT_MAP_GCOL = 0xA366;\n// const MAT_MAP_BCOL = 0xA368;\nconst NAMED_OBJECT = 0x4000;\n// const N_DIRECT_LIGHT = 0x4600;\n// const DL_OFF = 0x4620;\n// const DL_OUTER_RANGE = 0x465A;\n// const DL_INNER_RANGE = 0x4659;\n// const DL_MULTIPLIER = 0x465B;\n// const DL_EXCLUDE = 0x4654;\n// const DL_ATTENUATE = 0x4625;\n// const DL_SPOTLIGHT = 0x4610;\n// const DL_SPOT_ROLL = 0x4656;\n// const DL_SHADOWED = 0x4630;\n// const DL_LOCAL_SHADOW2 = 0x4641;\n// const DL_SEE_CONE = 0x4650;\n// const DL_SPOT_RECTANGULAR = 0x4651;\n// const DL_SPOT_ASPECT = 0x4657;\n// const DL_SPOT_PROJECTOR = 0x4653;\n// const DL_SPOT_OVERSHOOT = 0x4652;\n// const DL_RAY_BIAS = 0x4658;\n// const DL_RAYSHAD = 0x4627;\n// const N_CAMERA = 0x4700;\n// const CAM_SEE_CONE = 0x4710;\n// const CAM_RANGES = 0x4720;\n// const OBJ_HIDDEN = 0x4010;\n// const OBJ_VIS_LOFTER = 0x4011;\n// const OBJ_DOESNT_CAST = 0x4012;\n// const OBJ_DONT_RECVSHADOW = 0x4017;\n// const OBJ_MATTE = 0x4013;\n// const OBJ_FAST = 0x4014;\n// const OBJ_PROCEDURAL = 0x4015;\n// const OBJ_FROZEN = 0x4016;\nconst N_TRI_OBJECT = 0x4100;\nconst POINT_ARRAY = 0x4110;\n// const POINT_FLAG_ARRAY = 0x4111;\nconst FACE_ARRAY = 0x4120;\nconst MSH_MAT_GROUP = 0x4130;\n// const SMOOTH_GROUP = 0x4150;\n// const MSH_BOXMAP = 0x4190;\nconst TEX_VERTS = 0x4140;\nconst MESH_MATRIX = 0x4160;\n// const MESH_COLOR = 0x4165;\n// const MESH_TEXTURE_INFO = 0x4170;\n// const KFDATA = 0xB000;\n// const KFHDR = 0xB00A;\n// const KFSEG = 0xB008;\n// const KFCURTIME = 0xB009;\n// const AMBIENT_NODE_TAG = 0xB001;\n// const OBJECT_NODE_TAG = 0xB002;\n// const CAMERA_NODE_TAG = 0xB003;\n// const TARGET_NODE_TAG = 0xB004;\n// const LIGHT_NODE_TAG = 0xB005;\n// const L_TARGET_NODE_TAG = 0xB006;\n// const SPOTLIGHT_NODE_TAG = 0xB007;\n// const NODE_ID = 0xB030;\n// const NODE_HDR = 0xB010;\n// const PIVOT = 0xB013;\n// const INSTANCE_NAME = 0xB011;\n// const MORPH_SMOOTH = 0xB015;\n// const BOUNDBOX = 0xB014;\n// const POS_TRACK_TAG = 0xB020;\n// const COL_TRACK_TAG = 0xB025;\n// const ROT_TRACK_TAG = 0xB021;\n// const SCL_TRACK_TAG = 0xB022;\n// const MORPH_TRACK_TAG = 0xB026;\n// const FOV_TRACK_TAG = 0xB023;\n// const ROLL_TRACK_TAG = 0xB024;\n// const HOT_TRACK_TAG = 0xB027;\n// const FALL_TRACK_TAG = 0xB028;\n// const HIDE_TRACK_TAG = 0xB029;\n// const POLY_2D = 0x5000;\n// const SHAPE_OK = 0x5010;\n// const SHAPE_NOT_OK = 0x5011;\n// const SHAPE_HOOK = 0x5020;\n// const PATH_3D = 0x6000;\n// const PATH_MATRIX = 0x6005;\n// const SHAPE_2D = 0x6010;\n// const M_SCALE = 0x6020;\n// const M_TWIST = 0x6030;\n// const M_TEETER = 0x6040;\n// const M_FIT = 0x6050;\n// const M_BEVEL = 0x6060;\n// const XZ_CURVE = 0x6070;\n// const YZ_CURVE = 0x6080;\n// const INTERPCT = 0x6090;\n// const DEFORM_LIMIT = 0x60A0;\n// const USE_CONTOUR = 0x6100;\n// const USE_TWEEN = 0x6110;\n// const USE_SCALE = 0x6120;\n// const USE_TWIST = 0x6130;\n// const USE_TEETER = 0x6140;\n// const USE_FIT = 0x6150;\n// const USE_BEVEL = 0x6160;\n// const DEFAULT_VIEW = 0x3000;\n// const VIEW_TOP = 0x3010;\n// const VIEW_BOTTOM = 0x3020;\n// const VIEW_LEFT = 0x3030;\n// const VIEW_RIGHT = 0x3040;\n// const VIEW_FRONT = 0x3050;\n// const VIEW_BACK = 0x3060;\n// const VIEW_USER = 0x3070;\n// const VIEW_CAMERA = 0x3080;\n// const VIEW_WINDOW = 0x3090;\n// const VIEWPORT_LAYOUT_OLD = 0x7000;\n// const VIEWPORT_DATA_OLD = 0x7010;\n// const VIEWPORT_LAYOUT = 0x7001;\n// const VIEWPORT_DATA = 0x7011;\n// const VIEWPORT_DATA_3 = 0x7012;\n// const VIEWPORT_SIZE = 0x7020;\n// const NETWORK_VIEW = 0x7030;\n\nexport { TDSLoader };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAyBA,IAAM,YAAN,cAAwB,OAAO;AAAA,EAE9B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,QAAQ;AAEb,SAAK,QAAQ;AAEb,SAAK,YAAY,CAAC;AAClB,SAAK,SAAS,CAAC;AAAA,EAEhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,OAAS,KAAK,SAAS,KAAO,YAAY,eAAgB,GAAI,IAAI,KAAK;AAE7E,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAEhD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,eAAQ,MAAM,MAAO,MAAM,IAAK,CAAE;AAAA,MAEnC,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAO,aAAa,MAAO;AAE1B,SAAK,QAAQ,IAAI,MAAM;AACvB,SAAK,YAAY,CAAC;AAClB,SAAK,SAAS,CAAC;AAEf,SAAK,SAAU,aAAa,IAAK;AAEjC,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAO;AAE/C,WAAK,MAAM,IAAK,KAAK,OAAQ,CAAE,CAAE;AAAA,IAElC;AAEA,WAAO,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAU,aAAa,MAAO;AAE7B,UAAM,OAAO,IAAI,SAAU,WAAY;AACvC,UAAM,QAAQ,IAAI,MAAO,MAAM,GAAG,KAAK,YAAa;AAEpD,QAAK,MAAM,OAAO,aAAa,MAAM,OAAO,UAAU,MAAM,OAAO,UAAW;AAE7E,UAAI,OAAO,MAAM,UAAU;AAE3B,aAAQ,MAAO;AAEd,YAAK,KAAK,OAAO,aAAc;AAE9B,gBAAM,UAAU,KAAK,UAAU;AAC/B,eAAK,aAAc,uBAAuB,OAAQ;AAAA,QAEnD,WAAY,KAAK,OAAO,OAAQ;AAE/B,eAAK,aAAc,MAAM,IAAK;AAAA,QAE/B,OAAO;AAEN,eAAK,aAAc,yBAAyB,KAAK,KAAM;AAAA,QAExD;AAEA,eAAO,MAAM,UAAU;AAAA,MAExB;AAAA,IAED;AAEA,SAAK,aAAc,YAAY,KAAK,OAAO,SAAS,SAAU;AAAA,EAE/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAc,OAAO,MAAO;AAE3B,QAAI,OAAO,MAAM,UAAU;AAE3B,WAAQ,MAAO;AAEd,UAAK,KAAK,OAAO,cAAe;AAE/B,cAAM,UAAU,CAAE,KAAK,UAAU;AACjC,aAAK,aAAc,mBAAmB,OAAQ;AAAA,MAE/C,WAAY,KAAK,OAAO,cAAe;AAEtC,cAAM,QAAQ,KAAK,UAAU;AAC7B,aAAK,aAAc,mBAAmB,KAAM;AAC5C,aAAK,MAAM,MAAM,IAAK,OAAO,OAAO,KAAM;AAAA,MAE3C,WAAY,KAAK,OAAO,cAAe;AAEtC,aAAK,aAAc,cAAe;AAClC,aAAK,gBAAiB,IAAK;AAAA,MAE5B,WAAY,KAAK,OAAO,WAAY;AAEnC,aAAK,aAAc,UAAW;AAC9B,aAAK,kBAAmB,MAAM,IAAK;AAAA,MAEpC,OAAO;AAEN,aAAK,aAAc,0BAA0B,KAAK,KAAM;AAAA,MAEzD;AAEA,aAAO,MAAM,UAAU;AAAA,IAExB;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAiB,OAAQ;AAExB,UAAM,OAAO,MAAM,WAAW;AAE9B,QAAI,OAAO,MAAM,UAAU;AAC3B,WAAQ,MAAO;AAEd,UAAK,KAAK,OAAO,cAAe;AAE/B,cAAM,OAAO,KAAK,SAAU,IAAK;AACjC,aAAK,OAAO;AACZ,aAAK,OAAO,KAAM,IAAK;AAAA,MAExB,OAAO;AAEN,aAAK,aAAc,iCAAiC,KAAK,KAAM;AAAA,MAEhE;AAEA,aAAO,MAAM,UAAW;AAAA,IAEzB;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAmB,OAAO,MAAO;AAEhC,QAAI,OAAO,MAAM,UAAU;AAC3B,UAAM,WAAW,IAAI,kBAAkB;AAEvC,WAAQ,MAAO;AAEd,UAAK,KAAK,OAAO,UAAW;AAE3B,iBAAS,OAAO,KAAK,WAAW;AAChC,aAAK,aAAc,cAAc,SAAS,IAAK;AAAA,MAEhD,WAAY,KAAK,OAAO,UAAW;AAElC,aAAK,aAAc,cAAe;AAClC,iBAAS,YAAY;AAAA,MAEtB,WAAY,KAAK,OAAO,eAAgB;AAEvC,cAAM,QAAQ,KAAK,SAAS;AAC5B,iBAAS,qBAAqB;AAC9B,aAAK,aAAc,6BAA6B,KAAM;AAAA,MAEvD,WAAY,KAAK,OAAO,cAAe;AAEtC,iBAAS,OAAO;AAChB,aAAK,aAAc,gBAAiB;AAAA,MAErC,WAAY,KAAK,OAAO,cAAe;AAEtC,aAAK,aAAc,sBAAuB;AAC1C,iBAAS,WAAW;AAAA,MAErB,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,kBAAmB;AACtC,iBAAS,QAAQ,KAAK,UAAW,IAAK;AAAA,MAEvC,WAAY,KAAK,OAAO,cAAe;AAEtC,aAAK,aAAc,mBAAoB;AACvC,iBAAS,WAAW,KAAK,UAAW,IAAK;AAAA,MAE1C,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,kBAAmB;AACtC,iBAAS,QAAQ,KAAK,UAAW,IAAK;AAAA,MAEvC,WAAY,KAAK,OAAO,eAAgB;AAEvC,cAAM,YAAY,KAAK,eAAgB,IAAK;AAC5C,iBAAS,YAAY,YAAY;AACjC,aAAK,aAAc,oBAAoB,SAAU;AAAA,MAElD,WAAY,KAAK,OAAO,kBAAmB;AAE1C,cAAM,eAAe,KAAK,eAAgB,IAAK;AAC/C,iBAAS,UAAU,IAAI;AACvB,aAAK,aAAc,sBAAsB,YAAa;AACtD,iBAAS,cAAc,SAAS,UAAU,IAAI,OAAO;AAAA,MAEtD,WAAY,KAAK,OAAO,YAAa;AAEpC,aAAK,aAAc,aAAc;AACjC,iBAAS,MAAM,KAAK,QAAS,MAAM,IAAK;AAAA,MAEzC,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,YAAa;AAChC,iBAAS,UAAU,KAAK,QAAS,MAAM,IAAK;AAAA,MAE7C,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,eAAgB;AACnC,iBAAS,WAAW,KAAK,QAAS,MAAM,IAAK;AAAA,MAE9C,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,gBAAiB;AACpC,iBAAS,cAAc,KAAK,QAAS,MAAM,IAAK;AAAA,MAEjD,OAAO;AAEN,aAAK,aAAc,gCAAgC,KAAK,KAAM;AAAA,MAE/D;AAEA,aAAO,MAAM,UAAU;AAAA,IAExB;AAEA,SAAK,UAAW,SAAS,IAAK,IAAI;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAU,OAAQ;AAEjB,QAAI,OAAO,MAAM,UAAW;AAE5B,UAAM,WAAW,IAAI,eAAe;AAEpC,UAAM,WAAW,IAAI,kBAAkB;AACvC,UAAM,OAAO,IAAI,KAAM,UAAU,QAAS;AAC1C,SAAK,OAAO;AAEZ,WAAQ,MAAO;AAEd,UAAK,KAAK,OAAO,aAAc;AAE9B,cAAM,SAAS,KAAK,SAAU;AAE9B,aAAK,aAAc,gBAAgB,MAAO;AAI1C,cAAM,WAAW,CAAC;AAElB,iBAAU,IAAI,GAAG,IAAI,QAAQ,KAAQ;AAEpC,mBAAS,KAAM,KAAK,UAAW,CAAE;AACjC,mBAAS,KAAM,KAAK,UAAW,CAAE;AACjC,mBAAS,KAAM,KAAK,UAAW,CAAE;AAAA,QAElC;AAEA,iBAAS,aAAc,YAAY,IAAI,uBAAwB,UAAU,CAAE,CAAE;AAAA,MAE9E,WAAY,KAAK,OAAO,YAAa;AAEpC,aAAK,cAAe,MAAM,IAAK;AAAA,MAEhC,WAAY,KAAK,OAAO,WAAY;AAEnC,cAAM,SAAS,KAAK,SAAU;AAE9B,aAAK,aAAc,YAAY,MAAO;AAItC,cAAM,MAAM,CAAC;AAEb,iBAAU,IAAI,GAAG,IAAI,QAAQ,KAAO;AAEnC,cAAI,KAAM,KAAK,UAAW,CAAE;AAC5B,cAAI,KAAM,KAAK,UAAW,CAAE;AAAA,QAE7B;AAEA,iBAAS,aAAc,MAAM,IAAI,uBAAwB,KAAK,CAAE,CAAE;AAAA,MAGnE,WAAY,KAAK,OAAO,aAAc;AAErC,aAAK,aAAc,gCAAiC;AAEpD,cAAM,SAAS,CAAC;AAChB,iBAAU,IAAI,GAAG,IAAI,IAAI,KAAO;AAE/B,iBAAQ,CAAE,IAAI,KAAK,UAAW;AAAA,QAE/B;AAEA,cAAM,SAAS,IAAI,QAAQ;AAG3B,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AAGjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,EAAG;AAGlC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,CAAE,IAAI,OAAQ,CAAE;AACjC,eAAO,SAAU,EAAG,IAAI,OAAQ,CAAE;AAClC,eAAO,SAAU,EAAG,IAAI,OAAQ,EAAG;AAGnC,eAAO,SAAU,EAAG,IAAI;AACxB,eAAO,SAAU,EAAG,IAAI;AACxB,eAAO,SAAU,EAAG,IAAI;AACxB,eAAO,SAAU,EAAG,IAAI;AAExB,eAAO,UAAU;AAEjB,cAAM,UAAU,IAAI,QAAQ;AAC5B,gBAAQ,KAAM,MAAO,EAAE,OAAO;AAC9B,iBAAS,aAAc,OAAQ;AAE/B,eAAO,UAAW,KAAK,UAAU,KAAK,YAAY,KAAK,KAAM;AAAA,MAE9D,OAAO;AAEN,aAAK,aAAc,4BAA4B,KAAK,KAAM;AAAA,MAE3D;AAEA,aAAO,MAAM,UAAW;AAAA,IAEzB;AAEA,aAAS,qBAAqB;AAE9B,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAe,OAAO,MAAO;AAE5B,UAAM,QAAQ,MAAM,SAAU;AAE9B,SAAK,aAAc,eAAe,KAAM;AAExC,UAAM,QAAQ,CAAC;AAEf,aAAU,IAAI,GAAG,IAAI,OAAO,EAAG,GAAI;AAElC,YAAM,KAAM,MAAM,SAAU,GAAG,MAAM,SAAU,GAAG,MAAM,SAAU,CAAE;AAEpE,YAAM,SAAU;AAAA,IAEjB;AAEA,SAAK,SAAS,SAAU,KAAM;AAI9B,QAAI,gBAAgB;AACpB,QAAI,QAAQ;AAEZ,WAAQ,CAAE,MAAM,YAAa;AAE5B,YAAM,WAAW,MAAM,UAAW;AAElC,UAAK,SAAS,OAAO,eAAgB;AAEpC,aAAK,aAAc,sBAAuB;AAE1C,cAAM,QAAQ,KAAK,kBAAmB,QAAS;AAC/C,cAAM,QAAQ,MAAM,MAAM,SAAS;AAEnC,aAAK,SAAS,SAAU,OAAO,OAAO,aAAc;AAEpD,iBAAS;AACT;AAEA,cAAM,WAAW,KAAK,UAAW,MAAM,IAAK;AAE5C,YAAK,MAAM,QAAS,KAAK,QAAS,MAAM,MAAQ,MAAK,WAAW,CAAC;AAEjE,YAAK,aAAa,QAAY;AAE7B,eAAK,SAAS,KAAM,QAAS;AAAA,QAE9B;AAAA,MAED,OAAO;AAEN,aAAK,aAAc,qCAAqC,SAAS,KAAM;AAAA,MAExE;AAAA,IAED;AAEA,QAAK,KAAK,SAAS,WAAW,EAAI,MAAK,WAAW,KAAK,SAAU,CAAE;AAAA,EAEpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAS,OAAO,MAAO;AAEtB,QAAI,OAAO,MAAM,UAAW;AAC5B,QAAI,UAAU,CAAC;AAEf,UAAM,SAAS,IAAI,cAAe,KAAK,OAAQ;AAC/C,WAAO,QAAS,KAAK,gBAAgB,IAAK,EAAE,eAAgB,KAAK,WAAY;AAE7E,WAAQ,MAAO;AAEd,UAAK,KAAK,OAAO,aAAc;AAE9B,cAAM,OAAO,KAAK,WAAW;AAC7B,kBAAU,OAAO,KAAM,IAAK;AAE5B,aAAK,aAAc,iBAAiB,OAAO,IAAK;AAAA,MAEjD,WAAY,KAAK,OAAO,iBAAkB;AAEzC,gBAAQ,OAAO,IAAI,KAAK,UAAW;AACnC,aAAK,aAAc,oBAAoB,QAAQ,OAAO,CAAE;AAAA,MAEzD,WAAY,KAAK,OAAO,iBAAkB;AAEzC,gBAAQ,OAAO,IAAI,KAAK,UAAW;AACnC,aAAK,aAAc,oBAAoB,QAAQ,OAAO,CAAE;AAAA,MAEzD,WAAY,KAAK,OAAO,gBAAiB;AAExC,gBAAQ,OAAO,IAAI,KAAK,UAAW;AACnC,aAAK,aAAc,oBAAoB,QAAQ,OAAO,CAAE;AAAA,MAEzD,WAAY,KAAK,OAAO,gBAAiB;AAExC,gBAAQ,OAAO,IAAI,KAAK,UAAW;AACnC,aAAK,aAAc,oBAAoB,QAAQ,OAAO,CAAE;AAAA,MAEzD,OAAO;AAEN,aAAK,aAAc,8BAA8B,KAAK,KAAM;AAAA,MAE7D;AAEA,aAAO,MAAM,UAAW;AAAA,IAEzB;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAmB,OAAQ;AAE1B,UAAM,OAAO,MAAM,WAAW;AAC9B,UAAM,WAAW,MAAM,SAAS;AAEhC,SAAK,aAAc,oBAAoB,IAAK;AAC5C,SAAK,aAAc,qBAAqB,QAAS;AAEjD,UAAM,QAAQ,CAAC;AACf,aAAU,IAAI,GAAG,IAAI,UAAU,EAAG,GAAI;AAErC,YAAM,KAAM,MAAM,SAAU,CAAE;AAAA,IAE/B;AAEA,WAAO,EAAE,MAAY,MAAa;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAW,OAAQ;AAElB,UAAM,WAAW,MAAM,UAAW;AAClC,UAAM,QAAQ,IAAI,MAAM;AAExB,QAAK,SAAS,OAAO,YAAY,SAAS,OAAO,cAAe;AAE/D,YAAM,IAAI,SAAS,SAAU;AAC7B,YAAM,IAAI,SAAS,SAAU;AAC7B,YAAM,IAAI,SAAS,SAAU;AAE7B,YAAM,OAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,GAAI;AAExC,WAAK,aAAc,kBAAkB,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAE;AAAA,IAEhF,WAAY,SAAS,OAAO,WAAW,SAAS,OAAO,aAAc;AAEpE,YAAM,IAAI,SAAS,UAAW;AAC9B,YAAM,IAAI,SAAS,UAAW;AAC9B,YAAM,IAAI,SAAS,UAAW;AAE9B,YAAM,OAAQ,GAAG,GAAG,CAAE;AAEtB,WAAK,aAAc,kBAAkB,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAE;AAAA,IAEhF,OAAO;AAEN,WAAK,aAAc,gCAAgC,SAAS,KAAM;AAAA,IAEnE;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAgB,OAAQ;AAEvB,UAAM,WAAW,MAAM,UAAW;AAElC,YAAS,SAAS,IAAK;AAAA,MAEtB,KAAK;AACJ,eAAS,SAAS,UAAW,IAAI;AACjC;AAAA,MAED,KAAK;AACJ,eAAO,SAAS,UAAW;AAC3B;AAAA,MAED;AACC,aAAK,aAAc,qCAAqC,SAAS,KAAM;AACvE,eAAO;AAAA,IAET;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAc,SAAU;AAEvB,QAAK,KAAK,OAAQ;AAEjB,cAAQ,IAAK,OAAQ;AAAA,IAEtB;AAAA,EAED;AAED;AAIA,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUX,YAAa,MAAM,UAAU,cAAe;AAE3C,SAAK,OAAO;AAEZ,SAAK,SAAS;AAEd,SAAK,WAAW;AAChB,SAAK,eAAe;AAEpB,QAAK,KAAK,wBAAwB,UAAW;AAE5C,WAAK,eAAe,WAAY;AAAA,MAAC;AAAA,IAElC;AAEA,SAAK,KAAK,KAAK,SAAS;AACxB,SAAK,OAAO,KAAK,UAAU;AAC3B,SAAK,MAAM,KAAK,SAAS,KAAK;AAE9B,QAAK,KAAK,MAAM,KAAK,YAAa;AAEjC,WAAK,aAAc,iCAAiC,QAAS;AAAA,IAE9D;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AAEX,QAAK,KAAK,YAAa;AAEtB,aAAO;AAAA,IAER;AAEA,QAAI;AAEH,YAAM,OAAO,IAAI,OAAO,KAAK,MAAM,KAAK,UAAU,KAAK,YAAa;AACpE,WAAK,YAAY,KAAK;AACtB,aAAO;AAAA,IAER,SAAU,GAAI;AAEb,WAAK,aAAc,6BAA6B,KAAK,QAAS;AAC9D,aAAO;AAAA,IAER;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,QAAQ;AAEX,WAAO,KAAK,GAAG,SAAU,EAAG;AAAA,EAE7B;AAAA,EAEA,IAAI,aAAa;AAEhB,WAAO,KAAK,YAAY,KAAK;AAAA,EAE9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AAEV,UAAM,IAAI,KAAK,KAAK,SAAU,KAAK,UAAU,IAAK;AAClD,SAAK,YAAY;AACjB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AAEX,QAAI;AAEH,YAAM,IAAI,KAAK,KAAK,WAAY,KAAK,UAAU,IAAK;AACpD,WAAK,YAAY;AACjB,aAAO;AAAA,IAER,SAAU,GAAI;AAEb,WAAK,aAAc,IAAI,MAAM,KAAK,WAAW,MAAM,KAAK,KAAK,UAAW;AACxE,aAAO;AAAA,IAER;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AAET,UAAM,IAAI,KAAK,KAAK,SAAU,KAAK,UAAU,IAAK;AAClD,SAAK,YAAY;AACjB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AAEX,UAAM,IAAI,KAAK,KAAK,SAAU,KAAK,UAAU,IAAK;AAClD,SAAK,YAAY;AACjB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AAEX,UAAM,IAAI,KAAK,KAAK,UAAW,KAAK,UAAU,IAAK;AACnD,SAAK,YAAY;AACjB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AAEV,UAAM,IAAI,KAAK,KAAK,UAAW,KAAK,UAAU,IAAK;AACnD,SAAK,YAAY;AACjB,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AAEZ,QAAI,IAAI;AACR,QAAI,IAAI,KAAK,SAAS;AACtB,WAAQ,GAAI;AAEX,WAAK,OAAO,aAAc,CAAE;AAC5B,UAAI,KAAK,SAAS;AAAA,IAEnB;AAEA,WAAO;AAAA,EAER;AAED;AAGA,IAAM,WAAW;AAGjB,IAAM,YAAY;AAElB,IAAM,SAAS;AACf,IAAM,cAAc;AAEpB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,eAAe;AACrB,IAAM,cAAc;AACpB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,QAAQ;AACd,IAAM,eAAe;AACrB,IAAM,eAAe;AAwBrB,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AAEtB,IAAM,mBAAmB;AAOzB,IAAM,eAAe;AAErB,IAAM,eAAe;AACrB,IAAM,WAAW;AAKjB,IAAM,gBAAgB;AACtB,IAAM,aAAa;AAQnB,IAAM,cAAc;AAIpB,IAAM,cAAc;AAIpB,IAAM,cAAc;AAgBpB,IAAM,cAAc;AAGpB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AAOxB,IAAM,eAAe;AA8BrB,IAAM,eAAe;AACrB,IAAM,cAAc;AAEpB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AAGtB,IAAM,YAAY;AAClB,IAAM,cAAc;", "names": []}